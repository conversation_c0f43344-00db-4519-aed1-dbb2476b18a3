"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  FiBarChart2,
  FiPieChart,
  FiTrendingUp,
  FiUsers,
  FiMail,
  FiCalendar,
  FiDownload,
  FiRefreshCw,
  FiActivity,
} from "react-icons/fi";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { supabase } from "@/lib/supabase";
import { getValidatedUser } from "@/lib/userValidation";

export default function AdminReports() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState({
    users: {
      total: 0,
      active: 0,
      subscribers: 0,
      admins: 0,
      monthlyGrowth: [],
    },
    invitations: {
      total: 0,
      active: 0,
      monthlyStats: [],
      statusBreakdown: [],
    },
    guests: {
      total: 0,
      accepted: 0,
      declined: 0,
      attended: 0,
      responseRates: [],
    },
    revenue: {
      monthly: [],
      total: 0,
      average: 0,
    },
  });

  useEffect(() => {
    checkUserAndLoadData();
  }, []);

  const checkUserAndLoadData = async () => {
    try {
      setLoading(true);
      
      const validatedUser = await getValidatedUser();
      if (!validatedUser || validatedUser.role !== "admin") {
        router.push("/login");
        return;
      }

      await loadReportData();
    } catch (error) {
      console.error("Error loading report data:", error);
      toast.error("خطأ في تحميل بيانات التقارير");
    } finally {
      setLoading(false);
    }
  };

  const loadReportData = async () => {
    try {
      // جلب بيانات المستخدمين
      const { data: users } = await supabase
        .from("users")
        .select("id, role, is_active, created_at");

      // جلب بيانات الدعوات
      const { data: invitations } = await supabase
        .from("invitations")
        .select("id, status, created_at, event_date");

      // جلب بيانات الضيوف
      const { data: guests } = await supabase
        .from("guests")
        .select("id, status, attended, created_at");

      // جلب بيانات الاشتراكات
      const { data: subscriptions } = await supabase
        .from("subscriptions")
        .select("id, monthly_fee, currency, start_date, status");

      // معالجة البيانات
      const processedData = processReportData(users, invitations, guests, subscriptions);
      setReportData(processedData);

    } catch (error) {
      console.error("Error loading report data:", error);
      throw error;
    }
  };

  const processReportData = (users = [], invitations = [], guests = [], subscriptions = []) => {
    // معالجة بيانات المستخدمين
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.is_active).length;
    const subscribers = users.filter(u => u.role === 'subscriber').length;
    const admins = users.filter(u => u.role === 'admin').length;

    // نمو المستخدمين الشهري (آخر 6 أشهر)
    const monthlyGrowth = getMonthlyGrowth(users);

    // معالجة بيانات الدعوات
    const totalInvitations = invitations.length;
    const activeInvitations = invitations.filter(i => i.status === 'active' || i.status === 'published').length;
    const invitationStats = getMonthlyStats(invitations);
    const statusBreakdown = getStatusBreakdown(invitations);

    // معالجة بيانات الضيوف
    const totalGuests = guests.length;
    const acceptedGuests = guests.filter(g => g.status === 'accepted' || g.status === 'confirmed').length;
    const declinedGuests = guests.filter(g => g.status === 'declined').length;
    const attendedGuests = guests.filter(g => g.attended === true).length;
    const responseRates = getResponseRates(guests);

    // معالجة بيانات الإيرادات
    const revenueData = getRevenueData(subscriptions);

    return {
      users: {
        total: totalUsers,
        active: activeUsers,
        subscribers: subscribers,
        admins: admins,
        monthlyGrowth: monthlyGrowth,
      },
      invitations: {
        total: totalInvitations,
        active: activeInvitations,
        monthlyStats: invitationStats,
        statusBreakdown: statusBreakdown,
      },
      guests: {
        total: totalGuests,
        accepted: acceptedGuests,
        declined: declinedGuests,
        attended: attendedGuests,
        responseRates: responseRates,
      },
      revenue: revenueData,
    };
  };

  const getMonthlyGrowth = (users) => {
    const months = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7);
      const count = users.filter(u => u.created_at?.startsWith(monthKey)).length;
      months.push({
        month: date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' }),
        count: count,
      });
    }
    return months;
  };

  const getMonthlyStats = (invitations) => {
    const months = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7);
      const count = invitations.filter(inv => inv.created_at?.startsWith(monthKey)).length;
      months.push({
        month: date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' }),
        count: count,
      });
    }
    return months;
  };

  const getStatusBreakdown = (invitations) => {
    const statusCounts = {};
    invitations.forEach(inv => {
      statusCounts[inv.status] = (statusCounts[inv.status] || 0) + 1;
    });
    
    return Object.entries(statusCounts).map(([status, count]) => ({
      status: status,
      count: count,
      percentage: invitations.length > 0 ? Math.round((count / invitations.length) * 100) : 0,
    }));
  };

  const getResponseRates = (guests) => {
    const months = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7);
      const monthGuests = guests.filter(g => g.created_at?.startsWith(monthKey));
      const accepted = monthGuests.filter(g => g.status === 'accepted' || g.status === 'confirmed').length;
      const total = monthGuests.length;
      
      months.push({
        month: date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' }),
        rate: total > 0 ? Math.round((accepted / total) * 100) : 0,
      });
    }
    return months;
  };

  const getRevenueData = (subscriptions) => {
    const activeSubscriptions = subscriptions.filter(s => s.status === 'active');
    const totalRevenue = activeSubscriptions.reduce((sum, s) => sum + parseFloat(s.monthly_fee || 0), 0);
    const averageRevenue = activeSubscriptions.length > 0 ? totalRevenue / activeSubscriptions.length : 0;

    const monthly = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7);
      const monthSubscriptions = activeSubscriptions.filter(s => s.start_date?.startsWith(monthKey));
      const monthRevenue = monthSubscriptions.reduce((sum, s) => sum + parseFloat(s.monthly_fee || 0), 0);
      
      monthly.push({
        month: date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' }),
        revenue: monthRevenue,
      });
    }

    return {
      monthly: monthly,
      total: totalRevenue,
      average: averageRevenue,
    };
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">جاري تحميل التقارير...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100" dir="rtl">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-800 mb-2">التقارير والإحصائيات</h1>
            <p className="text-gray-600">رسوم بيانية وتحليلات شاملة للنظام</p>
          </div>
          <div className="flex gap-2">
            <button
              onClick={checkUserAndLoadData}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <FiRefreshCw className="h-4 w-4" />
              تحديث
            </button>
            <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
              <FiDownload className="h-4 w-4" />
              تصدير
            </button>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">إجمالي المستخدمين</p>
                <p className="text-3xl font-bold text-blue-600">{reportData.users.total}</p>
                <p className="text-sm text-green-600">+{reportData.users.monthlyGrowth.slice(-1)[0]?.count || 0} هذا الشهر</p>
              </div>
              <FiUsers className="h-12 w-12 text-blue-500" />
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">إجمالي الدعوات</p>
                <p className="text-3xl font-bold text-green-600">{reportData.invitations.total}</p>
                <p className="text-sm text-blue-600">{reportData.invitations.active} نشطة</p>
              </div>
              <FiMail className="h-12 w-12 text-green-500" />
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">إجمالي الضيوف</p>
                <p className="text-3xl font-bold text-purple-600">{reportData.guests.total}</p>
                <p className="text-sm text-green-600">{reportData.guests.accepted} مقبول</p>
              </div>
              <FiActivity className="h-12 w-12 text-purple-500" />
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">الدخل الشهري</p>
                <p className="text-3xl font-bold text-orange-600">{formatCurrency(reportData.revenue.total)}</p>
                <p className="text-sm text-blue-600">متوسط {formatCurrency(reportData.revenue.average)}</p>
              </div>
              <FiTrendingUp className="h-12 w-12 text-orange-500" />
            </div>
          </motion.div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* User Growth Chart */}
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <FiTrendingUp className="h-5 w-5 text-blue-500" />
              نمو المستخدمين الشهري
            </h3>
            <div className="h-64 flex items-end justify-between gap-2">
              {reportData.users.monthlyGrowth.map((month, index) => (
                <div key={index} className="flex flex-col items-center flex-1">
                  <div
                    className="bg-gradient-to-t from-blue-500 to-blue-300 rounded-t w-full transition-all duration-500 hover:from-blue-600 hover:to-blue-400"
                    style={{
                      height: `${Math.max((month.count / Math.max(...reportData.users.monthlyGrowth.map(m => m.count))) * 200, 10)}px`
                    }}
                  ></div>
                  <p className="text-xs text-gray-600 mt-2 text-center">{month.month}</p>
                  <p className="text-sm font-bold text-gray-800">{month.count}</p>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Invitation Stats Chart */}
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <FiMail className="h-5 w-5 text-green-500" />
              إحصائيات الدعوات الشهرية
            </h3>
            <div className="h-64 flex items-end justify-between gap-2">
              {reportData.invitations.monthlyStats.map((month, index) => (
                <div key={index} className="flex flex-col items-center flex-1">
                  <div
                    className="bg-gradient-to-t from-green-500 to-green-300 rounded-t w-full transition-all duration-500 hover:from-green-600 hover:to-green-400"
                    style={{
                      height: `${Math.max((month.count / Math.max(...reportData.invitations.monthlyStats.map(m => m.count))) * 200, 10)}px`
                    }}
                  ></div>
                  <p className="text-xs text-gray-600 mt-2 text-center">{month.month}</p>
                  <p className="text-sm font-bold text-gray-800">{month.count}</p>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Response Rates Chart */}
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <FiPieChart className="h-5 w-5 text-purple-500" />
              معدلات الاستجابة الشهرية
            </h3>
            <div className="h-64 flex items-end justify-between gap-2">
              {reportData.guests.responseRates.map((month, index) => (
                <div key={index} className="flex flex-col items-center flex-1">
                  <div
                    className="bg-gradient-to-t from-purple-500 to-purple-300 rounded-t w-full transition-all duration-500 hover:from-purple-600 hover:to-purple-400"
                    style={{
                      height: `${Math.max((month.rate / 100) * 200, 10)}px`
                    }}
                  ></div>
                  <p className="text-xs text-gray-600 mt-2 text-center">{month.month}</p>
                  <p className="text-sm font-bold text-gray-800">{month.rate}%</p>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Revenue Chart */}
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <FiBarChart2 className="h-5 w-5 text-orange-500" />
              الإيرادات الشهرية
            </h3>
            <div className="h-64 flex items-end justify-between gap-2">
              {reportData.revenue.monthly.map((month, index) => (
                <div key={index} className="flex flex-col items-center flex-1">
                  <div
                    className="bg-gradient-to-t from-orange-500 to-orange-300 rounded-t w-full transition-all duration-500 hover:from-orange-600 hover:to-orange-400"
                    style={{
                      height: `${Math.max((month.revenue / Math.max(...reportData.revenue.monthly.map(m => m.revenue))) * 200, 10)}px`
                    }}
                  ></div>
                  <p className="text-xs text-gray-600 mt-2 text-center">{month.month}</p>
                  <p className="text-xs font-bold text-gray-800">{formatCurrency(month.revenue)}</p>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Status Breakdown */}
        <motion.div
          className="bg-white rounded-2xl p-6 shadow-lg"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
            <FiPieChart className="h-5 w-5 text-indigo-500" />
            توزيع حالات الدعوات
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {reportData.invitations.statusBreakdown.map((item, index) => (
              <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-gray-800">{item.count}</p>
                <p className="text-sm text-gray-600">{item.status}</p>
                <p className="text-xs text-blue-600">{item.percentage}%</p>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      <ToastContainer position="top-right" />
    </div>
  );
}
