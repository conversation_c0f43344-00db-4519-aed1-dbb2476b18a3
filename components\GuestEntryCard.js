"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiCalendar, 
  FiMapPin, 
  FiClock, 
  FiUser, 
  FiPhone,
  FiDownload,
  FiShare2,
  FiCheck
} from 'react-icons/fi';
import { generateQRCodeImage, formatGuestCardData } from '@/lib/qrCodeHelpers';

const GuestEntryCard = ({ guestData, invitationData, onClose }) => {
  const [qrCodeImage, setQrCodeImage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [cardData, setCardData] = useState(null);

  useEffect(() => {
    if (guestData && invitationData) {
      initializeCard();
    }
  }, [guestData, invitationData]);

  const initializeCard = async () => {
    try {
      setLoading(true);
      
      // تنسيق بيانات البطاقة
      const formattedData = formatGuestCardData(guestData, invitationData);
      setCardData(formattedData);
      
      // إنتاج صورة QR Code
      if (guestData.qr_code) {
        const qrImage = await generateQRCodeImage(guestData.qr_code, {
          width: 200,
          color: {
            dark: '#1a365d',
            light: '#ffffff'
          }
        });
        setQrCodeImage(qrImage);
      }
    } catch (error) {
      console.error('Error initializing card:', error);
    } finally {
      setLoading(false);
    }
  };

  const downloadCard = () => {
    if (!cardData) return;
    
    // إنشاء canvas لرسم البطاقة
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // تحديد أبعاد البطاقة
    canvas.width = 600;
    canvas.height = 800;
    
    // خلفية البطاقة
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // إضافة النصوص والتفاصيل
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('بطاقة دخول', canvas.width / 2, 60);
    
    // معلومات الحدث
    ctx.font = '24px Arial';
    ctx.fillText(cardData.eventTitle, canvas.width / 2, 120);
    
    ctx.font = '18px Arial';
    ctx.fillText(`📅 ${cardData.eventDate}`, canvas.width / 2, 160);
    ctx.fillText(`🕐 ${cardData.eventTime}`, canvas.width / 2, 190);
    ctx.fillText(`📍 ${cardData.eventLocation}`, canvas.width / 2, 220);
    
    // معلومات الضيف
    ctx.fillText(`👤 ${cardData.guestName}`, canvas.width / 2, 280);
    ctx.fillText(`📱 ${cardData.guestPhone}`, canvas.width / 2, 310);
    
    // إضافة QR Code إذا كان متوفراً
    if (qrCodeImage) {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, (canvas.width - 200) / 2, 350, 200, 200);
        
        // تحميل الصورة
        const link = document.createElement('a');
        link.download = `entry-card-${cardData.guestName}.png`;
        link.href = canvas.toDataURL();
        link.click();
      };
      img.src = qrCodeImage;
    }
  };

  const shareCard = async () => {
    if (navigator.share && qrCodeImage) {
      try {
        // تحويل QR Code إلى blob
        const response = await fetch(qrCodeImage);
        const blob = await response.blob();
        const file = new File([blob], 'entry-qr.png', { type: 'image/png' });
        
        await navigator.share({
          title: `بطاقة دخول - ${cardData?.eventTitle}`,
          text: `بطاقة دخول للضيف ${cardData?.guestName}`,
          files: [file]
        });
      } catch (error) {
        console.error('Error sharing:', error);
        // fallback للنسخ
        copyQRCode();
      }
    } else {
      copyQRCode();
    }
  };

  const copyQRCode = () => {
    if (guestData?.qr_code) {
      navigator.clipboard.writeText(guestData.qr_code);
      alert('تم نسخ رمز QR إلى الحافظة');
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-center mt-4">جاري إنتاج بطاقة الدخول...</p>
        </div>
      </div>
    );
  }

  if (!cardData) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-t-2xl">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">🎫 بطاقة الدخول</h2>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-2xl"
            >
              ×
            </button>
          </div>
          <div className="flex items-center mt-2">
            <FiCheck className="text-green-300 ml-2" />
            <span className="text-green-100">تم تأكيد الحضور</span>
          </div>
        </div>

        {/* Card Content */}
        <div className="p-6">
          {/* Event Info */}
          <div className="text-center mb-6">
            <h3 className="text-xl font-bold text-gray-800 mb-4">
              {cardData.eventTitle}
            </h3>
            
            <div className="space-y-3 text-gray-600">
              <div className="flex items-center justify-center">
                <FiCalendar className="ml-2 text-blue-500" />
                <span>{cardData.eventDate}</span>
              </div>
              
              <div className="flex items-center justify-center">
                <FiClock className="ml-2 text-green-500" />
                <span>{cardData.eventTime}</span>
              </div>
              
              <div className="flex items-center justify-center">
                <FiMapPin className="ml-2 text-red-500" />
                <span>{cardData.eventLocation}</span>
              </div>
            </div>
          </div>

          {/* Guest Info */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-gray-800 mb-3">معلومات الضيف</h4>
            <div className="space-y-2">
              <div className="flex items-center">
                <FiUser className="ml-2 text-purple-500" />
                <span className="font-medium">{cardData.guestName}</span>
              </div>
              <div className="flex items-center">
                <FiPhone className="ml-2 text-blue-500" />
                <span>{cardData.guestPhone}</span>
              </div>
            </div>
          </div>

          {/* QR Code */}
          {qrCodeImage && (
            <div className="text-center mb-6">
              <h4 className="font-semibold text-gray-800 mb-3">رمز الدخول</h4>
              <div className="bg-white p-4 rounded-lg border-2 border-dashed border-gray-300 inline-block">
                <img 
                  src={qrCodeImage} 
                  alt="QR Code" 
                  className="w-48 h-48 mx-auto"
                />
              </div>
              <p className="text-sm text-gray-500 mt-2">
                اعرض هذا الرمز عند الدخول
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3 space-x-reverse">
            <button
              onClick={downloadCard}
              className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <FiDownload className="ml-2" />
              تحميل البطاقة
            </button>
            
            <button
              onClick={shareCard}
              className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center"
            >
              <FiShare2 className="ml-2" />
              مشاركة
            </button>
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h5 className="font-semibold text-yellow-800 mb-2">تعليمات مهمة:</h5>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• احتفظ بهذه البطاقة في هاتفك</li>
              <li>• اعرض رمز QR عند الدخول</li>
              <li>• تأكد من وضوح الرمز عند المسح</li>
              <li>• يمكنك طباعة البطاقة إذا لزم الأمر</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default GuestEntryCard;
