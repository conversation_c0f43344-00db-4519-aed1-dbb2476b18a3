# إعداد قاعدة البيانات - نظام الدعوات

## الملفات المطلوبة:
- ✅ `database/setup-complete.sql` - الملف الوحيد المطلوب

## التشغيل:
```sql
-- في Supabase SQL Editor
\i database/setup-complete.sql
```

## ما يحتويه الملف:
1. **إنشاء جميع الجداول** (users, invitations, guests, notifications, templates, system_settings)
2. **إضافة الفهارس** لتحسين الأداء
3. **إضافة التعليقات** التوضيحية
4. **إنشاء الدوال والـ Triggers** المطلوبة
5. **إعدادات الأمان** (RLS)
6. **البيانات الأساسية** (مدير افتراضي + قالب)

## الجداول الرئيسية:
- `users` - المستخدمين والإداريين
- `invitations` - الدعوات
- `guests` - المدعوين (مع أعمدة status و attended)
- `notifications` - الإشعارات
- `templates` - قوالب الدعوات
- `system_settings` - إعدادات النظام

## الفرق بين الحالات:
- **status** (رد الضيف): pending/accepted/declined - يحدده الضيف
- **attended** (الحضور الفعلي): true/false - يحدده صاحب الدعوة

## في حالة المشاكل:
1. تأكد من تشغيل الملف في Supabase SQL Editor
2. تحقق من console المتصفح للأخطاء
3. أعد تحميل الصفحة بعد تشغيل SQL

## ملاحظة:
تم حذف جميع ملفات SQL المكررة والاحتفاظ بملف واحد شامل فقط.
