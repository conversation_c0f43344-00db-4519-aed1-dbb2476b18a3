# دليل البدء السريع - نظام إدارة الدعوات

## المشكلة: "بيانات الدخول غير صحيحة"

إذا كنت تواجه هذا الخطأ، فهذا يعني أن قاعدة البيانات غير مُعدة بشكل صحيح.

## الحل السريع (5 دقائق)

### الخطوة 1: تشغيل الخادم
```bash
npm run dev
```

### الخطوة 2: اختبار الاتصال
اذهب إلى: `http://localhost:3000/test-connection`

### الخطوة 3: إعداد قاعدة البيانات
اذهب إلى: `http://localhost:3000/setup`
اضغط "فحص وإعداد قاعدة البيانات"

### الخطوة 4: تسجيل الدخول
اذهب إلى: `http://localhost:3000/login`

استخدم إحدى هذه البيانات:

**حساب المدير:**
- رقم الهاتف: `0500000001`
- كلمة المرور: `admin123`

**حساب المستخدم:**
- رقم الهاتف: `0500000002`
- كلمة المرور: `password123`

## إذا لم تعمل الطريقة السريعة

### الطريقة اليدوية:

1. **اذهب إلى Supabase Dashboard**
   - افتح [supabase.com](https://supabase.com)
   - سجل الدخول إلى حسابك
   - اختر مشروعك

2. **افتح SQL Editor**
   - من القائمة الجانبية، اختر "SQL Editor"
   - اضغط "New query"

3. **تشغيل أوامر SQL**
   - انسخ محتوى ملف `database-setup.sql`
   - الصق في SQL Editor
   - اضغط "Run"

4. **التحقق من النجاح**
   - اذهب إلى "Table Editor"
   - تأكد من وجود جدول `users`
   - تأكد من وجود بيانات المستخدمين

## روابط مفيدة

- **الصفحة الرئيسية**: `http://localhost:3000`
- **تسجيل الدخول**: `http://localhost:3000/login`
- **اختبار الاتصال**: `http://localhost:3000/test-connection`
- **إعداد قاعدة البيانات**: `http://localhost:3000/setup`
- **مركز المساعدة**: `http://localhost:3000/help`

## أخطاء شائعة وحلولها

### خطأ: "Supabase credentials are missing"
**الحل**: تحقق من ملف `.env.local` وتأكد من وجود:
```
NEXT_PUBLIC_SUPABASE_URL=your_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key_here
```

### خطأ: "relation 'users' does not exist"
**الحل**: قم بتشغيل أوامر SQL لإنشاء الجداول (راجع الطريقة اليدوية أعلاه)

### خطأ: "No user found with phone"
**الحل**: تأكد من إضافة المستخدمين التجريبيين عبر صفحة `/setup`

## الحصول على المساعدة

إذا كنت لا تزال تواجه مشاكل:

1. راجع مركز المساعدة: `http://localhost:3000/help`
2. تحقق من console المتصفح للأخطاء
3. تحقق من terminal للأخطاء في الخادم

## معلومات إضافية

- **قاعدة البيانات**: Supabase (PostgreSQL)
- **المصادقة**: نظام مصادقة مخصص
- **Frontend**: Next.js 14 + React 18
- **Styling**: Tailwind CSS

---

**ملاحظة**: هذا المشروع يستخدم Next.js مع App Router، وهو بالفعل مبني على React. لا تحتاج لتحويله إلى React - هو React بالفعل! 🎉
