"use client";

import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { FiSettings, FiSave, FiRefreshCw } from "react-icons/fi";
import Header from "@/components/Header";
import Toggle from "@/components/Toggle";

export default function AdminSettings() {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [settings, setSettings] = useState({
    allow_new_registrations: true,
    enableWhatsAppNotifications: true,
    enableEmailNotifications: true,
    requireEmailVerification: true,
    enableGuestTracking: true,
    enableQRCodeScanning: true,
    maxInvitationsPerSubscriber: 100,
    enableAutomaticReminders: true,
    maintenanceMode: false,
    enableAnalytics: true,
  });
  const [isSaving, setIsSaving] = useState(false);
  const router = useRouter();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 },
    },
  };

  useEffect(() => {
    const checkUser = async () => {
      try {
        const userData = localStorage.getItem("user");
        if (!userData) {
          router.push("/login");
          return;
        }

        const parsedUser = JSON.parse(userData);
        if (parsedUser.role !== "admin") {
          router.push("/login");
          return;
        }

        setUser(parsedUser);
        await fetchSettings();
      } catch (error) {
        console.error("Error checking user:", error);
        router.push("/login");
      }
    };

    checkUser();
  }, [router]);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from("system_settings")
        .select("*")
        .single();

      if (error && error.code === "PGRST116") {
        // Not found
        // أنشئ صف افتراضي
        const { data: newData, error: insertError } = await supabase
          .from("system_settings")
          .insert([{ allow_new_registrations: true }])
          .select()
          .single();
        if (insertError) throw insertError;
        setSettings({ ...newData });
        setLoading(false);
        return;
      }

      if (error) throw error;
      if (data) {
        setSettings({ ...data });
      }
      setLoading(false);
    } catch (error) {
      console.error("Error fetching settings:", error);
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    setIsSaving(true);
    try {
      const { data, error } = await supabase
        .from("system_settings")
        .upsert({
          ...settings,
          allow_new_registrations: settings.allow_new_registrations,
        })
        .select()
        .single();
      if (error) throw error;
      setSettings({ ...data }); // تحديث الحالة مباشرة بعد الحفظ
      alert("تم حفظ الإعدادات بنجاح");
    } catch (error) {
      console.error("Error saving settings:", error);
      alert("حدث خطأ أثناء حفظ الإعدادات");
    }
    setIsSaving(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="flex items-center justify-center h-full">
          <FiRefreshCw className="w-8 h-8 animate-spin text-blue-500" />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Header />

      <motion.div
        className="container mx-auto p-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div
          className="bg-white rounded-lg shadow-lg p-6 mb-6"
          variants={itemVariants}
        >
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-800 flex items-center">
              <FiSettings className="mr-2" /> إعدادات النظام
            </h1>
            <button
              onClick={saveSettings}
              disabled={isSaving}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg flex items-center hover:bg-blue-600 transition-colors"
            >
              {isSaving ? (
                <FiRefreshCw className="w-5 h-5 mr-2 animate-spin" />
              ) : (
                <FiSave className="w-5 h-5 mr-2" />
              )}
              حفظ الإعدادات
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-700 mb-3">
                إعدادات عامة
              </h2>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-700 flex flex-col">
                  السماح بالتسجيل الجديد
                  <span className="text-xs text-gray-500 mt-1">
                    إذا كان هذا الخيار مفعلًا، يمكن لأي عميل التسجيل بنفسه.
                  </span>
                </span>
                <Toggle
                  checked={settings.allow_new_registrations}
                  onChange={(value) =>
                    setSettings((prev) => ({
                      ...prev,
                      allow_new_registrations: value,
                    }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-700">تفعيل إشعارات واتساب</span>
                <Toggle
                  checked={settings.enableWhatsAppNotifications}
                  onChange={(value) =>
                    setSettings((prev) => ({
                      ...prev,
                      enableWhatsAppNotifications: value,
                    }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-700">
                  تفعيل إشعارات البريد الإلكتروني
                </span>
                <Toggle
                  checked={settings.enableEmailNotifications}
                  onChange={(value) =>
                    setSettings((prev) => ({
                      ...prev,
                      enableEmailNotifications: value,
                    }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-700">
                  تفعيل التحقق من البريد الإلكتروني
                </span>
                <Toggle
                  checked={settings.requireEmailVerification}
                  onChange={(value) =>
                    setSettings((prev) => ({
                      ...prev,
                      requireEmailVerification: value,
                    }))
                  }
                />
              </div>
            </div>

            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-700 mb-3">
                إعدادات متقدمة
              </h2>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-700">تفعيل تتبع الضيوف</span>
                <Toggle
                  checked={settings.enableGuestTracking}
                  onChange={(value) =>
                    setSettings((prev) => ({
                      ...prev,
                      enableGuestTracking: value,
                    }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-700">تفعيل مسح رمز QR</span>
                <Toggle
                  checked={settings.enableQRCodeScanning}
                  onChange={(value) =>
                    setSettings((prev) => ({
                      ...prev,
                      enableQRCodeScanning: value,
                    }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-700">تفعيل التذكيرات التلقائية</span>
                <Toggle
                  checked={settings.enableAutomaticReminders}
                  onChange={(value) =>
                    setSettings((prev) => ({
                      ...prev,
                      enableAutomaticReminders: value,
                    }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-700">وضع الصيانة</span>
                <Toggle
                  checked={settings.maintenanceMode}
                  onChange={(value) =>
                    setSettings((prev) => ({ ...prev, maintenanceMode: value }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-700">تفعيل التحليلات</span>
                <Toggle
                  checked={settings.enableAnalytics}
                  onChange={(value) =>
                    setSettings((prev) => ({ ...prev, enableAnalytics: value }))
                  }
                />
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
