-- إصلاح هيكل جدول guests للتأكد من وجود جميع الحقول المطلوبة
-- هذا السكريبت سيضيف الحقول المفقودة إذا لم تكن موجودة

-- 1. التحقق من هيكل الجدول الحالي
SELECT 
    'Current guests table structure:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'guests'
ORDER BY ordinal_position;

-- 2. إضافة الحقول المفقودة إذا لم تكن موجودة
-- إضا<PERSON><PERSON> حقل attended (boolean) إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'guests' 
        AND column_name = 'attended'
    ) THEN
        ALTER TABLE public.guests ADD COLUMN attended BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added attended column to guests table';
    ELSE
        RAISE NOTICE 'attended column already exists in guests table';
    END IF;
END $$;

-- 3. إضافة حقل notes إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'guests' 
        AND column_name = 'notes'
    ) THEN
        ALTER TABLE public.guests ADD COLUMN notes TEXT;
        RAISE NOTICE 'Added notes column to guests table';
    ELSE
        RAISE NOTICE 'notes column already exists in guests table';
    END IF;
END $$;

-- 4. إضافة حقل number_of_companions إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'guests' 
        AND column_name = 'number_of_companions'
    ) THEN
        ALTER TABLE public.guests ADD COLUMN number_of_companions INTEGER DEFAULT 0;
        RAISE NOTICE 'Added number_of_companions column to guests table';
    ELSE
        RAISE NOTICE 'number_of_companions column already exists in guests table';
    END IF;
END $$;

-- 5. التأكد من وجود حقل attended_at
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'guests' 
        AND column_name = 'attended_at'
    ) THEN
        ALTER TABLE public.guests ADD COLUMN attended_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added attended_at column to guests table';
    ELSE
        RAISE NOTICE 'attended_at column already exists in guests table';
    END IF;
END $$;

-- 6. تحديث البيانات الموجودة لضمان التوافق
-- إذا كان attended_at موجود وattended فارغ، نحدث attended
UPDATE public.guests 
SET attended = true 
WHERE attended_at IS NOT NULL AND attended IS NULL;

-- إذا كان attended = true وattended_at فارغ، نحدث attended_at
UPDATE public.guests 
SET attended_at = CURRENT_TIMESTAMP 
WHERE attended = true AND attended_at IS NULL;

-- 7. عرض الهيكل النهائي للجدول
SELECT 
    'Final guests table structure:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'guests'
ORDER BY ordinal_position;

-- 8. عرض إحصائيات الجدول
SELECT 
    'Guests table statistics:' as info,
    COUNT(*) as total_guests,
    COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_guests,
    COUNT(CASE WHEN status = 'declined' THEN 1 END) as declined_guests,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_guests,
    COUNT(CASE WHEN attended = true THEN 1 END) as attended_guests,
    COUNT(CASE WHEN attended_at IS NOT NULL THEN 1 END) as attended_at_not_null
FROM public.guests;
