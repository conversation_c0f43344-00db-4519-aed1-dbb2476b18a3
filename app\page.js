'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FiArrowRight, FiCheck, FiUsers, FiCalendar, FiMail, FiSmartphone, FiStar, FiShield } from 'react-icons/fi';
import LandingHeader from '@/components/LandingHeader';

export default function LandingPage() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const router = useRouter();
  
  // إضافة متغير isVisible لتتبع العناصر المرئية عند التمرير
  const [isVisible, setIsVisible] = useState({
    hero: false,
    features: false,
    'how-it-works': false,
    pricing: false,
    testimonials: false,
    faq: false
  });
  
  // إضافة مراجع للأقسام
  const heroRef = useRef(null);
  const featuresRef = useRef(null);
  const howItWorksRef = useRef(null);
  const pricingRef = useRef(null);
  const testimonialsRef = useRef(null);
  const faqRef = useRef(null);
  
  // تعريف الأنيميشنات
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  const fadeInUp = {
    hidden: { y: 60, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  // تعريف متغيرات CSS للتأثيرات
  const fadeIn = "transition-opacity duration-1000";
  const slideUp = "transition-transform duration-1000";
  const slideRight = "transition-transform duration-1000";
  const slideLeft = "transition-transform duration-1000";
  const scale = "transition-transform duration-1000";
  
  // تعريف ميزات النظام
  const features = [
    {
      title: "إدارة الدعوات",
      description: "إنشاء وإدارة دعوات مخصصة لمختلف المناسبات بسهولة وسرعة",
      icon: <FiMail className="h-6 w-6 text-indigo-600" />
    },
    {
      title: "إدارة المدعوين",
      description: "إضافة وتنظيم قوائم المدعوين ومتابعة حالة الحضور",
      icon: <FiUsers className="h-6 w-6 text-indigo-600" />
    },
    {
      title: "جدولة المناسبات",
      description: "تنظيم وجدولة المناسبات وإرسال تذكيرات للمدعوين",
      icon: <FiCalendar className="h-6 w-6 text-indigo-600" />
    },
    {
      title: "إشعارات SMS",
      description: "إرسال إشعارات ورسائل نصية للمدعوين بشكل تلقائي",
      icon: <FiSmartphone className="h-6 w-6 text-indigo-600" />
    },
    {
      title: "تقارير وإحصائيات",
      description: "عرض تقارير مفصلة وإحصائيات عن المدعوين والحضور",
      icon: <FiStar className="h-6 w-6 text-indigo-600" />
    },
    {
      title: "خصوصية وأمان",
      description: "حماية بيانات المدعوين والمناسبات بأعلى معايير الأمان",
      icon: <FiShield className="h-6 w-6 text-indigo-600" />
    }
  ];
  
  // تعريف آراء العملاء
  const testimonials = [
    {
      name: "أحمد محمد",
      role: "منظم حفلات زفاف",
      content: "نظام رائع ساعدني كثيراً في تنظيم دعوات الزفاف وتتبع المدعوين بكل سهولة."
    },
    {
      name: "سارة علي",
      role: "مديرة فعاليات",
      content: "أفضل نظام استخدمته لإدارة الدعوات. سهل الاستخدام وفعال في متابعة الردود."
    },
    {
      name: "خالد عبدالله",
      role: "منسق مناسبات",
      content: "وفر علي الكثير من الوقت والجهد في إدارة قوائم المدعوين وإرسال الرسائل التذكيرية."
    }
  ];

  useEffect(() => {
    // التحقق مما إذا كان المستخدم مسجل الدخول
    const user = localStorage.getItem('user');
    if (user) {
      setIsLoggedIn(true);
    }
    
    // إعداد مراقب التمرير لتفعيل التأثيرات
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const id = entry.target.id || entry.target.getAttribute('data-section');
            if (id) {
              setIsVisible(prev => ({ ...prev, [id]: true }));
            }
          }
        });
      },
      { threshold: 0.1 } // تفعيل عندما يكون 10% من العنصر مرئي
    );
    
    // إضافة مراقبة للأقسام
    const sections = [
      { ref: heroRef.current, id: 'hero' },
      { ref: featuresRef.current, id: 'features' },
      { ref: howItWorksRef.current, id: 'how-it-works' },
      { ref: pricingRef.current, id: 'pricing' },
      { ref: testimonialsRef.current, id: 'testimonials' },
      { ref: faqRef.current, id: 'faq' }
    ];
    
    sections.forEach(section => {
      if (section.ref) {
        observer.observe(section.ref);
      }
    });
    
    // تفعيل قسم البطل عند التحميل
    setIsVisible(prev => ({ ...prev, hero: true }));
    
    return () => {
      sections.forEach(section => {
        if (section.ref) {
          observer.unobserve(section.ref);
        }
      });
    };
  }, []);

  return (
    <div className="min-h-screen">
      <LandingHeader isLoggedIn={isLoggedIn} />
      
      {/* Hero Section */}
      <div 
        ref={heroRef}
        id="hero"
        data-section="hero"
        className="relative bg-gradient-to-r from-indigo-600 to-purple-600 overflow-hidden"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative z-10 py-20 md:py-28 lg:py-32">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div 
                className="text-center lg:text-right"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              >
                <motion.h1 
                  className="text-4xl md:text-5xl font-extrabold text-white tracking-tight leading-tight"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.8 }}
                >
                  نظام إدارة الدعوات الأمثل لمناسباتك
                </motion.h1>
                <motion.p 
                  className="mt-6 text-xl text-indigo-100 max-w-3xl"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.8 }}
                >
                  منصة متكاملة لإدارة الدعوات والمناسبات بكل احترافية وسهولة. أنشئ دعواتك، أدر قوائم المدعوين، وتابع الردود في مكان واحد.
                </motion.p>
                <motion.div 
                  className="mt-10 flex flex-col sm:flex-row justify-center lg:justify-end gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.8 }}
                >
                  <motion.div
                    whileHover={{ scale: 1.05, y: -5 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link 
                      href="/register" 
                      className="px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-700 bg-white hover:bg-indigo-50 md:py-4 md:text-lg md:px-10 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
                    >
                      إنشاء حساب جديد
                    </Link>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05, y: -5 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link 
                      href="/login" 
                      className="px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-800 bg-opacity-60 hover:bg-opacity-70 md:py-4 md:text-lg md:px-10 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
                    >
                      تسجيل الدخول
                    </Link>
                  </motion.div>
                </motion.div>
              </motion.div>
              <div className={`hidden lg:block ${isVisible.hero ? 'translate-x-0' : 'translate-x-20'} ${slideLeft}`}>
                <div className="relative h-96 w-full">
                  <div className="absolute inset-0 bg-white bg-opacity-10 backdrop-filter backdrop-blur-sm rounded-2xl overflow-hidden shadow-2xl transform -rotate-2 transition-all duration-500 hover:rotate-0 hover:scale-105">
                    <div className="p-6">
                      <div className="h-6 w-24 bg-indigo-200 bg-opacity-30 rounded mb-4"></div>
                      <div className="h-4 w-full bg-indigo-200 bg-opacity-20 rounded mb-2"></div>
                      <div className="h-4 w-3/4 bg-indigo-200 bg-opacity-20 rounded mb-2"></div>
                      <div className="h-4 w-1/2 bg-indigo-200 bg-opacity-20 rounded mb-6"></div>
                      {/* محتوى إضافي للنموذج */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Decorative elements */}
        <div className="absolute top-0 inset-x-0 h-48 bg-gradient-to-b from-indigo-700 opacity-50"></div>
        <div className="absolute bottom-0 inset-x-0 h-48 bg-gradient-to-t from-purple-700 opacity-50"></div>
        
        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(20)].map((_, i) => (
            <div 
              key={i}
              className="absolute rounded-full bg-white bg-opacity-20"
              style={{
                width: `${Math.random() * 20 + 5}px`,
                height: `${Math.random() * 20 + 5}px`,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animation: `float ${Math.random() * 10 + 10}s linear infinite`,
                animationDelay: `${Math.random() * 5}s`
              }}
            ></div>
          ))}
        </div>
      </div>
      
      {/* Features Section */}
      <div 
        ref={featuresRef}
        id="features" 
        data-section="features"
        className="py-20 bg-gray-50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center ${isVisible.features ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} ${fadeIn} ${slideUp}`}>
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              كل ما تحتاجه لإدارة دعواتك
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
              نظام متكامل يوفر لك جميع الأدوات اللازمة لإدارة الدعوات والمناسبات بكفاءة عالية
            </p>
          </div>
          
          <div className="mt-16">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <div 
                  key={index} 
                  className={`bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 ${isVisible.features ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
                  style={{ transitionDelay: `${index * 100}ms` }}
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="flex-shrink-0 h-16 w-16 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {/* How it works */}
      <div 
        ref={howItWorksRef}
        id="how-it-works" 
        data-section="how-it-works"
        className="py-20 bg-white"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center ${isVisible['how-it-works'] ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} ${fadeIn} ${slideUp}`}>
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              كيف يعمل النظام؟
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
              خطوات بسيطة للبدء في استخدام نظام إدارة الدعوات
            </p>
          </div>
          
          <div className="mt-16">
            <div className="relative">
              {/* Connector line */}
              <div className="hidden md:block absolute top-0 bottom-0 right-1/2 w-0.5 bg-indigo-200"></div>
              
              {/* Steps */}
              <div className="space-y-20">
                {/* Step 1 */}
                <div className="relative">
                  <div className="md:flex items-center">
                    <div className={`md:w-1/2 md:pl-12 md:text-left text-center ${isVisible['how-it-works'] ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'} ${fadeIn} ${slideRight}`}
                      style={{ transitionDelay: '100ms' }}>
                      <div className="md:hidden flex justify-center mb-4">
                        <div className="h-14 w-14 rounded-full bg-indigo-600 flex items-center justify-center">
                          <span className="text-white font-bold text-xl">1</span>
                        </div>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">إنشاء حساب</h3>
                      <p className="text-lg text-gray-600">قم بإنشاء حساب جديد في النظام بخطوات بسيطة وسريعة، واستمتع بتجربة مجانية لمدة 14 يوم.</p>
                    </div>
                    <div className={`hidden md:flex md:w-1/2 justify-center ${isVisible['how-it-works'] ? 'opacity-100 scale-100' : 'opacity-0 scale-50'} ${fadeIn} ${scale}`}
                      style={{ transitionDelay: '200ms' }}>
                      <div className="h-14 w-14 rounded-full bg-indigo-600 flex items-center justify-center transform transition-transform duration-500 hover:scale-110">
                        <span className="text-white font-bold text-xl">1</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Step 2 */}
                <div className="relative">
                  <div className="md:flex items-center">
                    <div className={`hidden md:flex md:w-1/2 justify-center ${isVisible['how-it-works'] ? 'opacity-100 scale-100' : 'opacity-0 scale-50'} ${fadeIn} ${scale}`}
                      style={{ transitionDelay: '300ms' }}>
                      <div className="h-14 w-14 rounded-full bg-indigo-600 flex items-center justify-center transform transition-transform duration-500 hover:scale-110">
                        <span className="text-white font-bold text-xl">2</span>
                      </div>
                    </div>
                    <div className={`md:w-1/2 md:pr-12 md:text-right text-center ${isVisible['how-it-works'] ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'} ${fadeIn} ${slideLeft}`}
                      style={{ transitionDelay: '400ms' }}>
                      <div className="md:hidden flex justify-center mb-4">
                        <div className="h-14 w-14 rounded-full bg-indigo-600 flex items-center justify-center">
                          <span className="text-white font-bold text-xl">2</span>
                        </div>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">إنشاء دعوة جديدة</h3>
                      <p className="text-lg text-gray-600">أنشئ دعوتك وأضف التفاصيل المطلوبة مثل التاريخ والمكان والوصف، واختر من بين مجموعة متنوعة من القوالب الجاهزة.</p>
                    </div>
                  </div>
                </div>
                
                {/* Step 3 */}
                <div className="relative">
                  <div className="md:flex items-center">
                    <div className={`md:w-1/2 md:pl-12 md:text-left text-center ${isVisible['how-it-works'] ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'} ${fadeIn} ${slideRight}`}
                      style={{ transitionDelay: '500ms' }}>
                      <div className="md:hidden flex justify-center mb-4">
                        <div className="h-14 w-14 rounded-full bg-indigo-600 flex items-center justify-center">
                          <span className="text-white font-bold text-xl">3</span>
                        </div>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">إضافة المدعوين</h3>
                      <p className="text-lg text-gray-600">أضف قائمة المدعوين وبياناتهم بسهولة، وقم بتنظيمهم في قوائم محددة.</p>
                    </div>
                    <div className={`hidden md:flex md:w-1/2 justify-center ${isVisible['how-it-works'] ? 'opacity-100 scale-100' : 'opacity-0 scale-50'} ${fadeIn} ${scale}`}
                      style={{ transitionDelay: '600ms' }}>
                      <div className="h-14 w-14 rounded-full bg-indigo-600 flex items-center justify-center transform transition-transform duration-500 hover:scale-110">
                        <span className="text-white font-bold text-xl">3</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Step 4 */}
                <div className="relative">
                  <div className="md:flex items-center">
                    <div className={`hidden md:flex md:w-1/2 justify-center ${isVisible['how-it-works'] ? 'opacity-100 scale-100' : 'opacity-0 scale-50'} ${fadeIn} ${scale}`}
                      style={{ transitionDelay: '700ms' }}>
                      <div className="h-14 w-14 rounded-full bg-indigo-600 flex items-center justify-center transform transition-transform duration-500 hover:scale-110">
                        <span className="text-white font-bold text-xl">4</span>
                      </div>
                    </div>
                    <div className={`md:w-1/2 md:pr-12 md:text-right text-center ${isVisible['how-it-works'] ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'} ${fadeIn} ${slideLeft}`}
                      style={{ transitionDelay: '800ms' }}>
                      <div className="md:hidden flex justify-center mb-4">
                        <div className="h-14 w-14 rounded-full bg-indigo-600 flex items-center justify-center">
                          <span className="text-white font-bold text-xl">4</span>
                        </div>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">إرسال الدعوات ومتابعة الردود</h3>
                      <p className="text-lg text-gray-600">أرسل الدعوات للمدعوين وتابع ردودهم بشكل مباشر، وقم بتحديث الحضور في الوقت الفعلي.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Testimonials */}
      <div 
        ref={testimonialsRef}
        id="testimonials" 
        data-section="testimonials"
        className="py-16 bg-indigo-50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center ${isVisible.testimonials ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} ${fadeIn} ${slideUp}`}>
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              ماذا يقول عملاؤنا
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
              آراء بعض العملاء الذين استخدموا نظامنا لإدارة دعواتهم
            </p>
          </div>
          
          <div className="mt-16">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <div 
                  key={index} 
                  className={`bg-white p-6 rounded-lg shadow-md ${isVisible.testimonials ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
                  style={{ transitionDelay: `${index * 150}ms` }}
                >
                  <div className="h-24">
                    <p className="text-gray-600 text-lg">{testimonial.content}</p>
                  </div>
                  <div className="mt-6 flex items-center">
                    <div className="h-10 w-10 rounded-full bg-indigo-200 flex items-center justify-center">
                      <span className="text-indigo-600 font-bold">{testimonial.name.charAt(0)}</span>
                    </div>
                    <div className="mr-3">
                      <h4 className="text-gray-900 font-medium">{testimonial.name}</h4>
                      <p className="text-gray-500 text-sm">{testimonial.role}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {/* FAQ Section */}
      <div 
        ref={faqRef}
        id="faq" 
        data-section="faq"
        className="py-20 bg-white"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center ${isVisible.faq ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} ${fadeIn} ${slideUp}`}>
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              الأسئلة الشائعة
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
              إجابات على الأسئلة الأكثر شيوعاً حول نظام إدارة الدعوات
            </p>
          </div>
          
          <div className="mt-12 max-w-3xl mx-auto">
            <div className="space-y-6">
              {[
                {
                  question: "كيف يمكنني البدء في استخدام النظام؟",
                  answer: "يمكنك البدء بإنشاء حساب جديد من خلال صفحة التسجيل، ثم إعداد ملفك الشخصي وإنشاء أول دعوة لك."
                },
                {
                  question: "هل يمكنني تخصيص تصميم الدعوات؟",
                  answer: "نعم، يوفر النظام مجموعة متنوعة من القوالب والتصاميم التي يمكنك تخصيصها حسب نوع المناسبة واحتياجاتك."
                },
                {
                  question: "كيف يتم إرسال الدعوات للمدعوين؟",
                  answer: "يمكن إرسال الدعوات عبر رسائل SMS أو مشاركة رابط الدعوة عبر وسائل التواصل الاجتماعي أو تطبيقات المراسلة."
                },
                {
                  question: "هل يمكنني متابعة حالة الدعوات والردود؟",
                  answer: "نعم، يوفر النظام لوحة تحكم تفاعلية تمكنك من متابعة حالة كل دعوة والاطلاع على ردود المدعوين في الوقت الفعلي."
                },
                {
                  question: "هل النظام متوافق مع الأجهزة المحمولة؟",
                  answer: "نعم، النظام متوافق تمامًا مع جميع الأجهزة المحمولة والحواسيب، ويمكن استخدامه من أي متصفح حديث."
                }
              ].map((faq, index) => (
                <div 
                  key={index} 
                  className={`bg-gray-50 rounded-lg p-6 shadow-sm ${isVisible.faq ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
                  style={{ transitionDelay: `${index * 100}ms` }}
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{faq.question}</h3>
                  <p className="text-gray-600">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
          
          <div className={`mt-16 text-center ${isVisible.faq ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`} style={{ transitionDelay: '500ms' }}>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">جاهز للبدء؟</h3>
            <Link 
              href="/register" 
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg"
            >
              إنشاء حساب مجاني
              <FiArrowRight className="mr-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </div>
      
      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-bold mb-4">نظام إدارة الدعوات</h3>
              <p className="text-gray-400">منصة متكاملة لإدارة الدعوات والمناسبات بكل احترافية وسهولة.</p>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4">روابط سريعة</h3>
              <ul className="space-y-2">
                <li><Link href="#features" className="text-gray-400 hover:text-white transition-colors">الميزات</Link></li>
                <li><Link href="#how-it-works" className="text-gray-400 hover:text-white transition-colors">كيف يعمل</Link></li>
                <li><Link href="#pricing" className="text-gray-400 hover:text-white transition-colors">الأسعار</Link></li>
                <li><Link href="#faq" className="text-gray-400 hover:text-white transition-colors">الأسئلة الشائعة</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4">الدعم</h3>
              <ul className="space-y-2">
                <li><Link href="#" className="text-gray-400 hover:text-white transition-colors">مركز المساعدة</Link></li>
                <li><Link href="#" className="text-gray-400 hover:text-white transition-colors">اتصل بنا</Link></li>
                <li><Link href="#" className="text-gray-400 hover:text-white transition-colors">سياسة الخصوصية</Link></li>
                <li><Link href="#" className="text-gray-400 hover:text-white transition-colors">شروط الاستخدام</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4">تواصل معنا</h3>
              <div className="flex space-x-4 space-x-reverse">
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <span className="sr-only">Facebook</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <span className="sr-only">Twitter</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <span className="sr-only">Instagram</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-center">
            <p className="text-gray-400 text-sm">
              &copy; {new Date().getFullYear()} نظام إدارة الدعوات. جميع الحقوق محفوظة.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}




