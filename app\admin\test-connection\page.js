'use client';

import { useState, useEffect } from 'react';
import { supabase } from '../../../lib/supabase';
import Navbar from '../../../components/Navbar';
import Link from 'next/link';

export default function TestConnection() {
  const [status, setStatus] = useState('جاري التحقق...');
  const [error, setError] = useState(null);
  const [envVars, setEnvVars] = useState({});
  const [buckets, setBuckets] = useState([]);

  useEffect(() => {
    async function checkConnection() {
      try {
        // التحقق من متغيرات البيئة
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
        
        setEnvVars({
          supabaseUrl: supabaseUrl ? 'موجود' : 'غير موجود',
          supabaseKey: supabaseKey ? 'موجود' : 'غير موجود'
        });

        // التحقق من الاتصال بـ Supabase
        const { data, error } = await supabase
          .from('users')
          .select('count(*)', { count: 'exact', head: true });

        if (error) {
          setStatus('فشل الاتصال بقاعدة البيانات');
          setError(error.message);
          console.error('Supabase database connection error:', error);
        } else {
          setStatus('تم الاتصال بقاعدة البيانات بنجاح');
          console.log('Database connection successful');
          
          // التحقق من الاتصال بـ Storage
          try {
            const { data: bucketsData, error: bucketsError } = await supabase.storage.listBuckets();
            
            if (bucketsError) {
              console.error('Storage connection error:', bucketsError);
              setError(prev => prev + '\n\nفشل الاتصال بالتخزين: ' + bucketsError.message);
            } else {
              console.log('Storage connection successful, buckets:', bucketsData);
              setBuckets(bucketsData || []);
              setStatus(prev => prev + ' والتخزين');
            }
          } catch (storageError) {
            console.error('Unexpected storage error:', storageError);
            setError(prev => prev + '\n\nخطأ غير متوقع في التخزين: ' + storageError.message);
          }
        }
      } catch (err) {
        setStatus('حدث خطأ غير متوقع');
        setError(err.message);
        console.error('Unexpected error checking connection:', err);
      }
    }

    checkConnection();
  }, []);

  return (
    <>
      <Navbar userRole="admin" />
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-6">اختبار الاتصال بـ Supabase</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-bold mb-4">حالة الاتصال</h2>
          
          <div className={`p-4 mb-4 rounded ${
            status.includes('بنجاح') ? 'bg-green-100 text-green-700' : 
            status.includes('فشل') ? 'bg-red-100 text-red-700' : 
            'bg-blue-100 text-blue-700'
          }`}>
            <p className="font-bold">{status}</p>
          </div>
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 p-4 rounded mb-4">
              <p className="font-bold">رسالة الخطأ:</p>
              <pre className="whitespace-pre-wrap">{error}</pre>
            </div>
          )}
          
          <div className="mb-6">
            <h3 className="font-bold mb-2">متغيرات البيئة:</h3>
            <ul className="list-disc pl-5">
              <li className="mb-1">NEXT_PUBLIC_SUPABASE_URL: {envVars.supabaseUrl}</li>
              <li className="mb-1">NEXT_PUBLIC_SUPABASE_ANON_KEY: {envVars.supabaseKey}</li>
            </ul>
          </div>
          
          {buckets.length > 0 && (
            <div>
              <h3 className="font-bold mb-2">مساحات التخزين الموجودة:</h3>
              <ul className="list-disc pl-5">
                {buckets.map(bucket => (
                  <li key={bucket.id || bucket.name} className="mb-1">
                    {bucket.name} {bucket.public ? '(عام)' : '(خاص)'}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
