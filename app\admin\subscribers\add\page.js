"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { addSubscriber } from "@/app/actions";
import Header from "@/components/Header";
import Link from "next/link";
import { FiUserPlus, FiArrowRight } from "react-icons/fi";
import { motion } from "framer-motion";
import { Dialog } from "@headlessui/react";

export default function AddSubscriber() {
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [showConfirm, setShowConfirm] = useState(false);
  const router = useRouter();

  async function handleSubmit(e) {
    e.preventDefault();
    setShowConfirm(true);
  }

  async function handleConfirmAdd() {
    setShowConfirm(false);
    setLoading(true);
    setError("");
    setSuccess("");
    if (!name || !phone || !password) {
      setError("جميع الحقول مطلوبة");
      setLoading(false);
      return;
    }
    try {
      const result = await addSubscriber(name, phone, password);
      if (result.error) {
        throw new Error(result.error.message || "حدث خطأ أثناء إضافة المشترك");
      }
      setSuccess("تمت إضافة المشترك بنجاح");
      setName("");
      setPhone("");
      setPassword("");
      setTimeout(() => router.push("/admin/subscribers"), 1200);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Header userRole="admin" />
      <motion.div
        className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-2xl mx-auto px-4 py-8 flex items-center justify-between">
          <div className="flex items-center">
            <div className="bg-white p-3 rounded-full shadow-md">
              <FiUserPlus className="h-6 w-6 text-indigo-600" />
            </div>
            <div className="mr-4">
              <h1 className="text-2xl font-bold">إضافة مشترك جديد</h1>
              <p className="text-indigo-200">أدخل بيانات المشترك الجديد</p>
            </div>
          </div>
          <Link
            href="/admin/subscribers"
            className="flex items-center text-white hover:text-gray-200"
          >
            <FiArrowRight className="ml-2" /> العودة للمشتركين
          </Link>
        </div>
      </motion.div>
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-lg p-8">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 text-center">
              {error}
            </div>
          )}
          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 text-center">
              {success}
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label
                htmlFor="name"
                className="block text-gray-700 font-bold mb-2"
              >
                الاسم
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="أدخل اسم المشترك"
                required
              />
            </div>
            <div>
              <label
                htmlFor="phone"
                className="block text-gray-700 font-bold mb-2"
              >
                رقم الهاتف
              </label>
              <input
                type="text"
                id="phone"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="05xxxxxxxx"
                dir="ltr"
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                يجب أن يكون رقم الهاتف فريداً
              </p>
            </div>
            <div>
              <label
                htmlFor="password"
                className="block text-gray-700 font-bold mb-2"
              >
                كلمة المرور
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="أدخل كلمة المرور"
                required
              />
            </div>
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={loading}
                className={`${
                  loading
                    ? "bg-indigo-600"
                    : "bg-indigo-600 hover:bg-indigo-700"
                } text-white font-bold py-2 px-6 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500`}
              >
                {loading ? "جاري الإضافة..." : "إضافة المشترك"}
              </button>
            </div>
          </form>
          {/* نافذة التأكيد */}
          <Dialog
            open={showConfirm}
            onClose={() => setShowConfirm(false)}
            className="fixed z-50 inset-0 overflow-y-auto"
          >
            <div className="flex items-center justify-center min-h-screen px-4">
              <div
                className="fixed inset-0 bg-black opacity-30"
                aria-hidden="true"
              />
              <Dialog.Panel className="bg-white rounded-lg shadow-xl p-8 z-50 max-w-sm mx-auto">
                <Dialog.Title className="text-lg font-bold mb-4 text-gray-800">
                  تأكيد إضافة المشترك
                </Dialog.Title>
                <Dialog.Description className="mb-6 text-gray-600">
                  هل أنت متأكد أنك تريد إضافة هذا المشترك؟
                </Dialog.Description>
                <div className="flex justify-end gap-2">
                  <button
                    onClick={() => setShowConfirm(false)}
                    className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handleConfirmAdd}
                    className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
                    disabled={loading}
                  >
                    {loading ? "جاري الإضافة..." : "تأكيد"}
                  </button>
                </div>
              </Dialog.Panel>
            </div>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
