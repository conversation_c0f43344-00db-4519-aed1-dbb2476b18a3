import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { FiSettings, FiUsers, FiMail, FiPieChart } from "react-icons/fi";

export default async function AdminDashboard() {
  // استخدام Server Component للحصول على البيانات
  const { data: subscribers } = await supabase
    .from("users")
    .select("*")
    .eq("role", "subscriber");

  const { data: invitations } = await supabase.from("invitations").select("*");

  const { data: guests } = await supabase.from("guests").select("*");

  const acceptedGuests =
    guests?.filter((guest) => guest.status === "accepted") || [];
  const attendedGuests = guests?.filter((guest) => guest.attended) || [];

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">لوحة تحكم المدير</h1>
        <Link
          href="/admin/settings"
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center"
        >
          <FiSettings className="mr-2" />
          الإعدادات
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">المشتركين</h2>
            <FiUsers className="text-blue-500 text-2xl" />
          </div>
          <p className="text-3xl font-bold text-blue-600">
            {subscribers?.length || 0}
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">الدعوات</h2>
            <FiMail className="text-green-500 text-2xl" />
          </div>
          <p className="text-3xl font-bold text-green-600">
            {invitations?.length || 0}
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">الحضور المؤكد</h2>
            <FiPieChart className="text-purple-500 text-2xl" />
          </div>
          <p className="text-3xl font-bold text-purple-600">
            {attendedGuests?.length || 0} / {acceptedGuests?.length || 0}
          </p>
        </div>
      </div>

      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">المشتركين</h2>
          <Link
            href="/admin/subscribers/add"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            إضافة مشترك جديد
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الاسم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  رقم الهاتف
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الإنشاء
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {subscribers?.map((subscriber) => (
                <tr key={subscriber.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {subscriber.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {subscriber.phone}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {new Date(subscriber.created_at).toLocaleDateString(
                      "ar-EG"
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Link
                      href={`/admin/subscribers/${subscriber.id}`}
                      className="text-blue-600 hover:text-blue-900 ml-4"
                    >
                      عرض
                    </Link>
                    <Link
                      href={`/admin/subscribers/${subscriber.id}/edit`}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      تعديل
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
