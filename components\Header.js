"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import {
  FiMenu,
  FiX,
  FiUser,
  FiLogOut,
  FiHome,
  FiMail,
  FiUsers,
  FiSettings,
  FiCalendar,
  FiFileText,
  FiBell,
} from "react-icons/fi";
import Logo from "./Logo";

export default function Header({ userRole = "subscriber", userName }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const router = useRouter();
  const pathname = usePathname();

  // تحديد الروابط بناءً على دور المستخدم
  const getLinks = () => {
    if (userRole === "admin") {
      return [
        { name: "الرئيسية", href: "/admin/dashboard", icon: FiHome },
        { name: "المشتركين", href: "/admin/subscribers", icon: FiUsers },
        { name: "الاشتراكات", href: "/admin/subscriptions", icon: FiFileText },
        { name: "التقارير", href: "/admin/reports", icon: FiCalendar },
        { name: "الإعدادات", href: "/admin/settings", icon: FiSettings },
      ];
    } else {
      return [
        { name: "الرئيسية", href: "/subscriber/dashboard", icon: FiHome },
        { name: "الدعوات", href: "/subscriber/invitations", icon: FiMail },
        { name: "الإعدادات", href: "/subscriber/settings", icon: FiSettings },
      ];
    }
  };

  const links = getLinks();

  // جلب الإشعارات للأدمن
  useEffect(() => {
    if (userRole === "admin") {
      fetchNotifications();
    }
  }, [userRole]);

  const fetchNotifications = async () => {
    try {
      const response = await fetch("/api/admin/notifications");
      if (response.ok) {
        const data = await response.json();
        setNotifications(data.notifications || []);
        setUnreadCount(data.unreadCount || 0);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("user");
    router.push("/login");
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
  };

  const markAsRead = async (notificationId) => {
    try {
      await fetch(`/api/admin/notifications/${notificationId}/read`, {
        method: "POST",
      });
      fetchNotifications();
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  return (
    <nav className="bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 shadow-lg sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          <div className="flex items-center">
            <Logo />
            <div className="hidden md:ml-6 md:flex md:items-center md:space-x-4 md:space-x-reverse">
              {links.map((item, index) => (
                <div key={item.name}>
                  <Link
                    href={item.href}
                    className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center hover:bg-white/10 ${
                      pathname === item.href
                        ? "bg-white/20 text-white"
                        : "text-purple-100 hover:text-white"
                    }`}
                  >
                    <item.icon className="ml-2" />
                    {item.name}
                  </Link>
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            {userName && (
              <div className="hidden md:flex items-center text-gray-700">
                <span className="bg-white/20 p-2 rounded-full ml-2">
                  <FiUser className="text-white" />
                </span>
                <span className="text-sm font-medium text-white">{userName}</span>
              </div>
            )}

            <div className="flex items-center space-x-2 space-x-reverse">
              {/* زر تسجيل الخروج */}
              {userRole && (
                <button
                  onClick={handleLogout}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 ml-2"
                >
                  <FiLogOut className="ml-2" />
                  تسجيل الخروج
                </button>
              )}
              {/* إشعار التفعيل للأدمن - بجانب زر تسجيل الخروج */}
              {userRole === "admin" && (
                <div className="relative">
                  <button
                    onClick={toggleNotifications}
                    className="relative p-2 text-white hover:bg-white/10 rounded-full transition-colors duration-200"
                  >
                    <FiBell className="h-6 w-6" />
                    {unreadCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {unreadCount > 9 ? "9+" : unreadCount}
                      </span>
                    )}
                  </button>
                  {showNotifications && (
                    <>
                      <div
                        className="fixed inset-0 z-40"
                        onClick={() => setShowNotifications(false)}
                      />
                      <div className="absolute right-0 mt-2 w-80 bg-white text-gray-800 rounded-lg shadow-2xl border border-gray-200 z-50 max-h-96 overflow-y-auto">
                        <div className="p-4 border-b border-gray-200">
                          <h3 className="text-lg font-semibold text-gray-900">
                            الإشعارات
                          </h3>
                        </div>
                        <div className="divide-y divide-gray-200">
                          {notifications.length === 0 ? (
                            <div className="p-4 text-center text-gray-500">
                              لا توجد إشعارات جديدة
                            </div>
                          ) : (
                            notifications.map((notification) => (
                              <div
                                key={notification.id}
                                className={`p-4 hover:bg-gray-50 cursor-pointer ${
                                  !notification.read ? "bg-blue-50" : ""
                                }`}
                                onClick={() => markAsRead(notification.id)}
                              >
                                <p className="text-sm font-medium text-gray-900">
                                  {notification.title}
                                </p>
                                <p className="text-sm text-gray-600 mt-1">
                                  {notification.message}
                                </p>
                                <p className="text-xs text-gray-400 mt-2">
                                  {new Date(
                                    notification.created_at
                                  ).toLocaleDateString("ar-SA")}
                                </p>
                              </div>
                            ))
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* زر القائمة للشاشات الصغيرة */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-purple-100 hover:text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white transition-colors duration-200"
            >
              {isMenuOpen ? (
                <FiX className="block h-6 w-6" />
              ) : (
                <FiMenu className="block h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* القائمة المنسدلة للشاشات الصغيرة */}
      {isMenuOpen && (
        <div className="md:hidden bg-purple-900 bg-opacity-95 absolute w-full z-50 shadow-xl">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {userName && (
              <div className="flex items-center px-3 py-2 text-white border-b border-purple-700 mb-2 pb-3">
                <span className="bg-white/20 p-2 rounded-full ml-2">
                  <FiUser className="text-white" />
                </span>
                <span className="text-sm font-medium">{userName}</span>
              </div>
            )}

            {links.map((item) => (
              <div key={item.name}>
                <Link
                  href={item.href}
                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 flex items-center ${
                    pathname === item.href
                      ? "bg-purple-700 text-white"
                      : "text-purple-100 hover:text-white hover:bg-purple-700"
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <item.icon className="ml-2" />
                  {item.name}
                </Link>
              </div>
            ))}

            {userRole && (
              <button
                onClick={() => {
                  handleLogout();
                  setIsMenuOpen(false);
                }}
                className="w-full text-right block px-3 py-2 rounded-md text-base font-medium text-purple-100 hover:text-white hover:bg-purple-700 transition-colors duration-200 flex items-center"
              >
                <FiLogOut className="ml-2" />
                تسجيل الخروج
              </button>
            )}
          </div>
        </div>
      )}

      {/* إغلاق نافذة الإشعارات عند الضغط خارجها */}
      {showNotifications && (
        <div
          className="fixed inset-0 z-30"
          onClick={() => setShowNotifications(false)}
        />
      )}
    </nav>
  );
}
