"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import {
  FiMenu,
  FiX,
  FiUser,
  FiLogOut,
  FiHome,
  FiMail,
  FiUsers,
  FiSettings,
  FiCalendar,
  FiFileText,
  FiBell,
} from "react-icons/fi";
import { motion, AnimatePresence } from "framer-motion";
import Logo from "./Logo";

export default function Header({ userRole = "subscriber", userName }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [user, setUser] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifCount, setNotifCount] = useState(0);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // التحقق من وجود المستخدم في localStorage
    const userData = localStorage.getItem("user");
    if (userData) {
      setUser(JSON.parse(userData));
    }
  }, []);

  useEffect(() => {
    if (userRole === "admin") {
      fetch("/api/admin/notifications")
        .then((res) => res.json())
        .then((data) => {
          if (data.success) {
            setNotifications(data.notifications);
            setNotifCount(data.notifications.length);
          }
        });
    }
  }, [userRole]);

  // تعريف الروابط حسب دور المستخدم
  const getLinks = () => {
    if (userRole === "admin") {
      return [
        { name: "لوحة التحكم", href: "/admin/dashboard", icon: "home" },
        { name: "المشتركين", href: "/admin/subscribers", icon: "users" },
        { name: "الدعوات", href: "/admin/invitations", icon: "mail" },
        { name: "الإعدادات", href: "/admin/settings", icon: "settings" },
      ];
    } else {
      return [
        { name: "لوحة التحكم", href: "/subscriber/dashboard", icon: "home" },
        { name: "الدعوات", href: "/subscriber/invitations", icon: "mail" },
        { name: "المدعوين", href: "/subscriber/guests", icon: "users" },
        { name: "الملف الشخصي", href: "/subscriber/profile", icon: "user" },
      ];
    }
  };

  const links = getLinks();

  // دالة للتحقق من الرابط النشط
  const isActive = (href) => {
    return pathname === href
      ? "bg-purple-700 text-white"
      : "text-purple-200 hover:bg-purple-600 hover:text-white";
  };

  // دالة لإرجاع الأيقونة المناسبة
  const getIcon = (name) => {
    switch (name) {
      case "لوحة التحكم":
        return <FiHome className="ml-2" />;
      case "المشتركين":
      case "المدعوين":
        return <FiUsers className="ml-2" />;
      case "الدعوات":
        return <FiMail className="ml-2" />;
      case "الإعدادات":
        return <FiSettings className="ml-2" />;
      case "الملف الشخصي":
        return <FiUser className="ml-2" />;
      case "المناسبات":
        return <FiCalendar className="ml-2" />;
      case "التقارير":
        return <FiFileText className="ml-2" />;
      default:
        return null;
    }
  };

  // تعريف الأنيميشنات
  const menuVariants = {
    closed: {
      opacity: 0,
      height: 0,
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
    open: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.3,
        ease: "easeInOut",
        staggerChildren: 0.07,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    closed: { opacity: 0, y: -10 },
    open: { opacity: 1, y: 0 },
  };

  const handleLogout = () => {
    // حذف بيانات المستخدم من localStorage
    localStorage.removeItem("user");

    // توجيه المستخدم إلى صفحة تسجيل الدخول
    router.push("/login");
  };

  const dashboardLink =
    userRole === "admin" ? "/admin/dashboard" : "/subscriber/dashboard";

  return (
    <header className="bg-gradient-to-br from-indigo-500 to-purple-600  shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          <motion.div
            className="flex items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Link href={dashboardLink} className="flex-shrink-0">
              <Logo />
            </Link>
            <div className="hidden md:ml-6 md:flex md:items-center md:space-x-4 md:space-x-reverse">
              {links.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <Link
                    href={item.href}
                    className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${isActive(
                      item.href
                    )} transition-colors duration-200`}
                  >
                    {getIcon(item.name)}
                    {item.name}
                  </Link>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <motion.div
            className="flex items-center space-x-4 space-x-reverse"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            {userName && (
              <motion.div
                className="hidden md:flex items-center text-gray-700"
                whileHover={{ scale: 1.05 }}
              >
                <span className="bg-purple-700 rounded-full h-8 w-8 flex items-center justify-center mr-2 text-white">
                  {userName.charAt(0)}
                </span>
                <span className="text-sm font-medium">{userName}</span>
              </motion.div>
            )}

            <div className="hidden md:flex items-center gap-4">
              {/* زر تسجيل الخروج */}
              {userRole && (
                <motion.button
                  onClick={handleLogout}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 ml-2"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FiLogOut className="ml-2" />
                  تسجيل الخروج
                </motion.button>
              )}
              {/* إشعار التفعيل للأدمن - بجانب زر تسجيل الخروج */}
              {userRole === "admin" && (
                <div className="relative">
                  <button
                    className="relative focus:outline-none ml-2 text-white"
                    onClick={() => setShowNotifications((prev) => !prev)}
                    aria-label="الإشعارات"
                  >
                    <FiBell className="w-6 h-6" />
                    {notifCount > 0 && (
                      <span className="absolute -top-5 -right-2 bg-red-500 text-xs rounded-full px-1.5 py-0.5 text-white">
                        {notifCount}
                      </span>
                    )}
                  </button>
                  <AnimatePresence>
                    {showNotifications && (
                      <>
                        {/* طبقة شفافة لإغلاق الإشعارات عند الضغط خارجها */}
                        <div
                          className="fixed inset-0 z-40"
                          onClick={() => setShowNotifications(false)}
                        />
                        <motion.div
                          className="absolute right-0 mt-2 w-80 bg-white text-gray-800 rounded-lg shadow-2xl border border-gray-200 z-50 max-h-96 overflow-y-auto"
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                        >
                          <div className="p-3 border-b font-bold text-indigo-700 flex items-center">
                            <FiBell className="ml-2" /> إشعارات التفعيل
                            <button
                              className="ml-auto text-gray-400 hover:text-red-500"
                              onClick={() => setShowNotifications(false)}
                              aria-label="إغلاق الإشعارات"
                            >
                              <FiX />
                            </button>
                          </div>
                          {notifications.length === 0 ? (
                            <div className="p-6 text-center text-gray-400 text-base font-semibold">
                              لا توجد إشعارات جديدة
                            </div>
                          ) : (
                            notifications.map((notif) => (
                              <NotificationItem
                                key={notif.id}
                                notif={notif}
                                notifications={notifications}
                                setNotifications={setNotifications}
                                notifCount={notifCount}
                                setNotifCount={setNotifCount}
                              />
                            ))
                          )}
                        </motion.div>
                      </>
                    )}
                  </AnimatePresence>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </div>

      {/* القائمة المنسدلة للشاشات الصغيرة */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            className="md:hidden bg-purple-900 bg-opacity-95 absolute w-full z-50 shadow-xl"
            variants={menuVariants}
            initial="closed"
            animate="open"
            exit="closed"
          >
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {userName && (
                <motion.div
                  className="flex items-center px-3 py-2 text-white border-b border-purple-700 mb-2 pb-3"
                  variants={itemVariants}
                >
                  <span className="bg-purple-700 rounded-full h-8 w-8 flex items-center justify-center mr-2">
                    {userName.charAt(0)}
                  </span>
                  <span className="text-sm font-medium">{userName}</span>
                </motion.div>
              )}

              {links.map((item) => (
                <motion.div key={item.name} variants={itemVariants}>
                  <Link
                    href={item.href}
                    className={`flex items-center px-3 py-2 rounded-md text-base font-medium ${
                      isActive(item.href).includes("bg-purple-700")
                        ? "bg-purple-700 text-white"
                        : "text-purple-200 hover:bg-purple-700 hover:text-white"
                    } transition-colors duration-200`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {getIcon(item.name)}
                    {item.name}
                  </Link>
                </motion.div>
              ))}

              {userRole && (
                <motion.button
                  onClick={() => {
                    handleLogout();
                    setIsMenuOpen(false);
                  }}
                  className="w-full flex items-center px-3 py-2 rounded-md text-base font-medium text-white bg-red-600 hover:bg-red-700 transition-colors duration-200"
                  variants={itemVariants}
                  whileTap={{ scale: 0.95 }}
                >
                  <FiLogOut className="ml-2" />
                  تسجيل الخروج
                </motion.button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* إغلاق نافذة الإشعارات عند الضغط خارجها */}
      <useEffect>
        {showNotifications && (
          <div
            className="fixed inset-0 z-10"
            onClick={() => setShowNotifications(false)}
          />
        )}
      </useEffect>
    </header>
  );
}

// مكون فرعي لعناصر الإشعار مع حالة تحميل
function NotificationItem({
  notif,
  notifications,
  setNotifications,
  notifCount,
  setNotifCount,
}) {
  const [loading, setLoading] = useState(false);
  return (
    <div className="p-3 border-b last:border-b-0 flex flex-col">
      <span className="font-medium">{notif.userName}</span>
      <span className="text-xs text-gray-500">
        {new Date(notif.createdAt).toLocaleString("ar-SA")}
      </span>
      <button
        className={`mt-2 bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600 text-xs flex items-center justify-center ${
          loading ? "opacity-60 cursor-not-allowed" : ""
        }`}
        onClick={async () => {
          setLoading(true);
          await fetch("/api/admin/activate-user", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ userId: notif.userId }),
          });
          setNotifications(
            notifications.filter((n) => n.userId !== notif.userId)
          );
          setNotifCount(notifCount - 1);
          setLoading(false);
        }}
        disabled={loading}
      >
        {loading ? (
          <svg className="animate-spin h-4 w-4 mr-1" viewBox="0 0 24 24">
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8v8z"
            />
          </svg>
        ) : null}
        تفعيل الحساب
      </button>
    </div>
  );
}
