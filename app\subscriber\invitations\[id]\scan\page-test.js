"use client";

import { useState } from "react";

export default function ScanPageTest({ params }) {
  const [loading, setLoading] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
        <div className="bg-white shadow-xl rounded-2xl overflow-hidden mb-8">
          <div className="p-6 bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 text-white">
            <h1 className="text-3xl font-bold mb-2">
              اختبار صفحة المسح
            </h1>
            <p className="text-purple-100 text-lg">
              معرف الدعوة: {params?.id || "غير محدد"}
            </p>
          </div>
        </div>
        
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
          <h2 className="text-2xl font-bold mb-6 text-gray-800">
            اختبار أساسي
          </h2>
          <p className="text-gray-600">
            إذا كنت ترى هذه الرسالة، فإن الصفحة تعمل بشكل أساسي.
          </p>
          <button
            onClick={() => setLoading(!loading)}
            className="mt-4 bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
          >
            {loading ? "جاري التحميل..." : "اختبار الحالة"}
          </button>
        </div>
      </div>
    </div>
  );
}
