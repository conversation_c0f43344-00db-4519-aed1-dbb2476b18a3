// مكتبة مساعدة لإنتاج ومعالجة أكواد QR للدعوات
import QRCode from 'qrcode';
import { v4 as uuidv4 } from 'uuid';

/**
 * إنتاج رمز QR فريد للضيف
 * @param {string} invitationId - معرف الدعوة
 * @param {string} guestPhone - رقم هاتف الضيف
 * @param {string} guestName - اسم الضيف
 * @returns {string} رمز QR فريد
 */
export function generateGuestQRCode(invitationId, guestPhone, guestName) {
  // إنشاء رمز فريد يحتوي على معلومات الضيف والدعوة
  const uniqueId = uuidv4();
  const timestamp = Date.now();
  
  // تشفير بسيط للبيانات
  const qrData = {
    id: uniqueId,
    invitationId,
    phone: guestPhone,
    name: guestName,
    timestamp,
    type: 'guest_entry'
  };
  
  // تحويل البيانات إلى نص مشفر
  return btoa(JSON.stringify(qrData));
}

/**
 * فك تشفير رمز QR واستخراج البيانات
 * @param {string} qrCode - رمز QR المشفر
 * @returns {object|null} بيانات الضيف أو null في حالة الخطأ
 */
export function decodeGuestQRCode(qrCode) {
  try {
    const decodedData = JSON.parse(atob(qrCode));
    
    // التحقق من صحة البيانات
    if (!decodedData.id || !decodedData.invitationId || !decodedData.phone) {
      return null;
    }
    
    return decodedData;
  } catch (error) {
    console.error('Error decoding QR code:', error);
    return null;
  }
}

/**
 * إنتاج صورة QR Code
 * @param {string} qrCodeData - البيانات المشفرة
 * @param {object} options - خيارات التصميم
 * @returns {Promise<string>} رابط الصورة (Data URL)
 */
export async function generateQRCodeImage(qrCodeData, options = {}) {
  const defaultOptions = {
    width: 300,
    height: 300,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    },
    errorCorrectionLevel: 'M',
    type: 'image/png',
    quality: 0.92,
    margin: 1,
    ...options
  };
  
  try {
    const qrCodeDataURL = await QRCode.toDataURL(qrCodeData, defaultOptions);
    return qrCodeDataURL;
  } catch (error) {
    console.error('Error generating QR code image:', error);
    throw new Error('فشل في إنتاج رمز QR');
  }
}

/**
 * إنتاج QR Code مخصص للدعوة مع تصميم جميل
 * @param {string} qrCodeData - البيانات المشفرة
 * @param {object} guestInfo - معلومات الضيف
 * @param {object} invitationInfo - معلومات الدعوة
 * @returns {Promise<string>} رابط الصورة المخصصة
 */
export async function generateCustomQRCode(qrCodeData, guestInfo, invitationInfo) {
  try {
    // إنتاج QR Code أساسي
    const qrImage = await generateQRCodeImage(qrCodeData, {
      width: 250,
      color: {
        dark: '#1a365d',
        light: '#ffffff'
      }
    });
    
    return qrImage;
  } catch (error) {
    console.error('Error generating custom QR code:', error);
    throw error;
  }
}

/**
 * التحقق من صحة رمز QR للدعوة
 * @param {string} qrCode - رمز QR
 * @param {string} expectedInvitationId - معرف الدعوة المتوقع
 * @returns {object} نتيجة التحقق
 */
export function validateQRCode(qrCode, expectedInvitationId) {
  const decodedData = decodeGuestQRCode(qrCode);
  
  if (!decodedData) {
    return {
      valid: false,
      error: 'رمز QR غير صالح أو تالف'
    };
  }
  
  if (decodedData.invitationId !== expectedInvitationId) {
    return {
      valid: false,
      error: 'رمز QR لا ينتمي لهذه الدعوة'
    };
  }
  
  // التحقق من عمر الرمز (صالح لمدة 30 يوم)
  const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
  const currentTime = Date.now();
  
  if (currentTime - decodedData.timestamp > thirtyDaysInMs) {
    return {
      valid: false,
      error: 'رمز QR منتهي الصلاحية'
    };
  }
  
  return {
    valid: true,
    data: decodedData
  };
}

/**
 * إنشاء رابط مشاركة للدعوة مع QR Code
 * @param {string} invitationId - معرف الدعوة
 * @param {string} baseUrl - الرابط الأساسي للموقع
 * @returns {string} رابط المشاركة
 */
export function generateInvitationShareLink(invitationId, baseUrl = '') {
  return `${baseUrl}/invitation/${invitationId}`;
}

/**
 * إنتاج QR Code لرابط الدعوة (للمشاركة العامة)
 * @param {string} invitationId - معرف الدعوة
 * @param {string} baseUrl - الرابط الأساسي
 * @returns {Promise<string>} صورة QR Code للرابط
 */
export async function generateInvitationLinkQR(invitationId, baseUrl = '') {
  const shareLink = generateInvitationShareLink(invitationId, baseUrl);
  
  return await generateQRCodeImage(shareLink, {
    width: 200,
    color: {
      dark: '#2d3748',
      light: '#ffffff'
    }
  });
}

/**
 * تنسيق معلومات الضيف لعرضها في البطاقة
 * @param {object} guestData - بيانات الضيف
 * @param {object} invitationData - بيانات الدعوة
 * @returns {object} البيانات المنسقة
 */
export function formatGuestCardData(guestData, invitationData) {
  return {
    guestName: guestData.name || 'ضيف كريم',
    guestPhone: guestData.phone,
    eventTitle: invitationData.title,
    eventDate: new Date(invitationData.event_date).toLocaleDateString('ar-SA'),
    eventTime: new Date(invitationData.event_date).toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit'
    }),
    eventLocation: invitationData.location,
    qrCode: guestData.qr_code,
    status: guestData.status,
    confirmedAt: guestData.updated_at
  };
}
