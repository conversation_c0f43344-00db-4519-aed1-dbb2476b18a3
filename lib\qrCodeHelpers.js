import QRCode from 'qrcode';

/**
 * إنشاء QR Code للضيف
 * @param {string} invitationId - معرف الدعوة
 * @param {string} guestPhone - رقم هاتف الضيف
 * @param {string} guestName - اسم الضيف
 * @returns {string} QR Code مُشفر
 */
export const generateGuestQRCode = (invitationId, guestPhone, guestName = '') => {
  const qrData = {
    id: crypto.randomUUID(),
    invitationId: invitationId,
    phone: guestPhone,
    name: guestName,
    timestamp: Date.now(),
    type: 'guest_entry',
    version: '2.0'
  };

  // تشفير البيانات
  const encodedData = btoa(JSON.stringify(qrData));
  return encodedData;
};

/**
 * إنشاء صورة QR Code
 * @param {string} qrCodeData - بيانات QR Code
 * @param {Object} options - خيارات التصميم
 * @returns {Promise<string>} صورة QR Code كـ Data URL
 */
export const generateQRCodeImage = async (qrCodeData, options = {}) => {
  const defaultOptions = {
    width: 256,
    margin: 2,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    },
    errorCorrectionLevel: 'M'
  };

  const finalOptions = { ...defaultOptions, ...options };

  try {
    const qrCodeDataURL = await QRCode.toDataURL(qrCodeData, finalOptions);
    return qrCodeDataURL;
  } catch (error) {
    console.error('Error generating QR code image:', error);
    throw new Error('فشل في إنتاج صورة QR Code');
  }
};

/**
 * التحقق من صحة QR Code للضيف
 * @param {string} qrCodeData - بيانات QR Code المُشفرة
 * @param {string} expectedInvitationId - معرف الدعوة المتوقع
 * @returns {Object|null} بيانات الضيف أو null إذا كان غير صحيح
 */
export const validateGuestQRCode = (qrCodeData, expectedInvitationId) => {
  try {
    // فك تشفير البيانات
    const decodedData = JSON.parse(atob(qrCodeData));
    
    // التحقق من صحة البيانات
    if (!decodedData.type || decodedData.type !== 'guest_entry') {
      return null;
    }
    
    if (!decodedData.invitationId || decodedData.invitationId !== expectedInvitationId) {
      return null;
    }
    
    if (!decodedData.phone) {
      return null;
    }
    
    // التحقق من انتهاء الصلاحية (24 ساعة)
    const currentTime = Date.now();
    const qrTime = decodedData.timestamp;
    const timeDiff = currentTime - qrTime;
    const twentyFourHours = 24 * 60 * 60 * 1000;
    
    if (timeDiff > twentyFourHours) {
      throw new Error('انتهت صلاحية رمز QR');
    }
    
    return {
      id: decodedData.id,
      invitationId: decodedData.invitationId,
      phone: decodedData.phone,
      name: decodedData.name || '',
      timestamp: decodedData.timestamp,
      isValid: true
    };
    
  } catch (error) {
    console.error('Error validating QR code:', error);
    return null;
  }
};

/**
 * إنشاء QR Code للتحميل
 * @param {string} qrCodeData - بيانات QR Code
 * @param {string} fileName - اسم الملف
 * @param {Object} options - خيارات التصميم
 */
export const downloadQRCode = async (qrCodeData, fileName = 'qr-code', options = {}) => {
  try {
    const qrImage = await generateQRCodeImage(qrCodeData, {
      width: 512,
      ...options
    });
    
    const link = document.createElement('a');
    link.download = `${fileName}.png`;
    link.href = qrImage;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading QR code:', error);
    throw new Error('فشل في تحميل QR Code');
  }
};

/**
 * إنشاء QR Code متقدم مع تصميم مخصص
 * @param {Object} guestData - بيانات الضيف
 * @param {Object} invitationData - بيانات الدعوة
 * @param {Object} designOptions - خيارات التصميم
 * @returns {Promise<string>} صورة QR Code مخصصة
 */
export const generateCustomQRCode = async (guestData, invitationData, designOptions = {}) => {
  const qrData = generateGuestQRCode(
    invitationData.id,
    guestData.phone,
    guestData.name
  );
  
  const customOptions = {
    width: 400,
    margin: 3,
    color: {
      dark: designOptions.primaryColor || '#1a365d',
      light: designOptions.backgroundColor || '#ffffff'
    },
    errorCorrectionLevel: 'H',
    ...designOptions
  };
  
  return await generateQRCodeImage(qrData, customOptions);
};
