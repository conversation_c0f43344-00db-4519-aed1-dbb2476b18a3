import { NextResponse } from 'next/server';
import { supabase } from '../../../../lib/supabase';

export async function POST(request) {
  try {
    const { name, phone, password, role } = await request.json();

    if (!name || !phone || !password || !role) {
      return NextResponse.json({
        success: false,
        error: 'جميع الحقول مطلوبة',
      }, { status: 400 });
    }

    // التحقق من وجود المستخدم مسبقاً
    const { data: existingUser, error: existingUserError } = await supabase
      .from('users')
      .select('*')
      .eq('phone', phone)
      .single();

    if (existingUserError && existingUserError.code !== 'PGRST116') {
      return NextResponse.json({
        success: false,
        error: 'خطأ في قاعدة البيانات',
        details: existingUserError.message,
      }, { status: 500 });
    }

    if (existingUser) {
      return NextResponse.json({
        success: false,
        error: 'رقم الهاتف مستخدم بالفعل',
      }, { status: 409 });
    }

    // إنشاء المستخدم الجديد
    const { data: newUser, error: insertError } = await supabase
      .from('users')
      .insert([{ name, phone, password, role }])
      .select()
      .single();

    if (insertError) {
      return NextResponse.json({
        success: false,
        error: 'فشل في إنشاء المستخدم',
        details: insertError.message,
      }, { status: 500 });
    }

    // إرجاع بيانات المستخدم بدون كلمة المرور
    const { password: _, ...userWithoutPassword } = newUser;

    return NextResponse.json({
      success: true,
      user: userWithoutPassword,
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ غير متوقع',
      details: error.message,
    }, { status: 500 });
  }
}
