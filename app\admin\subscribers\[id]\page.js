'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  FiUser, 
  FiMail, 
  FiPhone, 
  FiCalendar, 
  FiClock, 
  FiArrowRight, 
  FiEdit, 
  FiTrash2, 
  FiLock,
  FiUsers,
  FiCheckCircle,
  FiXCircle,
  FiClock as FiPending,
  FiBarChart2,
  FiActivity,
  FiRefreshCw
} from 'react-icons/fi';
import Header from '@/components/Header';
import dynamic from 'next/dynamic';

// استيراد مكتبة ApexCharts بشكل ديناميكي لتجنب مشاكل SSR
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

export default function SubscriberDetails({ params }) {
  const subscriberId = params.id;
  const [subscriber, setSubscriber] = useState(null);
  const [invitations, setInvitations] = useState([]);
  const [stats, setStats] = useState({
    totalInvitations: 0,
    totalGuests: 0,
    totalAttendance: 0,
    acceptedGuests: 0,
    declinedGuests: 0,
    pendingGuests: 0,
    responseRate: 0,
    attendanceRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [chartData, setChartData] = useState({
    guestResponses: [],
    invitationsOverTime: []
  });
  const router = useRouter();

  useEffect(() => {
    const checkUser = async () => {
      try {
        const userData = localStorage.getItem('user');
        if (!userData) {
          router.push('/login');
          return;
        }
        
        const parsedUser = JSON.parse(userData);
        if (parsedUser.role !== 'admin') {
          router.push('/login');
          return;
        }
        
        fetchSubscriberData();
      } catch (error) {
        console.error('Error checking user:', error);
        router.push('/login');
      }
    };

    checkUser();
  }, [router, subscriberId]);

  const fetchSubscriberData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // جلب بيانات المشترك
      const { data: subscriberData, error: subscriberError } = await supabase
        .from('users')
        .select('*')
        .eq('id', subscriberId)
        .eq('role', 'subscriber')
        .single();
        
      if (subscriberError) throw subscriberError;
      if (!subscriberData) throw new Error('لم يتم العثور على المشترك');
      
      setSubscriber(subscriberData);
      
      // جلب دعوات المشترك
      const { data: invitationsData, error: invitationsError } = await supabase
        .from('invitations')
        .select('*')
        .eq('user_id', subscriberId)
        .order('created_at', { ascending: false });
        
      if (invitationsError) throw invitationsError;
      
      setInvitations(invitationsData || []);
      
      // جلب إحصائيات المدعوين
      const { data: guestsData, error: guestsError } = await supabase
        .from('guests')
        .select('*')
        .eq('user_id', subscriberId);
        
      if (guestsError) throw guestsError;
      
      const guests = guestsData || [];
      const acceptedGuests = guests.filter(guest => guest.status === 'accepted');
      const declinedGuests = guests.filter(guest => guest.status === 'declined');
      const pendingGuests = guests.filter(guest => guest.status === 'pending');
      const attendedGuests = guests.filter(guest => guest.attended);
      
      const responseRate = guests.length > 0 
        ? Math.round(((acceptedGuests.length + declinedGuests.length) / guests.length) * 100) 
        : 0;
        
      const attendanceRate = acceptedGuests.length > 0 
        ? Math.round((attendedGuests.length / acceptedGuests.length) * 100) 
        : 0;
      
      setStats({
        totalInvitations: invitationsData?.length || 0,
        totalGuests: guests.length,
        totalAttendance: attendedGuests.length,
        acceptedGuests: acceptedGuests.length,
        declinedGuests: declinedGuests.length,
        pendingGuests: pendingGuests.length,
        responseRate,
        attendanceRate
      });
      
      // إعداد بيانات الرسوم البيانية
      setChartData({
        guestResponses: {
          options: {
            chart: {
              type: 'pie',
              toolbar: {
                show: false
              }
            },
            labels: ['قبول', 'رفض', 'معلق'],
            colors: ['#10B981', '#EF4444', '#F59E0B'],
            legend: {
              position: 'bottom',
              fontFamily: 'Tajawal, sans-serif',
            },
            responsive: [{
              breakpoint: 480,
              options: {
                chart: {
                  width: 200
                },
                legend: {
                  position: 'bottom'
                }
              }
            }],
            dataLabels: {
              enabled: true,
              formatter: function(val) {
                return Math.round(val) + '%';
              },
              style: {
                fontFamily: 'Tajawal, sans-serif',
              }
            },
            tooltip: {
              y: {
                formatter: function(val) {
                  return val + ' مدعو';
                }
              }
            }
          },
          series: [
            stats.acceptedGuests,
            stats.declinedGuests,
            stats.pendingGuests
          ]
        },
        invitationsOverTime: {
          options: {
            chart: {
              type: 'area',
              toolbar: {
                show: false
              },
              fontFamily: 'Tajawal, sans-serif',
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              curve: 'smooth',
              width: 2
            },
            fill: {
              type: 'gradient',
              gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
              }
            },
            xaxis: {
              type: 'datetime',
              categories: invitations.map(inv => new Date(inv.created_at).getTime()).sort(),
              labels: {
                formatter: function(val) {
                  return new Date(val).toLocaleDateString('ar-SA');
                },
                style: {
                  fontFamily: 'Tajawal, sans-serif',
                }
              }
            },
            yaxis: {
              labels: {
                formatter: function(val) {
                  return Math.round(val);
                },
                style: {
                  fontFamily: 'Tajawal, sans-serif',
                }
              }
            },
            tooltip: {
              x: {
                format: 'dd/MM/yy'
              }
            },
            colors: ['#6366F1']
          },
          series: [{
            name: 'الدعوات',
            data: invitations.map((inv, index) => index + 1).reverse()
          }]
        }
      });
      
      setLoading(false);
    } catch (error) {
      console.error('Error fetching subscriber data:', error);
      setError(error.message || 'حدث خطأ أثناء جلب البيانات');
      setLoading(false);
    }
  };

  const handleResetPassword = async () => {
    try {
      // تنفيذ إعادة تعيين كلمة المرور
      const newPassword = Math.random().toString(36).slice(-8); // كلمة مرور عشوائية
      
      const { error } = await supabase
        .from('users')
        .update({ password: newPassword })
        .eq('id', subscriberId);
        
      if (error) throw error;
      
      alert(`تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة هي: ${newPassword}`);
    } catch (error) {
      console.error('Error resetting password:', error);
      alert('حدث خطأ أثناء إعادة تعيين كلمة المرور');
    }
  };

  if (loading) {
    return (
      <>
        <Header userRole="admin" />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Header userRole="admin" />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
            <div className="text-red-500 text-5xl mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">حدث خطأ</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="flex justify-center space-x-4 space-x-reverse">
              <button
                onClick={() => router.push('/admin/subscribers')}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
              >
                العودة للقائمة
              </button>
              <button
                onClick={fetchSubscriberData}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header userRole="admin" />
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* رأس الصفحة */}
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl shadow-xl p-6 mb-8 text-white">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div className="flex items-center">
                <div className="bg-white rounded-full p-3 shadow-md">
                  <FiUser className="h-8 w-8 text-indigo-600" />
                </div>
                <div className="mr-4">
                  <h1 className="text-3xl font-bold">{subscriber.name || 'مشترك بدون اسم'}</h1>
                  <p className="text-indigo-100 flex items-center">
                    <FiCalendar className="inline ml-1" /> مشترك منذ {new Date(subscriber.created_at).toLocaleDateString('ar-SA')}
                  </p>
                </div>
              </div>
              <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
                <Link
                  href={`/admin/subscribers/edit/${subscriberId}`}
                  className="bg-white text-indigo-700 hover:bg-indigo-50 px-4 py-2 rounded-lg flex items-center transition-colors"
                >
                  <FiEdit className="ml-1" /> تعديل
                </Link>
                <button
                  onClick={handleResetPassword}
                  className="bg-white text-indigo-700 hover:bg-indigo-50 px-4 py-2 rounded-lg flex items-center transition-colors"
                >
                  <FiLock className="ml-1" /> إعادة تعيين كلمة المرور
                </button>
                <Link
                  href="/admin/subscribers"
                  className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
                >
                  <FiArrowRight className="ml-1" /> العودة للقائمة
                </Link>
              </div>
            </div>
          </div>

          {/* محتوى الصفحة */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* القسم الأول: معلومات المشترك */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
                  <h2 className="text-xl font-bold text-gray-800">معلومات المشترك</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">الاسم</p>
                      <p className="font-medium">{subscriber.name || 'غير محدد'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">البريد الإلكتروني</p>
                      <div className="flex items-center">
                        <FiMail className="text-gray-400 ml-2" />
                        <p className="font-medium">{subscriber.email || 'غير محدد'}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">رقم الهاتف</p>
                      <div className="flex items-center">
                        <FiPhone className="text-gray-400 ml-2" />
                        <p className="font-medium">{subscriber.phone || 'غير محدد'}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">تاريخ التسجيل</p>
                      <div className="flex items-center">
                        <FiCalendar className="text-gray-400 ml-2" />
                        <p className="font-medium">{new Date(subscriber.created_at).toLocaleDateString('ar-SA')}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">آخر تسجيل دخول</p>
                      <div className="flex items-center">
                        <FiClock className="text-gray-400 ml-2" />
                        <p className="font-medium">
                          {subscriber.last_login 
                            ? new Date(subscriber.last_login).toLocaleDateString('ar-SA') + ' ' + new Date(subscriber.last_login).toLocaleTimeString('ar-SA')
                            : 'لم يسجل الدخول بعد'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* إحصائيات المشترك */}
              <div className="bg-white rounded-xl shadow-md overflow-hidden mt-6">
                <div className="border-b border-gray-200 bg-gray-50 px-6 py-4 flex justify-between items-center">
                  <h2 className="text-xl font-bold text-gray-800">إحصائيات المشترك</h2>
                  <button
                    onClick={fetchSubscriberData}
                    className="text-indigo-600 hover:text-indigo-800 flex items-center text-sm"
                  >
                    <FiRefreshCw className="ml-1" /> تحديث
                  </button>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="bg-indigo-50 rounded-lg p-4 text-center">
                      <p className="text-3xl font-bold text-indigo-700">{stats.totalInvitations}</p>
                      <p className="text-sm text-gray-600">إجمالي الدعوات</p>
                    </div>
                    <div className="bg-purple-50 rounded-lg p-4 text-center">
                      <p className="text-3xl font-bold text-purple-700">{stats.totalGuests}</p>
                      <p className="text-sm text-gray-600">إجمالي المدعوين</p>
                    </div>
                    <div className="bg-green-50 rounded-lg p-4 text-center">
                      <p className="text-3xl font-bold text-green-700">{stats.acceptedGuests}</p>
                      <p className="text-sm text-gray-600">قبول الدعوة</p>
                    </div>
                    <div className="bg-red-50 rounded-lg p-4 text-center">
                      <p className="text-3xl font-bold text-red-700">{stats.declinedGuests}</p>
                      <p className="text-sm text-gray-600">اعتذار</p>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium text-gray-700">معدل الاستجابة</span>
                        <span className="text-sm font-medium text-gray-700">{stats.responseRate}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="bg-indigo-600 h-2.5 rounded-full" style={{ width: `${stats.responseRate}%` }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium text-gray-700">معدل الحضور</span>
                        <span className="text-sm font-medium text-gray-700">{stats.attendanceRate}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="bg-green-600 h-2.5 rounded-full" style={{ width: `${stats.attendanceRate}%` }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* القسم الثاني: الرسوم البيانية والإحصائيات */}
            <div className="lg:col-span-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {/* رسم بياني لتوزيع حالات المدعوين */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
                    <h2 className="text-xl font-bold text-gray-800">توزيع حالات المدعوين</h2>
                  </div>
                  <div className="p-4">
                    {typeof window !== 'undefined' && stats.totalGuests > 0 ? (
                      <Chart
                        options={chartData.guestResponses.options}
                        series={chartData.guestResponses.series}
                        type="pie"
                        height={300}
                      />
                    ) : (
                      <div className="h-[300px] flex items-center justify-center">
                        <p className="text-gray-500">لا توجد بيانات كافية لعرض الرسم البياني</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* رسم بياني لتطور الدعوات مع الوقت */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
                    <h2 className="text-xl font-bold text-gray-800">تطور الدعوات مع الوقت</h2>
                  </div>
                  <div className="p-4">
                    {typeof window !== 'undefined' && invitations.length > 0 ? (
                      <Chart
                        options={chartData.invitationsOverTime.options}
                        series={chartData.invitationsOverTime.series}
                        type="area"
                        height={300}
                      />
                    ) : (
                      <div className="h-[300px] flex items-center justify-center">
                        <p className="text-gray-500">لا توجد بيانات كافية لعرض الرسم البياني</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* قائمة الدعوات */}
              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="border-b border-gray-200 bg-gray-50 px-6 py-4 flex justify-between items-center">
                  <h2 className="text-xl font-bold text-gray-800">دعوات المشترك</h2>
                  <span className="bg-indigo-100 text-indigo-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    {invitations.length} دعوة
                  </span>
                </div>
                <div className="p-6">
                  {invitations.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 mb-4">
                        <FiMail className="h-6 w-6 text-indigo-600" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد دعوات</h3>
                      <p className="text-gray-500">لم يقم هذا المشترك بإنشاء أي دعوات بعد.</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              عنوان الدعوة
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              تاريخ الحدث
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              المدعوين
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              الحالة
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              التفاصيل
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {invitations.map((invitation, index) => (
                            <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">
                                {invitation.event_title}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-500">
                                {new Date(invitation.event_date).toLocaleDateString('ar-SA')}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-500">
                                {invitation.guests_count}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-500">
                                {invitation.status}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <Link
                                  href={`/admin/invitations/${invitation.id}`}
                                  className="text-indigo-600 hover:text-indigo-800"
                                >
                                  عرض التفاصيل
                                </Link>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
