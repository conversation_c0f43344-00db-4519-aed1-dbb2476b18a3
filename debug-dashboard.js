/**
 * ملف تشخيص مشاكل الداشبورد
 */

import { supabase } from './lib/supabase.js';

async function debugDashboard() {
  console.log('🔍 بدء تشخيص مشاكل الداشبورد...\n');

  try {
    // 1. اختبار الاتصال بقاعدة البيانات
    console.log('1️⃣ اختبار الاتصال بقاعدة البيانات...');
    const { data: testConnection, error: connectionError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.error('❌ فشل الاتصال بقاعدة البيانات:', connectionError);
      return;
    }
    console.log('✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح');

    // 2. فحص جدول المستخدمين
    console.log('\n2️⃣ فحص جدول المستخدمين...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, name, phone, role, is_active')
      .eq('is_active', true)
      .limit(5);

    if (usersError) {
      console.error('❌ خطأ في جلب المستخدمين:', usersError);
      return;
    }

    console.log(`✅ تم العثور على ${users?.length || 0} مستخدم نشط`);
    if (users && users.length > 0) {
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.name} (${user.id}) - ${user.role}`);
      });
    }

    // 3. فحص جدول الدعوات
    console.log('\n3️⃣ فحص جدول الدعوات...');
    const { data: invitations, error: invitationsError } = await supabase
      .from('invitations')
      .select('id, title, user_id, created_at')
      .limit(10);

    if (invitationsError) {
      console.error('❌ خطأ في جلب الدعوات:', invitationsError);
      return;
    }

    console.log(`✅ تم العثور على ${invitations?.length || 0} دعوة`);
    if (invitations && invitations.length > 0) {
      invitations.forEach((inv, index) => {
        console.log(`   ${index + 1}. ${inv.title || 'بدون عنوان'} (${inv.id}) - المستخدم: ${inv.user_id}`);
      });
    }

    // 4. فحص جدول الضيوف
    console.log('\n4️⃣ فحص جدول الضيوف...');
    const { data: guests, error: guestsError } = await supabase
      .from('guests')
      .select('id, name, status, attended, invitation_id')
      .limit(10);

    if (guestsError) {
      console.error('❌ خطأ في جلب الضيوف:', guestsError);
      return;
    }

    console.log(`✅ تم العثور على ${guests?.length || 0} ضيف`);
    if (guests && guests.length > 0) {
      guests.forEach((guest, index) => {
        console.log(`   ${index + 1}. ${guest.name} - الحالة: ${guest.status} - حضر: ${guest.attended} - الدعوة: ${guest.invitation_id}`);
      });
    }

    // 5. اختبار دالة fetchUserStats مع مستخدم حقيقي
    if (users && users.length > 0) {
      const testUser = users[0];
      console.log(`\n5️⃣ اختبار fetchUserStats مع المستخدم: ${testUser.name} (${testUser.id})`);
      
      try {
        // استيراد الدالة
        const { fetchUserStats } = await import('./lib/dashboardHelpers.js');
        
        console.log('📊 جاري جلب إحصائيات المستخدم...');
        const userStats = await fetchUserStats(testUser.id);
        
        console.log('✅ تم جلب الإحصائيات بنجاح:');
        console.log('   - عدد الدعوات:', userStats.stats.invitations);
        console.log('   - عدد المدعوين:', userStats.stats.guests);
        console.log('   - عدد المقبولين:', userStats.stats.accepted);
        console.log('   - عدد المرفوضين:', userStats.stats.declined);
        console.log('   - عدد المنتظرين:', userStats.stats.pending);
        console.log('   - عدد الحاضرين:', userStats.stats.attendance);
        
        console.log('\n📋 تفاصيل الدعوات:');
        userStats.invitations.forEach((inv, index) => {
          console.log(`   ${index + 1}. ${inv.title || 'بدون عنوان'}`);
          console.log(`      - المدعوين: ${inv.guest_count || 0}`);
          console.log(`      - المقبولين: ${inv.accepted_count || 0}`);
          console.log(`      - الحاضرين: ${inv.attended_count || 0}`);
        });
        
      } catch (statsError) {
        console.error('❌ خطأ في fetchUserStats:', statsError);
        console.error('تفاصيل الخطأ:', statsError.message);
        console.error('Stack trace:', statsError.stack);
      }
    }

    // 6. فحص localStorage (إذا كان متاحاً)
    console.log('\n6️⃣ فحص localStorage...');
    if (typeof window !== 'undefined' && window.localStorage) {
      const userData = localStorage.getItem('user');
      if (userData) {
        try {
          const user = JSON.parse(userData);
          console.log('✅ بيانات المستخدم في localStorage:', user);
        } catch (parseError) {
          console.error('❌ خطأ في تحليل بيانات localStorage:', parseError);
        }
      } else {
        console.log('⚠️ لا توجد بيانات مستخدم في localStorage');
      }
    } else {
      console.log('⚠️ localStorage غير متاح (بيئة الخادم)');
    }

    console.log('\n✅ انتهى التشخيص بنجاح!');

  } catch (error) {
    console.error('❌ خطأ عام في التشخيص:', error);
    console.error('تفاصيل الخطأ:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// تشغيل التشخيص
debugDashboard();
