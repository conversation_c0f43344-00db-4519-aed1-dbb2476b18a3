# نظام إدارة الدعوات

نظام متكامل لإدارة الدعوات والمناسبات مبني بـ Next.js و Supabase.

## المميزات

- 🎉 إنشاء وإدارة الدعوات المخصصة
- 👥 إدارة قوائم المدعوين
- 📱 إرسال دعوات عبر SMS و WhatsApp
- 📊 تتبع الردود والحضور
- 🎨 قوالب دعوات متنوعة
- 📈 تقارير وإحصائيات مفصلة

## التقنيات المستخدمة

- **Frontend**: Next.js 14, React 18, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Custom authentication system
- **SMS/WhatsApp**: Twilio
- **UI Components**: Headless UI, Fr<PERSON>r Motion
- **Icons**: React Icons

## البدء السريع

### 1. تثبيت المتطلبات

```bash
npm install
# أو
yarn install
```

### 2. إعداد متغيرات البيئة

أنشئ ملف `.env.local` وأضف المتغيرات التالية:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_ACCOUNT_TOKEN=your_twilio_auth_token
TWILIO_WEDDING_TEMPLATE_SID=your_twilio_template_sid
```

### 3. إعداد قاعدة البيانات

#### الطريقة السريعة:

1. شغل الخادم: `npm run dev`
2. اذهب إلى: `http://localhost:3000/setup`
3. اضغط "فحص وإعداد قاعدة البيانات"

#### الطريقة اليدوية:

1. اذهب إلى لوحة تحكم Supabase
2. افتح SQL Editor
3. انسخ والصق محتوى ملف `database-setup.sql`
4. اضغط Run

### 4. تشغيل الخادم

```bash
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح.

## بيانات تسجيل الدخول للاختبار

### حساب المدير:

- رقم الهاتف: `**********`
- كلمة المرور: `admin123`

### حساب المستخدم:

- رقم الهاتف: `**********`
- كلمة المرور: `password123`

## حل المشاكل

### مشكلة "بيانات الدخول غير صحيحة"

إذا واجهت هذا الخطأ:

1. **تحقق من قاعدة البيانات**: اذهب إلى `/test-connection`
2. **أعد إعداد قاعدة البيانات**: اذهب إلى `/setup`
3. **راجع المساعدة**: اذهب إلى `/help`

### خطوات التشخيص:

1. **اختبار الاتصال**: `http://localhost:3000/test-connection`
2. **إعداد قاعدة البيانات**: `http://localhost:3000/setup`
3. **مركز المساعدة**: `http://localhost:3000/help`

## بنية المشروع

```
├── app/                    # صفحات Next.js (App Router)
│   ├── api/               # API endpoints
│   ├── admin/             # صفحات المدير
│   ├── subscriber/        # صفحات المستخدم
│   ├── login/             # صفحة تسجيل الدخول
│   ├── setup/             # صفحة إعداد قاعدة البيانات
│   └── help/              # مركز المساعدة
├── components/            # مكونات React قابلة للإعادة
├── lib/                   # مكتبات ومساعدات
├── hooks/                 # React hooks مخصصة
├── database/              # ملفات قاعدة البيانات
└── public/                # ملفات ثابتة
```

## API Endpoints

- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/register` - إنشاء حساب جديد
- `GET /api/setup-db` - إعداد قاعدة البيانات
- `GET /api/check-connection` - فحص الاتصال بقاعدة البيانات
- `POST /api/send-whatsapp` - إرسال رسائل WhatsApp

## المساهمة

1. Fork المشروع
2. أنشئ branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. افتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.
