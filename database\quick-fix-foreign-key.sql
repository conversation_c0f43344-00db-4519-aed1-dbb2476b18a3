-- إصلاح سريع لمشكلة foreign key constraint
-- تشغيل هذا السكريبت سيحل المشكلة فوراً

-- 1. حذف foreign key constraint الحالي
ALTER TABLE public.invitations DROP CONSTRAINT IF EXISTS fk_user;
ALTER TABLE public.invitations DROP CONSTRAINT IF EXISTS invitations_user_id_fkey;

-- 2. إضافة foreign key constraint الصحيح
ALTER TABLE public.invitations 
ADD CONSTRAINT fk_invitations_user_id 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- 3. التحقق من أن الجدولين موجودان
SELECT 
    'Users table exists: ' || CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN 'YES' ELSE 'NO' END as users_status,
    'Invitations table exists: ' || CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invitations' AND table_schema = 'public') THEN 'YES' ELSE 'NO' END as invitations_status;

-- 4. عرض foreign keys الحالية
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name='invitations'
    AND tc.table_schema='public';
