import { NextResponse } from "next/server";
import { supabase } from "../../../lib/supabase";

export async function GET() {
  try {
    console.log("Starting database setup...");

    // Check Supabase connection first
    if (!supabase) {
      return NextResponse.json({
        success: false,
        message: "Failed to connect to the database - check environment variables",
        error: "Supabase client not initialized",
      });
    }

    const results = [];

    // 1. Check and advise on users table creation
    try {
      const { data: usersData, error: usersError } = await supabase
        .from("users")
        .select("count(*)", { count: "exact", head: true });

      if (usersError && usersError.message.includes("does not exist")) {
        console.log(
          "Users table does not exist. It needs to be created manually in the Supabase dashboard with 'id (UUID primary key), name (text), phone (text unique), password (text), role (text with default 'subscriber')'."
        );
        results.push({
          table: "users",
          status: "needs_manual_creation",
          message: "Users table needs to be created manually in the Supabase dashboard.",
        });
      } else if (usersError) {
        results.push({
          table: "users",
          status: "error",
          message: `Error checking users table: ${usersError.message}`,
        });
      } else {
        results.push({
          table: "users",
          status: "exists",
          message: "Users table exists.",
        });
      }
    } catch (error) {
      results.push({
        table: "users",
        status: "error",
        message: `Error during users table check: ${error.message}`,
      });
    }

    // 2. Add test users if they don't exist
    try {
      const testUsers = [
        {
          name: "مدير النظام",
          phone: "0500000001",
          password: "admin123",
          role: "admin",
        },
        {
          name: "مستخدم تجريبي",
          phone: "0500000002",
          password: "password123",
          role: "subscriber",
        },
      ];

      for (const user of testUsers) {
        const { data: existingUsers, error: checkError } = await supabase
          .from("users")
          .select("phone")
          .eq("phone", user.phone)
          .single(); // Use single to get one record or null

        if (checkError && checkError.code === 'PGRST116') { // No rows found error code
            // User does not exist, insert them
            const { data: insertData, error: insertError } = await supabase
                .from("users")
                .insert([user])
                .select();

            if (insertError) {
                results.push({
                    table: "users",
                    status: "insert_error",
                    message: `Failed to add user ${user.phone}: ${insertError.message}`,
                });
            } else {
                results.push({
                    table: "users",
                    status: "user_created",
                    message: `User ${user.phone} created successfully.`,
                });
            }
        } else if (existingUsers) {
          results.push({
            table: "users",
            status: "user_exists",
            message: `User ${user.phone} already exists.`,
          });
        } else if (checkError) {
            results.push({
                table: "users",
                status: "check_error",
                message: `Failed to check for user ${user.phone}: ${checkError.message}`,
            });
        }
      }
    } catch (error) {
      results.push({
        table: "users",
        status: "error",
        message: `Error in user management: ${error.message}`,
      });
    }

    // Add new columns to the invitations table if they don't exist
    // This assumes 'templates' table and its 'id' column already exist or will be created.
    const columnsToAdd = [
        { name: 'template_id', type: 'UUID REFERENCES templates(id)' }, // Assuming template_id is UUID
        { name: 'additional_data', type: 'JSONB DEFAULT \'{}\'::jsonb' },
        { name: 'message', type: 'TEXT' }
    ];

    for (const column of columnsToAdd) {
        const { error: columnError } = await supabase.rpc("execute_sql", {
            sql_query: `
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns
                        WHERE table_name = 'invitations' AND column_name = '${column.name}'
                    ) THEN
                        ALTER TABLE invitations
                        ADD COLUMN ${column.name} ${column.type};
                    END IF;
                END $$;
            `,
        });

        if (columnError) {
            console.error(`Error adding column ${column.name}:`, columnError);
            results.push({
                table: "invitations",
                status: "column_add_error",
                message: `Failed to add column '${column.name}': ${columnError.message}`,
            });
        } else {
            results.push({
                table: "invitations",
                status: "column_added_or_exists",
                message: `Column '${column.name}' checked/added successfully.`,
            });
        }
    }

    return NextResponse.json({
      success: true,
      message: "Database setup process completed.",
      results, // Include the detailed results for each step
    });
  } catch (error) {
    console.error("Error in database setup:", error);
    return NextResponse.json({
      success: false,
      message: "An error occurred during database setup.",
      error: error.message,
    });
  }
}