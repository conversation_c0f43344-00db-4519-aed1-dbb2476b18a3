import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function GET(request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // جلب الإشعارات الخاصة بطلبات تفعيل الحسابات
    const { data: notifications, error } = await supabase
      .from("notifications")
      .select("id, user_id, type, created_at, message")
      .eq("type", "activation_request")
      .order("created_at", { ascending: false });
    if (error) throw error;

    // جلب بيانات المستخدمين المرتبطين بالإشعارات دفعة واحدة
    const userIds = notifications.map((n) => n.user_id);
    let usersData = [];
    if (userIds.length > 0) {
      const { data: users, error: usersError } = await supabase
        .from("users")
        .select("id, name, phone")
        .in("id", userIds);
      if (usersError) throw usersError;
      usersData = users;
    }

    // ربط بيانات المستخدمين بالإشعارات
    const formattedNotifications = notifications.map((notification) => {
      const user = usersData.find((u) => u.id === notification.user_id) || {};
      return {
        id: notification.id,
        userId: notification.user_id,
        type: notification.type,
        createdAt: notification.created_at,
        userName: user.name,
        userPhone: user.phone,
        message: notification.message,
      };
    });

    return NextResponse.json({
      success: true,
      notifications: formattedNotifications,
    });
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return NextResponse.json(
      {
        success: false,
        error: "حدث خطأ أثناء جلب الإشعارات",
      },
      { status: 500 }
    );
  }
}
