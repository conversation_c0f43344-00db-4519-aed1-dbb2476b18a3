// pages/api/send-invitation.js
import { supabase } from '@/lib/supabase';
import { sendWhatsAppInvitationWithTemplate } from '@/lib/twilio';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { invitationId, attendeeIds } = req.body;

    if (!invitationId) {
      return res.status(400).json({ error: 'Invitation ID is required' });
    }

    // الحصول على بيانات الدعوة
    const { data: invitation, error: invitationError } = await supabase
      .from('invitations')
      .select('*')
      .eq('id', invitationId)
      .single();
    
    if (invitationError) {
      return res.status(404).json({ error: 'Invitation not found' });
    }

    // الحصول على بيانات المدعوين
    let query = supabase
      .from('attendance')
      .select('*')
      .eq('invitation_id', invitationId);
    
    // إذا تم تحديد مدعوين محددين، نقوم بتصفية النتائج
    if (attendeeIds && attendeeIds.length > 0) {
      query = query.in('id', attendeeIds);
    }
    
    const { data: attendees, error: attendeesError } = await query;
    
    if (attendeesError) {
      return res.status(500).json({ error: 'Error fetching attendees' });
    }

    // إرسال الدعوات لكل مدعو
    const results = [];
    for (const attendee of attendees) {
      // إرسال الدعوة عبر واتساب
      const result = await sendWhatsAppInvitationWithTemplate(attendee.phone, {
        id: invitation.id,
        title: invitation.title,
        event_date: invitation.event_date,
        location: invitation.location,
        message: invitation.message || invitation.description
      });
      
      results.push({
        attendeeId: attendee.id,
        phone: attendee.phone,
        success: result.success,
        error: result.error
      });
      
      // تحديث حالة الإرسال في قاعدة البيانات
      if (result.success) {
        await supabase
          .from('attendance')
          .update({ status: 'sent' })
          .eq('id', attendee.id);
      }
    }
    
    // تحديث حالة الدعوة إلى "مرسلة" إذا تم إرسال جميع الدعوات
    if (results.every(r => r.success)) {
      await supabase
        .from('invitations')
        .update({ status: 'sent' })
        .eq('id', invitationId);
    }

    return res.status(200).json({ success: true, results });
  } catch (error) {
    console.error('Error sending invitations:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

