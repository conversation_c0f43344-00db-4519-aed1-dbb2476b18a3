'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import { createAndSendInvitation } from '@/app/actions';
import { FiCalendar, FiClock, FiMapPin, FiMessageSquare, FiUser, FiUsers, FiSend, FiCheck, FiEdit, FiSmartphone } from 'react-icons/fi';

export default function CreateInvitationPage() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [title, setTitle] = useState('حفل زفاف');
  const [date, setDate] = useState('');
  const [time, setTime] = useState('');
  const [location, setLocation] = useState('');
  const [message, setMessage] = useState('');
  const [groomName, setGroomName] = useState('أحمد محمد عبد الرحمن');
  const [brideName, setBrideName] = useState('سارة مصطفى حسن');
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [shareLink, setShareLink] = useState('');
  const [invitationId, setInvitationId] = useState('');

  useEffect(() => {
    // التحقق من وجود المستخدم في localStorage
    const userData = localStorage.getItem('user');
    if (!userData) {
      router.push('/login');
      return;
    }
    
    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/login');
    }
  }, [router]);

  // تنسيق التاريخ بالعربية
  const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('ar-SA', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    } catch (error) {
      return '';
    }
  };

  // تنسيق الوقت بالعربية
  const formatTime = (timeString) => {
    if (!timeString) return '';
    try {
      const [hours, minutes] = timeString.split(':');
      const date = new Date();
      date.setHours(hours);
      date.setMinutes(minutes);
      return date.toLocaleTimeString('ar-SA', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } catch (error) {
      return '';
    }
  };

  // إنشاء نص المعاينة
  const previewText = `عزيزنا الضيف الكريم،

بكل حب ومودة، ندعوك لمشاركتنا فرحتنا في ${title || 'حفل زفاف'}:
العريس: ${groomName}
إلى كريمتنا: ${brideName}

 التاريخ: ${formatDate(date) || 'سيتم تحديده'}
 الساعة: ${formatTime(time) || 'سيتم تحديدها'}
 المكان: ${location || 'سيتم تحديده'}

${message ? message + '\n' : ''}
حضوركم سعدنا ويُزيد من بهجة مناسبتنا.
للتأكيد على حضوركم، يرجى الضغط على الرابط التالي:

 للاستفسارات، يرجى التواصل معانا عبر المنصة.

${invitationId ? `http://localhost:3000/invitation/${invitationId}` : '[رابط الدعوة سيظهر هنا]'}`;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');
    setSuccess('');
    setShareLink('');

    try {
      // التحقق من إدخال جميع البيانات المطلوبة
      if (!title || !date || !time || !location) {
        setError('يرجى إدخال جميع البيانات المطلوبة');
        setSubmitting(false);
        return;
      }

      if (!user || !user.id) {
        throw new Error('لم يتم العثور على بيانات المستخدم، يرجى تسجيل الدخول مرة أخرى');
      }

      // تنسيق التاريخ والوقت
      const eventDateTime = new Date(`${date}T${time}`);
      
      // إنشاء الدعوة - نستخدم رقم هاتف وهمي لأننا لا نحتاجه الآن
      const { success, data, shareLink: whatsappLink, error: invitationError } = await createAndSendInvitation(
        user.id,
        title,
        eventDateTime.toISOString(),
        location,
        message,
        '+123456789', // رقم هاتف وهمي
        'ضيف', // اسم وهمي
        groomName,
        brideName
      );

      if (!success || invitationError) {
        throw new Error(invitationError || 'حدث خطأ أثناء إنشاء الدعوة');
      }

      setSuccess('تم إنشاء الدعوة بنجاح!');
      setShareLink(whatsappLink);
      setInvitationId(data.id);
      
    } catch (error) {
      console.error('Error creating invitation:', error);
      setError(error.message || 'حدث خطأ أثناء إنشاء الدعوة');
    } finally {
      setSubmitting(false);
    }
  };

  // الحصول على الوقت الحالي بتنسيق 12 ساعة
  const getCurrentTime = () => {
    return new Date().toLocaleTimeString('ar-SA', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  if (!user) {
    return (
      <>
        <Header userName={user?.name} userRole={user?.role} />
        <div className="flex items-center justify-center min-h-screen bg-gray-50">
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header userName={user?.name} userRole={user?.role} />
      <div className="bg-gray-100 min-h-screen py-8">
        <div className="container mx-auto px-4 max-w-6xl">
          <div className="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl shadow-xl p-8 mb-8">
            <div className="flex items-center">
              <div className="bg-white/20 p-3 rounded-full mr-4">
                <FiSend className="text-white text-2xl" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">إنشاء دعوة جديدة</h1>
                <p className="text-purple-100 mt-2">قم بإنشاء دعوة مخصصة وشاركها مع ضيوفك بسهولة</p>
              </div>
            </div>
          </div>
          
          {error && (
            <div className="bg-red-50 border-r-4 border-red-500 text-red-700 p-4 rounded-lg mb-6 flex items-center shadow-sm">
              <svg className="h-6 w-6 text-red-500 ml-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{error}</span>
            </div>
          )}
          
          {success && (
            <div className="bg-green-50 border-r-4 border-green-500 text-green-700 p-4 rounded-lg mb-6 flex items-center shadow-sm">
              <svg className="h-6 w-6 text-green-500 ml-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>{success}</span>
            </div>
          )}
          
          {shareLink && (
            <div className="bg-white border-r-4 border-blue-500 p-8 rounded-xl shadow-lg mb-8 transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="bg-blue-100 p-3 rounded-full mr-4">
                  <FiCheck className="text-blue-600 text-xl" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-800">تم إنشاء الدعوة بنجاح!</h2>
                  <p className="text-gray-600">يمكنك الآن مشاركة الدعوة مع ضيوفك</p>
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
                <p className="text-gray-700 mb-2 font-medium">رابط الدعوة:</p>
                <div className="flex items-center bg-white border border-gray-300 rounded-lg p-2">
                  <span className="text-gray-600 truncate flex-1">
                    {`http://localhost:3000/invitation/${invitationId}`}
                  </span>
                  <button 
                    onClick={() => {
                      navigator.clipboard.writeText(`http://localhost:3000/invitation/${invitationId}`);
                      alert('تم نسخ الرابط!');
                    }}
                    className="bg-gray-200 hover:bg-gray-300 text-gray-700 py-1 px-3 rounded-md text-sm transition-colors duration-200 mr-2"
                  >
                    نسخ
                  </button>
                </div>
              </div>
              <div className="flex flex-wrap gap-4">
                <a 
                  href={shareLink} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg inline-flex items-center transition-colors duration-300 shadow-md"
                >
                  <svg className="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.498 14.382c-.301-.15-1.767-.867-2.04-.966-.273-.101-.473-.15-.673.15-.197.295-.771.964-.944 1.162-.175.195-.349.21-.646.075-.3-.15-1.263-.465-2.403-1.485-.888-.795-1.484-1.77-1.66-2.07-.174-.3-.019-.465.13-.615.136-.135.301-.345.451-.523.146-.181.194-.301.297-.496.1-.21.049-.375-.025-.524-.075-.15-.672-1.62-.922-2.206-.24-.584-.487-.51-.672-.51-.172-.015-.371-.015-.571-.015-.2 0-.523.074-.797.359-.273.3-1.045 1.02-1.045 2.475s1.07 2.865 1.219 3.075c.149.195 2.105 3.195 5.1 4.485.714.3 1.27.48 1.704.629.714.227 1.365.195 1.88.121.574-.091 1.767-.721 2.016-1.426.255-.705.255-1.29.18-1.425-.074-.135-.27-.21-.57-.345z" />
                    <path d="M20.52 3.449C12.831-3.984.106 1.407.101 11.893c0 1.96.5 3.891 1.44 5.592l-1.54 5.616 5.763-1.506c1.631.87 3.471 1.331 5.346 1.335 9.579 0 15.135-7.819 15.14-15.354.004-4.108-1.595-7.961-4.511-10.876-2.919-2.917-6.78-4.521-10.889-4.525H20.52v.015zm-9.399 21.441h-.005c-2.643-.003-5.236-.732-7.484-2.114l-.537-.319-5.562 1.446 1.476-5.385-.345-.546c-1.461-2.325-2.236-5.015-2.236-7.806 0-8.048 6.573-14.61 14.656-14.61 3.915.004 7.595 1.529 10.366 4.295 2.77 2.777 4.295 6.461 4.29 10.382-.004 8.05-6.576 14.616-14.65 14.616l.015-.006z" />
                  </svg>
                  مشاركة عبر واتساب
                </a>
                <button 
                  onClick={() => router.push(`/invitation/${invitationId}`)}
                  className="bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-3 px-6 rounded-lg inline-flex items-center transition-colors duration-300 shadow-md"
                >
                  <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  معاينة الدعوة
                </button>
              </div>
            </div>
          )}
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* قسم الحقول */}
            <div>
              <form onSubmit={handleSubmit} className="bg-white rounded-xl shadow-lg p-8">
                <h2 className="text-xl font-bold text-gray-800 mb-6 border-r-4 border-purple-500 pr-3 flex items-center">
                  <FiEdit className="ml-2 text-purple-500" /> معلومات الدعوة
                </h2>
                
                <div className="mb-6">
                  <label className="block text-gray-700 font-medium mb-2">عنوان الدعوة</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                      <FiMessageSquare />
                    </div>
                    <input
                      type="text"
                      className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="مثال: حفل زفاف"
                      required
                    />
                  </div>
                </div>
                
                <div className="mb-6">
                  <label className="block text-gray-700 font-medium mb-2">اسم العريس</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                      <FiUser />
                    </div>
                    <input
                      type="text"
                      className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      value={groomName}
                      onChange={(e) => setGroomName(e.target.value)}
                      placeholder="اسم العريس"
                      required
                    />
                  </div>
                </div>
                
                <div className="mb-6">
                  <label className="block text-gray-700 font-medium mb-2">اسم العروس</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                      <FiUser />
                    </div>
                    <input
                      type="text"
                      className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      value={brideName}
                      onChange={(e) => setBrideName(e.target.value)}
                      placeholder="اسم العروس"
                      required
                    />
                  </div>
                </div>
                
                <div className="flex flex-wrap -mx-2 mb-6">
                  <div className="w-full md:w-1/2 px-2 mb-6 md:mb-0">
                    <label className="block text-gray-700 font-medium mb-2">التاريخ</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                        <FiCalendar />
                      </div>
                      <input
                        type="date"
                        className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        value={date}
                        onChange={(e) => setDate(e.target.value)}
                        required
                      />
                    </div>
                  </div>
                  <div className="w-full md:w-1/2 px-2">
                    <label className="block text-gray-700 font-medium mb-2">الوقت</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                        <FiClock />
                      </div>
                      <input
                        type="time"
                        className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        value={time}
                        onChange={(e) => setTime(e.target.value)}
                        required
                      />
                    </div>
                  </div>
                </div>
                
                <div className="mb-6">
                  <label className="block text-gray-700 font-medium mb-2">المكان</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                      <FiMapPin />
                    </div>
                    <input
                      type="text"
                      className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      placeholder="مثال: قاعة الأميرات - الرياض"
                      required
                    />
                  </div>
                </div>
                
                <div className="mb-6">
                  <label className="block text-gray-700 font-medium mb-2">رسالة إضافية (اختياري)</label>
                  <textarea
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="أي معلومات إضافية ترغب في إضافتها للدعوة"
                    rows="3"
                  ></textarea>
                </div>
                
                <div className="flex items-center justify-between">
                  <button
                    type="submit"
                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                    disabled={submitting}
                  >
                    {submitting ? 'جاري الإنشاء...' : 'إنشاء الدعوة'}
                  </button>
                </div>
              </form>
            </div>
            
            {/* قسم المعاينة */}
            <div>
              <div className="bg-white rounded-xl shadow-lg p-6 h-full">
                <h2 className="text-xl font-bold text-gray-800 mb-6 border-r-4 border-indigo-500 pr-3 flex items-center">
                  <FiSmartphone className="ml-2 text-indigo-500" /> معاينة الدعوة (واتساب)
                </h2>
                
                <div className="bg-[#e5ddd5] rounded-lg  overflow-y-auto" style={{ backgroundImage: "url('https://i.pinimg.com/originals/97/c0/07/97c00759d90d786d9b6a5a18bd4bcf6f.jpg')", backgroundSize: "cover" }}>
                  <div className="flex items-center bg-[#128C7E] text-white p-2 rounded-t-lg">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center overflow-hidden mr-2">
                      <FiUser className="text-gray-600 text-xl" />
                    </div>
                    <div>
                      <p className="font-medium mx-1 mt-1">أنت</p>
                      <p className="text-xs opacity-80">+966 5xxxxxxxx</p>
                    </div>
                  </div>
                  
                  <div className="py-4 px-2">
                    <div className="bg-white p-3 rounded-lg shadow-sm mb-2 max-w-xs mr-auto">
                      <div className="whitespace-pre-wrap text-gray-800 text-sm">مرحباً! هذه معاينة الدعوة لحضور المناسبة السعيدة</div>
                      <div className="text-right text-xs text-gray-500 mt-1">{getCurrentTime()}</div>
                    </div>
                    
                    <div className="bg-[#dcf8c6] p-3 rounded-lg shadow-sm mb-2 max-w-xs ml-auto">
                      <div className="whitespace-pre-wrap text-gray-800 text-sm">{previewText}</div>
                      <div className="text-right text-xs text-gray-500 mt-1">{getCurrentTime()}</div>
                    </div>
                    
                    {invitationId && (
                      <div className="bg-white p-3 rounded-lg shadow-sm mb-2 max-w-xs mr-auto">
                        <div className="whitespace-pre-wrap text-gray-800 text-sm">شكراً للدعوة! سأحضر بإذن الله</div>
                        <div className="text-right text-xs text-gray-500 mt-1">{getCurrentTime()}</div>
                      </div>
                    )}
                  </div>

                  <div className='flex'>
                  <div className="bg-[#474c51] mx-1 w-[90%] p-2 flex items-center rounded-full">
                    <input type="text" className="flex-1 mx-1 border-0 bg-transparent outline-none" placeholder="اكتب رسالة..." disabled />
                    </div>
                    <button className="w-10 h-10 rounded-full bg-[#128C7E] text-white flex items-center justify-center">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h14M12 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}











