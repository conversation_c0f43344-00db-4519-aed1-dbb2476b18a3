"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  FiBarChart2,
  <PERSON>PieChart,
  FiTrendingUp,
  FiUsers,
  FiMail,
  FiCalendar,
  FiDownload,
  FiRefreshCw,
  FiActivity,
  FiDollarSign,
} from "react-icons/fi";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { supabase } from "@/lib/supabase";
import { getValidatedUser } from "@/lib/userValidation";

export default function AdminAnalytics() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState({
    overview: {
      totalUsers: 0,
      totalInvitations: 0,
      totalGuests: 0,
      totalRevenue: 0,
      growthRate: 0,
    },
    userGrowth: [],
    invitationTrends: [],
    guestAcceptanceRates: [],
    revenueAnalysis: [],
    topPerformers: [],
    systemHealth: {
      activeUsers: 0,
      responseRate: 0,
      conversionRate: 0,
      churnRate: 0,
    },
  });

  useEffect(() => {
    checkUserAndLoadData();
  }, []);

  const checkUserAndLoadData = async () => {
    try {
      setLoading(true);
      
      const validatedUser = await getValidatedUser();
      if (!validatedUser || validatedUser.role !== "admin") {
        router.push("/login");
        return;
      }

      await loadAnalyticsData();
    } catch (error) {
      console.error("Error loading analytics data:", error);
      toast.error("خطأ في تحميل بيانات التحليلات");
    } finally {
      setLoading(false);
    }
  };

  const loadAnalyticsData = async () => {
    try {
      // جلب البيانات الأساسية
      const [usersData, invitationsData, guestsData, subscriptionsData] = await Promise.all([
        supabase.from("users").select("id, created_at, is_active, role"),
        supabase.from("invitations").select("id, created_at, status, user_id"),
        supabase.from("guests").select("id, created_at, status, attended, invitation_id"),
        supabase.from("subscriptions").select("id, monthly_fee, currency, start_date, status, user_id")
      ]);

      if (usersData.error) throw usersData.error;
      if (invitationsData.error) throw invitationsData.error;
      if (guestsData.error) throw guestsData.error;
      if (subscriptionsData.error) throw subscriptionsData.error;

      // معالجة البيانات
      const processedData = processAnalyticsData(
        usersData.data || [],
        invitationsData.data || [],
        guestsData.data || [],
        subscriptionsData.data || []
      );

      setAnalyticsData(processedData);
    } catch (error) {
      console.error("Error loading analytics data:", error);
      throw error;
    }
  };

  const processAnalyticsData = (users, invitations, guests, subscriptions) => {
    // حساب الإحصائيات العامة
    const totalUsers = users.length;
    const totalInvitations = invitations.length;
    const totalGuests = guests.length;
    const totalRevenue = subscriptions
      .filter(s => s.status === 'active')
      .reduce((sum, s) => sum + parseFloat(s.monthly_fee || 0), 0);

    // حساب معدل النمو (آخر 30 يوم مقارنة بالـ 30 يوم السابقة)
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    const recentUsers = users.filter(u => new Date(u.created_at) >= thirtyDaysAgo).length;
    const previousUsers = users.filter(u => 
      new Date(u.created_at) >= sixtyDaysAgo && new Date(u.created_at) < thirtyDaysAgo
    ).length;
    const growthRate = previousUsers > 0 ? ((recentUsers - previousUsers) / previousUsers) * 100 : 0;

    // نمو المستخدمين الشهري (آخر 12 شهر)
    const userGrowth = getMonthlyData(users, 12, 'created_at');

    // اتجاهات الدعوات
    const invitationTrends = getMonthlyData(invitations, 12, 'created_at');

    // معدلات قبول الضيوف
    const guestAcceptanceRates = getMonthlyAcceptanceRates(guests, 12);

    // تحليل الإيرادات
    const revenueAnalysis = getMonthlyRevenue(subscriptions, 12);

    // أفضل المؤدين (المستخدمين الأكثر نشاطاً)
    const topPerformers = getTopPerformers(users, invitations, guests);

    // صحة النظام
    const activeUsers = users.filter(u => u.is_active && u.role === 'subscriber').length;
    const acceptedGuests = guests.filter(g => g.status === 'accepted' || g.status === 'confirmed').length;
    const responseRate = totalGuests > 0 ? (acceptedGuests / totalGuests) * 100 : 0;
    const attendedGuests = guests.filter(g => g.attended === true).length;
    const conversionRate = acceptedGuests > 0 ? (attendedGuests / acceptedGuests) * 100 : 0;
    const activeSubscriptions = subscriptions.filter(s => s.status === 'active').length;
    const churnRate = totalUsers > 0 ? ((totalUsers - activeUsers) / totalUsers) * 100 : 0;

    return {
      overview: {
        totalUsers,
        totalInvitations,
        totalGuests,
        totalRevenue,
        growthRate,
      },
      userGrowth,
      invitationTrends,
      guestAcceptanceRates,
      revenueAnalysis,
      topPerformers,
      systemHealth: {
        activeUsers,
        responseRate,
        conversionRate,
        churnRate,
      },
    };
  };

  const getMonthlyData = (data, months, dateField) => {
    const result = [];
    for (let i = months - 1; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7);
      const count = data.filter(item => item[dateField]?.startsWith(monthKey)).length;
      result.push({
        month: date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' }),
        count: count,
      });
    }
    return result;
  };

  const getMonthlyAcceptanceRates = (guests, months) => {
    const result = [];
    for (let i = months - 1; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7);
      const monthGuests = guests.filter(g => g.created_at?.startsWith(monthKey));
      const accepted = monthGuests.filter(g => g.status === 'accepted' || g.status === 'confirmed').length;
      const total = monthGuests.length;
      const rate = total > 0 ? Math.round((accepted / total) * 100) : 0;
      
      result.push({
        month: date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' }),
        rate: rate,
        total: total,
        accepted: accepted,
      });
    }
    return result;
  };

  const getMonthlyRevenue = (subscriptions, months) => {
    const result = [];
    for (let i = months - 1; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7);
      const monthSubscriptions = subscriptions.filter(s => 
        s.start_date?.startsWith(monthKey) && s.status === 'active'
      );
      const revenue = monthSubscriptions.reduce((sum, s) => sum + parseFloat(s.monthly_fee || 0), 0);
      
      result.push({
        month: date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' }),
        revenue: revenue,
        subscriptions: monthSubscriptions.length,
      });
    }
    return result;
  };

  const getTopPerformers = (users, invitations, guests) => {
    const subscribers = users.filter(u => u.role === 'subscriber');
    const performers = subscribers.map(user => {
      const userInvitations = invitations.filter(inv => inv.user_id === user.id);
      const userGuests = guests.filter(g => 
        userInvitations.some(inv => inv.id === g.invitation_id)
      );
      const acceptedGuests = userGuests.filter(g => g.status === 'accepted' || g.status === 'confirmed');
      const attendedGuests = userGuests.filter(g => g.attended === true);

      return {
        userId: user.id,
        name: user.name || 'مستخدم غير محدد',
        invitations: userInvitations.length,
        guests: userGuests.length,
        accepted: acceptedGuests.length,
        attended: attendedGuests.length,
        score: userInvitations.length + acceptedGuests.length + attendedGuests.length,
      };
    });

    return performers
      .sort((a, b) => b.score - a.score)
      .slice(0, 5);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const exportData = () => {
    const dataStr = JSON.stringify(analyticsData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `analytics-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
    toast.success("تم تصدير البيانات بنجاح");
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">جاري تحميل التحليلات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100" dir="rtl">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-800 mb-2">التحليلات المتقدمة</h1>
            <p className="text-gray-600">تحليلات شاملة وإحصائيات متقدمة للنظام</p>
          </div>
          <div className="flex gap-2">
            <button
              onClick={checkUserAndLoadData}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <FiRefreshCw className="h-4 w-4" />
              تحديث
            </button>
            <button
              onClick={exportData}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <FiDownload className="h-4 w-4" />
              تصدير
            </button>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">إجمالي المستخدمين</p>
                <p className="text-3xl font-bold text-blue-600">{analyticsData.overview.totalUsers}</p>
                <p className={`text-sm ${analyticsData.overview.growthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {analyticsData.overview.growthRate >= 0 ? '+' : ''}{analyticsData.overview.growthRate.toFixed(1)}% نمو
                </p>
              </div>
              <FiUsers className="h-12 w-12 text-blue-500" />
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">إجمالي الدعوات</p>
                <p className="text-3xl font-bold text-green-600">{analyticsData.overview.totalInvitations}</p>
                <p className="text-sm text-blue-600">{analyticsData.systemHealth.activeUsers} مستخدم نشط</p>
              </div>
              <FiMail className="h-12 w-12 text-green-500" />
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">إجمالي الضيوف</p>
                <p className="text-3xl font-bold text-purple-600">{analyticsData.overview.totalGuests}</p>
                <p className="text-sm text-green-600">{analyticsData.systemHealth.responseRate.toFixed(1)}% معدل الاستجابة</p>
              </div>
              <FiActivity className="h-12 w-12 text-purple-500" />
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">الدخل الشهري</p>
                <p className="text-3xl font-bold text-orange-600">{formatCurrency(analyticsData.overview.totalRevenue)}</p>
                <p className="text-sm text-blue-600">{analyticsData.systemHealth.conversionRate.toFixed(1)}% معدل التحويل</p>
              </div>
              <FiDollarSign className="h-12 w-12 text-orange-500" />
            </div>
          </motion.div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* User Growth Chart */}
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <FiTrendingUp className="h-5 w-5 text-blue-500" />
              نمو المستخدمين (12 شهر)
            </h3>
            <div className="h-64 flex items-end justify-between gap-1">
              {analyticsData.userGrowth.map((month, index) => {
                const maxValue = Math.max(...analyticsData.userGrowth.map(m => m.count));
                const height = maxValue > 0 ? Math.max((month.count / maxValue) * 200, 5) : 5;
                return (
                  <div key={index} className="flex flex-col items-center flex-1">
                    <div
                      className="bg-gradient-to-t from-blue-500 to-blue-300 rounded-t w-full transition-all duration-500 hover:from-blue-600 hover:to-blue-400"
                      style={{ height: `${height}px` }}
                      title={`${month.month}: ${month.count} مستخدم`}
                    ></div>
                    <p className="text-xs text-gray-600 mt-2 text-center transform -rotate-45 origin-center">
                      {month.month}
                    </p>
                    <p className="text-xs font-bold text-gray-800">{month.count}</p>
                  </div>
                );
              })}
            </div>
          </motion.div>

          {/* Invitation Trends Chart */}
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <FiMail className="h-5 w-5 text-green-500" />
              اتجاهات الدعوات (12 شهر)
            </h3>
            <div className="h-64 flex items-end justify-between gap-1">
              {analyticsData.invitationTrends.map((month, index) => {
                const maxValue = Math.max(...analyticsData.invitationTrends.map(m => m.count));
                const height = maxValue > 0 ? Math.max((month.count / maxValue) * 200, 5) : 5;
                return (
                  <div key={index} className="flex flex-col items-center flex-1">
                    <div
                      className="bg-gradient-to-t from-green-500 to-green-300 rounded-t w-full transition-all duration-500 hover:from-green-600 hover:to-green-400"
                      style={{ height: `${height}px` }}
                      title={`${month.month}: ${month.count} دعوة`}
                    ></div>
                    <p className="text-xs text-gray-600 mt-2 text-center transform -rotate-45 origin-center">
                      {month.month}
                    </p>
                    <p className="text-xs font-bold text-gray-800">{month.count}</p>
                  </div>
                );
              })}
            </div>
          </motion.div>
        </div>

        {/* Additional Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Guest Acceptance Rates */}
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <FiPieChart className="h-5 w-5 text-purple-500" />
              معدلات القبول
            </h3>
            <div className="space-y-3">
              {analyticsData.guestAcceptanceRates.slice(-6).map((month, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{month.month}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${month.rate}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-bold text-gray-800">{month.rate}%</span>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Revenue Analysis */}
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <FiBarChart2 className="h-5 w-5 text-orange-500" />
              تحليل الإيرادات
            </h3>
            <div className="space-y-3">
              {analyticsData.revenueAnalysis.slice(-6).map((month, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{month.month}</span>
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-800">
                      {formatCurrency(month.revenue)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {month.subscriptions} اشتراك
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Top Performers */}
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <FiUsers className="h-5 w-5 text-indigo-500" />
              أفضل المؤدين
            </h3>
            <div className="space-y-3">
              {analyticsData.topPerformers.map((performer, index) => (
                <div key={performer.userId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                      <span className="text-indigo-600 font-bold text-sm">{index + 1}</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">{performer.name}</div>
                      <div className="text-xs text-gray-500">
                        {performer.invitations} دعوة • {performer.accepted} قبول
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-indigo-600">{performer.score}</div>
                    <div className="text-xs text-gray-500">نقطة</div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* System Health */}
        <motion.div
          className="bg-white rounded-2xl p-6 shadow-lg"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
            <FiActivity className="h-5 w-5 text-red-500" />
            صحة النظام
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{analyticsData.systemHealth.activeUsers}</div>
              <div className="text-sm text-gray-600">مستخدم نشط</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{analyticsData.systemHealth.responseRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">معدل الاستجابة</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">{analyticsData.systemHealth.conversionRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">معدل التحويل</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600">{analyticsData.systemHealth.churnRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">معدل التسرب</div>
            </div>
          </div>
        </motion.div>
      </div>

      <ToastContainer position="top-right" />
    </div>
  );
}
