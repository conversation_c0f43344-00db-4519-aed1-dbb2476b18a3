-- إنشاء جدول المستخدمين إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT auth.uid(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'subscriber',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إضا<PERSON>ة بعض المستخدمين للاختبار
INSERT INTO public.users (name, phone, password, role) VALUES
('مدير النظام', '0500000000', '123', 'admin'),
('مستخدم عادي', '0500000001', '123', 'subscriber');

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الدعوات
CREATE TABLE IF NOT EXISTS public.invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_date TIMESTAMP WITH TIME ZONE,
    location TEXT,
    template_id UUID REFERENCES public.templates(id),
    status VARCHAR(50) DEFAULT 'draft',
    additional_data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول الإشعارات
CREATE TABLE public.notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id),
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول القوالب
CREATE TABLE public.templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id),
    template_name VARCHAR(255) NOT NULL,
    template_structure JSONB DEFAULT '{
  "text_areas": [
    {"id": "title", "x": 50, "y": 30, "width": 80, "height": 10, "font_size": 24, "color": "#000000", "align": "center", "max_length": 50},
    {"id": "names", "x": 50, "y": 45, "width": 80, "height": 8, "font_size": 20, "color": "#000000", "align": "center", "max_length": 40},
    {"id": "date", "x": 50, "y": 55, "width": 60, "height": 6, "font_size": 16, "color": "#000000", "align": "center", "max_length": 30},
    {"id": "location", "x": 50, "y": 65, "width": 70, "height": 6, "font_size": 16, "color": "#000000", "align": "center", "max_length": 50},
    {"id": "description", "x": 50, "y": 75, "width": 80, "height": 15, "font_size": 14, "color": "#000000", "align": "center", "max_length": 200}
  ],
  "color_scheme": {
    "primary": "#8A2BE2",
    "secondary": "#FFC0CB",
    "text": "#000000",
    "background": "#FFFFFF"
  },
  "fonts": ["Arial", "Tajawal", "El Messiri", "Cairo"]
}'::jsonb,
    template_type VARCHAR(50) DEFAULT 'wedding',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- تحديث جدول القوالب
ALTER TABLE templates ADD COLUMN IF NOT EXISTS template_structure JSONB DEFAULT '{
  "text_areas": [
    {"id": "title", "x": 50, "y": 30, "width": 80, "height": 10, "font_size": 24, "color": "#000000", "align": "center", "max_length": 50},
    {"id": "names", "x": 50, "y": 45, "width": 80, "height": 8, "font_size": 20, "color": "#000000", "align": "center", "max_length": 40},
    {"id": "date", "x": 50, "y": 55, "width": 60, "height": 6, "font_size": 16, "color": "#000000", "align": "center", "max_length": 30},
    {"id": "location", "x": 50, "y": 65, "width": 70, "height": 6, "font_size": 16, "color": "#000000", "align": "center", "max_length": 50},
    {"id": "description", "x": 50, "y": 75, "width": 80, "height": 15, "font_size": 14, "color": "#000000", "align": "center", "max_length": 200}
  ],
  "color_scheme": {
    "primary": "#8A2BE2",
    "secondary": "#FFC0CB",
    "text": "#000000",
    "background": "#FFFFFF"
  },
  "fonts": ["Arial", "Tajawal", "El Messiri", "Cairo"]
}'::jsonb;

-- إضافة حقل لتحديد نوع القالب
ALTER TABLE templates ADD COLUMN IF NOT EXISTS template_type VARCHAR(50) DEFAULT 'wedding';

-- حذف جدول الضيوف إذا كان موجوداً
DROP TABLE IF EXISTS public.guests CASCADE;

-- إنشاء جدول الضيوف
CREATE TABLE public.guests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invitation_id UUID NOT NULL REFERENCES public.invitations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    notes TEXT DEFAULT '',
    number_of_companions INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(invitation_id, phone)
);

-- منح الصلاحيات على جدول الضيوف
GRANT ALL ON public.guests TO authenticated;
GRANT ALL ON public.guests TO anon;

-- تفعيل RLS على جدول الضيوف
ALTER TABLE public.guests ENABLE ROW LEVEL SECURITY;

-- سياسة السماح بإضافة ضيوف جدد
CREATE POLICY "Allow insert guests" ON public.guests
    FOR INSERT 
    TO public
    WITH CHECK (true);

-- سياسة السماح بتحديث الضيوف
CREATE POLICY "Allow update guests" ON public.guests
    FOR UPDATE
    TO public
    USING (true);

-- سياسة السماح بعرض الضيوف
CREATE POLICY "Allow select guests" ON public.guests
    FOR SELECT
    TO public
    USING (true);

