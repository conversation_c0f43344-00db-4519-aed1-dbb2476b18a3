-- ملف SQL شامل لإعداد قاعدة البيانات - نظام الدعوات
-- يحتوي على جميع الإعدادات والإصلاحات المطلوبة

-- =====================================================
-- 1. التأكد من وجود جميع الجداول المطلوبة
-- =====================================================

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    phone VARCHAR UNIQUE NOT NULL,
    password VARCHAR NOT NULL,
    role VARCHAR DEFAULT 'subscriber' CHECK (role IN ('admin', 'subscriber')),
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- جدول الدعوات
CREATE TABLE IF NOT EXISTS public.invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    title VARCHAR NOT NULL,
    description TEXT,
    event_date TIMESTAMP WITH TIME ZONE,
    location VARCHAR,
    template_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- جدول المدعوين
CREATE TABLE IF NOT EXISTS public.guests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invitation_id UUID NOT NULL REFERENCES public.invitations(id) ON DELETE CASCADE,
    name VARCHAR,
    phone VARCHAR,
    status VARCHAR DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
    attended BOOLEAN DEFAULT false,
    attended_at TIMESTAMP WITH TIME ZONE,
    qr_code TEXT,
    notes TEXT,
    number_of_companions INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- جدول الإشعارات
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR NOT NULL,
    title VARCHAR NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- جدول القوالب
CREATE TABLE IF NOT EXISTS public.templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    content TEXT NOT NULL,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS public.system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. إضافة الفهارس لتحسين الأداء
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_users_phone ON public.users(phone);
CREATE INDEX IF NOT EXISTS idx_users_role ON public.users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON public.users(is_active);

CREATE INDEX IF NOT EXISTS idx_invitations_user_id ON public.invitations(user_id);
CREATE INDEX IF NOT EXISTS idx_invitations_event_date ON public.invitations(event_date);

CREATE INDEX IF NOT EXISTS idx_guests_invitation_id ON public.guests(invitation_id);
CREATE INDEX IF NOT EXISTS idx_guests_phone ON public.guests(phone);
CREATE INDEX IF NOT EXISTS idx_guests_status ON public.guests(status);
CREATE INDEX IF NOT EXISTS idx_guests_attended ON public.guests(attended);
CREATE INDEX IF NOT EXISTS idx_guests_qr_code ON public.guests(qr_code);

CREATE INDEX IF NOT EXISTS idx_notifications_type ON public.notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(is_read);

-- =====================================================
-- 3. إضافة التعليقات التوضيحية
-- =====================================================

COMMENT ON TABLE public.users IS 'جدول المستخدمين - يحتوي على بيانات المشتركين والإداريين';
COMMENT ON COLUMN public.users.role IS 'دور المستخدم: admin = مدير، subscriber = مشترك';
COMMENT ON COLUMN public.users.is_active IS 'حالة تفعيل الحساب - يحددها المدير';

COMMENT ON TABLE public.invitations IS 'جدول الدعوات - يحتوي على بيانات الدعوات المنشأة';
COMMENT ON TABLE public.guests IS 'جدول المدعوين - يحتوي على قائمة المدعوين لكل دعوة';

COMMENT ON COLUMN public.guests.status IS 'رد الضيف على الدعوة (يحدده الضيف): pending = لم يرد، accepted = قبل، declined = اعتذر';
COMMENT ON COLUMN public.guests.attended IS 'الحضور الفعلي (يحدده صاحب الدعوة): true = حضر، false = لم يحضر';
COMMENT ON COLUMN public.guests.attended_at IS 'وقت تسجيل الحضور الفعلي';

-- =====================================================
-- 4. إنشاء دوال مساعدة مهمة
-- =====================================================

-- دالة لتحديث attended_at تلقائياً
CREATE OR REPLACE FUNCTION update_attended_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.attended = true AND (OLD.attended IS NULL OR OLD.attended = false) THEN
        NEW.attended_at = CURRENT_TIMESTAMP;
    ELSIF NEW.attended = false THEN
        NEW.attended_at = NULL;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث attended_at
DROP TRIGGER IF EXISTS trigger_update_attended_timestamp ON public.guests;
CREATE TRIGGER trigger_update_attended_timestamp
    BEFORE UPDATE ON public.guests
    FOR EACH ROW
    EXECUTE FUNCTION update_attended_timestamp();

-- دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة triggers لتحديث updated_at
DROP TRIGGER IF EXISTS trigger_update_users_updated_at ON public.users;
CREATE TRIGGER trigger_update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_invitations_updated_at ON public.invitations;
CREATE TRIGGER trigger_update_invitations_updated_at
    BEFORE UPDATE ON public.invitations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_guests_updated_at ON public.guests;
CREATE TRIGGER trigger_update_guests_updated_at
    BEFORE UPDATE ON public.guests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 5. إعدادات الأمان (RLS)
-- =====================================================

-- تفعيل Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للمستخدمين (المشتركون يرون بياناتهم فقط)
DROP POLICY IF EXISTS "Users can view own data" ON public.users;
CREATE POLICY "Users can view own data" ON public.users
    FOR SELECT USING (true); -- مؤقتاً للسماح بالوصول

DROP POLICY IF EXISTS "Users can update own data" ON public.users;
CREATE POLICY "Users can update own data" ON public.users
    FOR UPDATE USING (true); -- مؤقتاً للسماح بالوصول

-- سياسات الأمان للدعوات
DROP POLICY IF EXISTS "Users can manage own invitations" ON public.invitations;
CREATE POLICY "Users can manage own invitations" ON public.invitations
    FOR ALL USING (true); -- مؤقتاً للسماح بالوصول

-- سياسات الأمان للمدعوين
DROP POLICY IF EXISTS "Users can manage guests of own invitations" ON public.guests;
CREATE POLICY "Users can manage guests of own invitations" ON public.guests
    FOR ALL USING (true); -- مؤقتاً للسماح بالوصول

-- =====================================================
-- 6. بيانات أساسية (إذا لم تكن موجودة)
-- =====================================================

-- إضافة مدير افتراضي إذا لم يكن موجوداً
INSERT INTO public.users (id, name, phone, password, role, is_active)
VALUES (
    'f2ccc0c9-c8f1-4a17-af15-8e8a93bda15f',
    'المدير العام',
    '1234567890',
    '$2b$10$example.hash.here',
    'admin',
    true
) ON CONFLICT (phone) DO NOTHING;

-- إضافة قالب افتراضي
INSERT INTO public.templates (name, content, is_default)
VALUES (
    'القالب الافتراضي',
    'مرحباً {name}، أنت مدعو لحضور {event_title} في {event_date} بمكان {location}',
    true
) ON CONFLICT DO NOTHING;

-- =====================================================
-- 7. التحقق النهائي وإعادة تحميل Schema
-- =====================================================

-- عرض إحصائيات النظام
SELECT 
    'Database setup completed successfully!' as status,
    (SELECT COUNT(*) FROM public.users) as total_users,
    (SELECT COUNT(*) FROM public.invitations) as total_invitations,
    (SELECT COUNT(*) FROM public.guests) as total_guests,
    (SELECT COUNT(*) FROM public.notifications) as total_notifications;

-- إعادة تحميل schema cache
NOTIFY pgrst, 'reload schema';
