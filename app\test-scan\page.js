"use client";

import { useState } from "react";
import Header from "@/components/Header";
import Logo from "@/components/Logo";

export default function TestScanPage() {
  const [user] = useState({ name: "اختبار", role: "subscriber" });

  return (
    <>
      <Header userName={user?.name} userRole="subscriber" />
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden mb-8">
            <div className="p-6 bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 text-white">
              <h1 className="text-3xl font-bold mb-2">اختبار المكونات</h1>
              <p className="text-purple-100 text-lg">
                هذه صفحة اختبار للتأكد من أن المكونات تعمل بشكل صحيح
              </p>
            </div>
          </div>
          
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">
              اختبار Logo
            </h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 space-x-reverse">
                <Logo size="sm" />
                <span>صغير</span>
              </div>
              <div className="flex items-center space-x-4 space-x-reverse">
                <Logo size="md" />
                <span>متوسط</span>
              </div>
              <div className="flex items-center space-x-4 space-x-reverse">
                <Logo size="lg" />
                <span>كبير</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
