import { NextResponse } from 'next/server';
import twilio from 'twilio';

// إعداد Twilio API
const accountSid = process.env.TWILIO_ACCOUNT_SID || '**********************************';
const authToken = process.env.TWILIO_AUTH_TOKEN || 'ae33e59158b8c750c13be6569cb492ef';
const client = twilio(accountSid, authToken);

export async function POST(request) {
  try {
    const { to, message } = await request.json();
    
    console.log('Sending WhatsApp message to:', to);
    console.log('Message content:', message);
    
    // تنسيق رقم الهاتف
    let formattedPhone = to.replace(/\s+/g, '');
    
    // التأكد من أن الرقم يبدأ بـ +
    if (!formattedPhone.startsWith('+')) {
      // إذا كان الرقم يبدأ بـ 0، نستبدله بـ +966 (للسعودية)
      if (formattedPhone.startsWith('0')) {
        formattedPhone = '+966' + formattedPhone.substring(1);
      } else {
        // وإلا نضيف + في البداية
        formattedPhone = '+' + formattedPhone;
      }
    }
    
    // إرسال الرسالة باستخدام Twilio
    const twilioResponse = await client.messages.create({
      body: message,
      from: 'whatsapp:+***********', // رقم Twilio الخاص بك
      to: `whatsapp:${formattedPhone}`
    });
    
    console.log('Twilio message sent with SID:', twilioResponse.sid);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Message sent successfully',
      sid: twilioResponse.sid
    });
  } catch (error) {
    console.error('Error sending WhatsApp message:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message || 'حدث خطأ أثناء إرسال الرسالة' 
    }, { status: 500 });
  }
}
