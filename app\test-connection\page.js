'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function TestConnection() {
  const [connectionStatus, setConnectionStatus] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkConnection();
  }, []);

  const checkConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/check-connection');
      const data = await response.json();
      setConnectionStatus(data);
    } catch (error) {
      setConnectionStatus({
        success: false,
        message: 'فشل في الاتصال بالخادم',
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6 text-center">
            اختبار الاتصال بقاعدة البيانات
          </h1>
          
          <div className="space-y-6">
            <div className="flex justify-center">
              <button
                onClick={checkConnection}
                disabled={loading}
                className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'جاري الفحص...' : 'فحص الاتصال'}
              </button>
            </div>

            {connectionStatus && (
              <div className={`p-4 rounded-md ${
                connectionStatus.success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                <h3 className={`text-lg font-medium mb-2 ${
                  connectionStatus.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {connectionStatus.success ? '✅ نجح الاتصال' : '❌ فشل الاتصال'}
                </h3>
                
                <p className={`mb-3 ${
                  connectionStatus.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {connectionStatus.message}
                </p>

                {connectionStatus.envStatus && (
                  <div className="mb-3">
                    <h4 className="font-medium mb-2">حالة متغيرات البيئة:</h4>
                    <ul className="text-sm space-y-1">
                      <li>Supabase URL: {connectionStatus.envStatus.supabaseUrl}</li>
                      <li>Supabase Key: {connectionStatus.envStatus.supabaseKey}</li>
                    </ul>
                  </div>
                )}

                {connectionStatus.error && (
                  <div className="mt-3">
                    <h4 className="font-medium text-red-800 mb-1">تفاصيل الخطأ:</h4>
                    <code className="text-xs bg-red-100 p-2 rounded block">
                      {connectionStatus.error}
                    </code>
                  </div>
                )}

                {connectionStatus.needsSetup && (
                  <div className="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded">
                    <p className="text-yellow-800 font-medium">
                      يبدو أن قاعدة البيانات تحتاج إلى إعداد
                    </p>
                    <Link 
                      href="/setup"
                      className="inline-block mt-2 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
                    >
                      الذهاب لصفحة الإعداد
                    </Link>
                  </div>
                )}
              </div>
            )}

            <div className="text-center space-y-2">
              <div>
                <Link 
                  href="/setup"
                  className="inline-block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mr-2"
                >
                  إعداد قاعدة البيانات
                </Link>
                <Link 
                  href="/login"
                  className="inline-block px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
                >
                  تسجيل الدخول
                </Link>
              </div>
              <div>
                <Link href="/" className="text-blue-500 hover:underline">
                  العودة إلى الصفحة الرئيسية
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
