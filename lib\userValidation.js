// دوال التحقق من صحة المستخدم
import { useState, useEffect } from "react";
import { supabase } from "./supabase";

/**
 * التحقق من صحة المستخدم المحفوظ في localStorage
 * @param {Object} user - بيانات المستخدم من localStorage
 * @returns {Promise<{isValid: boolean, user: Object|null, error: string|null}>}
 */
export async function validateUserFromStorage(user) {
  try {
    // التحقق من وجود البيانات الأساسية
    if (!user || !user.id) {
      return {
        isValid: false,
        user: null,
        error: "بيانات المستخدم غير مكتملة",
      };
    }

    // التحقق من وجود المستخدم في قاعدة البيانات
    const { data: dbUser, error: dbError } = await supabase
      .from("users")
      .select("id, name, phone, role, is_active, created_at")
      .eq("id", user.id)
      .single();

    if (dbError || !dbUser) {
      console.error("User validation error:", dbError);
      return {
        isValid: false,
        user: null,
        error: "المستخدم غير موجود في قاعدة البيانات",
      };
    }

    // التحقق من تفعيل الحساب
    if (!dbUser.is_active) {
      return {
        isValid: false,
        user: dbUser,
        error: "حسابك غير مفعل. يرجى انتظار موافقة المدير.",
      };
    }

    // تحديث بيانات المستخدم في localStorage إذا كانت مختلفة
    const updatedUser = {
      id: dbUser.id,
      name: dbUser.name,
      phone: dbUser.phone,
      role: dbUser.role,
    };

    return {
      isValid: true,
      user: updatedUser,
      error: null,
    };
  } catch (error) {
    console.error("User validation error:", error);
    return {
      isValid: false,
      user: null,
      error: "حدث خطأ أثناء التحقق من بيانات المستخدم",
    };
  }
}

/**
 * تنظيف localStorage وإعادة توجيه المستخدم لصفحة تسجيل الدخول
 */
export function clearUserAndRedirect() {
  if (typeof window !== "undefined") {
    localStorage.removeItem("user");
    window.location.href = "/login";
  }
}

/**
 * الحصول على المستخدم المحقق من localStorage
 * @returns {Promise<Object|null>} - بيانات المستخدم المحققة أو null
 */
export async function getValidatedUser() {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    const userData = localStorage.getItem("user");
    if (!userData) {
      return null;
    }

    const user = JSON.parse(userData);
    const validation = await validateUserFromStorage(user);

    if (!validation.isValid) {
      console.warn("User validation failed:", validation.error);
      clearUserAndRedirect();
      return null;
    }

    // تحديث localStorage بالبيانات المحدثة
    localStorage.setItem("user", JSON.stringify(validation.user));
    return validation.user;
  } catch (error) {
    console.error("Error getting validated user:", error);
    clearUserAndRedirect();
    return null;
  }
}

/**
 * hook مخصص للتحقق من المستخدم في React components
 */
export function useValidatedUser() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function checkUser() {
      try {
        const validatedUser = await getValidatedUser();
        setUser(validatedUser);
        setError(null);
      } catch (err) {
        setError(err.message);
        setUser(null);
      } finally {
        setLoading(false);
      }
    }

    checkUser();
  }, []);

  return { user, loading, error };
}
