'use client';

import Link from 'next/link';
import { useState } from 'react';

export default function HelpPage() {
  const [activeTab, setActiveTab] = useState('setup');

  const tabs = [
    { id: 'setup', label: 'إعداد قاعدة البيانات' },
    { id: 'login', label: 'مشاكل تسجيل الدخول' },
    { id: 'troubleshooting', label: 'حل المشاكل' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-3xl font-bold text-gray-900">مركز المساعدة</h1>
            <p className="mt-2 text-gray-600">دليل شامل لحل مشاكل النظام</p>
          </div>

          {/* التبويبات */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* تبويب إعداد قاعدة البيانات */}
            {activeTab === 'setup' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">إعداد قاعدة البيانات</h2>
                  <p className="text-gray-600 mb-6">
                    إذا كنت تواجه مشاكل في تسجيل الدخول، فقد تحتاج إلى إعداد قاعدة البيانات أولاً.
                  </p>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-blue-800 mb-2">الطريقة السريعة</h3>
                  <p className="text-blue-700 mb-3">استخدم أدوات الإعداد التلقائي:</p>
                  <div className="space-x-2 space-x-reverse">
                    <Link 
                      href="/setup"
                      className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      صفحة الإعداد
                    </Link>
                    <Link 
                      href="/test-connection"
                      className="inline-block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      اختبار الاتصال
                    </Link>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-yellow-800 mb-2">الطريقة اليدوية</h3>
                  <p className="text-yellow-700 mb-3">إذا فشلت الطريقة التلقائية:</p>
                  <ol className="list-decimal list-inside space-y-2 text-yellow-700">
                    <li>اذهب إلى <a href="https://supabase.com" target="_blank" className="underline">لوحة تحكم Supabase</a></li>
                    <li>اختر مشروعك</li>
                    <li>اذهب إلى SQL Editor</li>
                    <li>انسخ والصق محتوى ملف <code className="bg-yellow-100 px-1 rounded">database-setup.sql</code></li>
                    <li>اضغط Run لتشغيل الأوامر</li>
                  </ol>
                </div>

                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">التحقق من النجاح</h3>
                  <p className="text-gray-700 mb-3">بعد الإعداد، تأكد من:</p>
                  <ul className="list-disc list-inside space-y-1 text-gray-700">
                    <li>وجود جدول <code className="bg-gray-100 px-1 rounded">users</code></li>
                    <li>وجود المستخدمين التجريبيين</li>
                    <li>إمكانية تسجيل الدخول</li>
                  </ul>
                </div>
              </div>
            )}

            {/* تبويب مشاكل تسجيل الدخول */}
            {activeTab === 'login' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">حل مشاكل تسجيل الدخول</h2>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">خطأ "بيانات الدخول غير صحيحة"</h3>
                  <p className="text-red-700 mb-3">هذا الخطأ يحدث عادة للأسباب التالية:</p>
                  <ul className="list-disc list-inside space-y-1 text-red-700">
                    <li>جدول المستخدمين غير موجود في قاعدة البيانات</li>
                    <li>لا توجد بيانات مستخدمين في الجدول</li>
                    <li>رقم الهاتف أو كلمة المرور غير صحيحة</li>
                    <li>مشكلة في الاتصال بقاعدة البيانات</li>
                  </ul>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-green-800 mb-2">بيانات تسجيل الدخول للاختبار</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-white p-3 rounded border">
                      <h4 className="font-medium text-green-800">حساب المدير:</h4>
                      <p>رقم الهاتف: <code className="bg-gray-100 px-1 rounded">0500000001</code></p>
                      <p>كلمة المرور: <code className="bg-gray-100 px-1 rounded">admin123</code></p>
                    </div>
                    <div className="bg-white p-3 rounded border">
                      <h4 className="font-medium text-green-800">حساب المستخدم:</h4>
                      <p>رقم الهاتف: <code className="bg-gray-100 px-1 rounded">0500000002</code></p>
                      <p>كلمة المرور: <code className="bg-gray-100 px-1 rounded">password123</code></p>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-blue-800 mb-2">خطوات الحل</h3>
                  <ol className="list-decimal list-inside space-y-2 text-blue-700">
                    <li>تأكد من إعداد قاعدة البيانات أولاً</li>
                    <li>استخدم بيانات تسجيل الدخول الصحيحة</li>
                    <li>تأكد من كتابة رقم الهاتف بالتنسيق الصحيح</li>
                    <li>جرب إنشاء مستخدم تجريبي جديد</li>
                  </ol>
                </div>
              </div>
            )}

            {/* تبويب حل المشاكل */}
            {activeTab === 'troubleshooting' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">حل المشاكل العامة</h2>
                </div>

                <div className="space-y-4">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">مشكلة: لا يمكن الاتصال بقاعدة البيانات</h3>
                    <p className="text-gray-600 mb-2"><strong>الحل:</strong></p>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      <li>تحقق من ملف <code className="bg-gray-100 px-1 rounded">.env.local</code></li>
                      <li>تأكد من صحة متغيرات Supabase</li>
                      <li>تحقق من حالة مشروع Supabase</li>
                    </ul>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">مشكلة: الصفحات لا تعمل</h3>
                    <p className="text-gray-600 mb-2"><strong>الحل:</strong></p>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      <li>تأكد من تشغيل الخادم: <code className="bg-gray-100 px-1 rounded">npm run dev</code></li>
                      <li>تحقق من وجود أخطاء في وحدة التحكم</li>
                      <li>امسح cache المتصفح</li>
                    </ul>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">مشكلة: خطأ 500 في API</h3>
                    <p className="text-gray-600 mb-2"><strong>الحل:</strong></p>
                    <ul className="list-disc list-inside space-y-1 text-gray-600">
                      <li>تحقق من logs الخادم</li>
                      <li>تأكد من صحة أوامر SQL</li>
                      <li>تحقق من صلاحيات قاعدة البيانات</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">أدوات مفيدة للتشخيص</h3>
                  <div className="space-x-2 space-x-reverse">
                    <Link 
                      href="/test-connection"
                      className="inline-block px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                    >
                      اختبار الاتصال
                    </Link>
                    <Link 
                      href="/setup"
                      className="inline-block px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                    >
                      إعداد قاعدة البيانات
                    </Link>
                    <button 
                      onClick={() => window.open('/api/check-connection', '_blank')}
                      className="inline-block px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
                    >
                      فحص API
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex justify-between items-center">
              <Link href="/" className="text-blue-500 hover:underline">
                العودة إلى الصفحة الرئيسية
              </Link>
              <Link href="/login" className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                تسجيل الدخول
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
