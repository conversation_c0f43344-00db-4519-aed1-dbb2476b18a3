-- إعداد نهائي لنظام QR Code المحسن
-- Final setup for enhanced QR Code system

-- التأكد من وجود جميع الأعمدة المطلوبة
ALTER TABLE guests 
ADD COLUMN IF NOT EXISTS qr_code TEXT,
ADD COLUMN IF NOT EXISTS qr_generated_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS qr_scanned_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS attended_at TIMESTAMP;

-- إنشاء فهارس للأداء المحسن
CREATE INDEX IF NOT EXISTS idx_guests_qr_code ON guests(qr_code) WHERE qr_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_guests_invitation_status ON guests(invitation_id, status);
CREATE INDEX IF NOT EXISTS idx_guests_attended ON guests(attended) WHERE attended = true;
CREATE INDEX IF NOT EXISTS idx_guests_qr_scanned ON guests(qr_scanned_at) WHERE qr_scanned_at IS NOT NULL;

-- دالة محسنة لإنتاج QR code
CREATE OR REPLACE FUNCTION generate_enhanced_qr_code(
    p_invitation_id UUID,
    p_guest_phone TEXT,
    p_guest_name TEXT DEFAULT NULL
) RETURNS TEXT AS $$
DECLARE
    qr_data JSONB;
    encoded_qr TEXT;
    unique_id UUID;
BEGIN
    -- إنشاء معرف فريد
    unique_id := gen_random_uuid();
    
    -- إنشاء بيانات QR code محسنة
    qr_data := jsonb_build_object(
        'id', unique_id,
        'invitationId', p_invitation_id,
        'phone', p_guest_phone,
        'name', COALESCE(p_guest_name, ''),
        'timestamp', extract(epoch from now()) * 1000,
        'type', 'guest_entry',
        'version', '2.0'
    );
    
    -- تشفير البيانات
    encoded_qr := encode(qr_data::text::bytea, 'base64');
    
    RETURN encoded_qr;
END;
$$ LANGUAGE plpgsql;

-- تحديث جميع الضيوف المقبولين بـ QR codes جديدة
UPDATE guests 
SET 
    qr_code = generate_enhanced_qr_code(invitation_id, phone, name),
    qr_generated_at = NOW()
WHERE 
    status = 'accepted' 
    AND (qr_code IS NULL OR qr_code = '' OR qr_code NOT LIKE '%version%');

-- دالة محسنة لتحديث QR code عند القبول
CREATE OR REPLACE FUNCTION auto_generate_qr_on_acceptance()
RETURNS TRIGGER AS $$
BEGIN
    -- إذا تم تغيير الحالة إلى مقبول
    IF NEW.status = 'accepted' AND (OLD.status IS NULL OR OLD.status != 'accepted') THEN
        -- إنشاء QR code جديد
        NEW.qr_code := generate_enhanced_qr_code(NEW.invitation_id, NEW.phone, NEW.name);
        NEW.qr_generated_at := NOW();
    END IF;
    
    -- إذا تم تغيير الحالة من مقبول إلى شيء آخر، حذف QR code
    IF OLD.status = 'accepted' AND NEW.status != 'accepted' THEN
        NEW.qr_code := NULL;
        NEW.qr_generated_at := NULL;
        NEW.qr_scanned_at := NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إعادة إنشاء trigger
DROP TRIGGER IF EXISTS trigger_auto_qr_generation ON guests;
CREATE TRIGGER trigger_auto_qr_generation
    BEFORE UPDATE ON guests
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_qr_on_acceptance();

-- دالة لمعالجة مسح QR code مع تسجيل مفصل
CREATE OR REPLACE FUNCTION process_qr_scan(
    p_qr_code TEXT,
    p_invitation_id UUID
) RETURNS JSONB AS $$
DECLARE
    guest_record RECORD;
    qr_data JSONB;
    result JSONB;
BEGIN
    -- محاولة فك تشفير QR code
    BEGIN
        qr_data := (decode(p_qr_code, 'base64')::text)::JSONB;
        
        -- التحقق من صحة البيانات
        IF qr_data->>'type' != 'guest_entry' OR 
           (qr_data->>'invitationId')::UUID != p_invitation_id THEN
            RAISE EXCEPTION 'Invalid QR code for this invitation';
        END IF;
        
        -- البحث باستخدام البيانات المفكوكة
        SELECT * INTO guest_record
        FROM guests 
        WHERE phone = qr_data->>'phone' 
        AND invitation_id = p_invitation_id;
        
    EXCEPTION WHEN OTHERS THEN
        -- إذا فشل فك التشفير، البحث مباشرة
        SELECT * INTO guest_record
        FROM guests 
        WHERE (qr_code = p_qr_code OR phone = p_qr_code)
        AND invitation_id = p_invitation_id;
    END;
    
    -- إذا لم نجد الضيف
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Guest not found or QR code invalid'
        );
    END IF;
    
    -- التحقق من أن الضيف مقبول
    IF guest_record.status != 'accepted' THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Guest has not accepted the invitation',
            'guest', row_to_json(guest_record)
        );
    END IF;
    
    -- التحقق من الحضور المسبق
    IF guest_record.attended THEN
        RETURN jsonb_build_object(
            'success', true,
            'message', 'Guest already attended',
            'alreadyAttended', true,
            'guest', row_to_json(guest_record)
        );
    END IF;
    
    -- تسجيل الحضور
    UPDATE guests 
    SET 
        attended = true,
        attended_at = NOW(),
        qr_scanned_at = NOW()
    WHERE id = guest_record.id;
    
    -- جلب البيانات المحدثة
    SELECT * INTO guest_record FROM guests WHERE id = guest_record.id;
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Attendance recorded successfully',
        'guest', row_to_json(guest_record)
    );
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على إحصائيات الدعوة
CREATE OR REPLACE FUNCTION get_invitation_stats(p_invitation_id UUID)
RETURNS JSONB AS $$
DECLARE
    stats JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total', COUNT(*),
        'accepted', COUNT(*) FILTER (WHERE status = 'accepted'),
        'declined', COUNT(*) FILTER (WHERE status = 'declined'),
        'pending', COUNT(*) FILTER (WHERE status = 'pending'),
        'attended', COUNT(*) FILTER (WHERE attended = true),
        'withQR', COUNT(*) FILTER (WHERE qr_code IS NOT NULL AND qr_code != ''),
        'scanned', COUNT(*) FILTER (WHERE qr_scanned_at IS NOT NULL)
    ) INTO stats
    FROM guests
    WHERE invitation_id = p_invitation_id;
    
    RETURN stats;
END;
$$ LANGUAGE plpgsql;

-- إنشاء view للإحصائيات السريعة
CREATE OR REPLACE VIEW invitation_stats_view AS
SELECT 
    i.id as invitation_id,
    i.title,
    i.event_date,
    COUNT(g.id) as total_guests,
    COUNT(g.id) FILTER (WHERE g.status = 'accepted') as accepted_guests,
    COUNT(g.id) FILTER (WHERE g.attended = true) as attended_guests,
    COUNT(g.id) FILTER (WHERE g.qr_code IS NOT NULL) as guests_with_qr,
    COUNT(g.id) FILTER (WHERE g.qr_scanned_at IS NOT NULL) as qr_scanned_count,
    ROUND(
        (COUNT(g.id) FILTER (WHERE g.attended = true) * 100.0) / 
        NULLIF(COUNT(g.id) FILTER (WHERE g.status = 'accepted'), 0), 
        2
    ) as attendance_rate
FROM invitations i
LEFT JOIN guests g ON i.id = g.invitation_id
GROUP BY i.id, i.title, i.event_date;

-- تحديث timestamps للسجلات الموجودة
UPDATE guests 
SET attended_at = updated_at 
WHERE attended = true AND attended_at IS NULL;

-- إضافة تعليقات للتوثيق
COMMENT ON FUNCTION generate_enhanced_qr_code IS 'إنشاء QR code محسن للضيوف المقبولين';
COMMENT ON FUNCTION process_qr_scan IS 'معالجة مسح QR code وتسجيل الحضور';
COMMENT ON FUNCTION get_invitation_stats IS 'الحصول على إحصائيات الدعوة';
COMMENT ON VIEW invitation_stats_view IS 'عرض سريع لإحصائيات جميع الدعوات';

-- عرض النتائج النهائية
SELECT 
    'QR System Setup Complete' as status,
    COUNT(*) as total_guests,
    COUNT(*) FILTER (WHERE status = 'accepted') as accepted_guests,
    COUNT(*) FILTER (WHERE qr_code IS NOT NULL) as guests_with_qr,
    COUNT(*) FILTER (WHERE attended = true) as attended_guests
FROM guests;
