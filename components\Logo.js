import React from "react";

export default function Logo({ size = "md" }) {
  const sizeClasses = {
    sm: "h-6",
    md: "h-8",
    lg: "h-10",
    xl: "h-12",
  };

  return (
    <div className="flex items-center">
      <div
        className={`${sizeClasses[size]} aspect-square bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold`}
      >
        <span className="text-xl">د</span>
      </div>
      <span className="mr-2 font-bold text-white">نظام الدعوات</span>
    </div>
  );
}
