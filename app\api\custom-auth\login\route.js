import { NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function POST(request) {
  try {
    const { identifier, password } = await request.json();
    if (!identifier || !password) {
      return NextResponse.json(
        { success: false, error: "جميع الحقول مطلوبة" },
        { status: 400 }
      );
    }
    const supabase = createRouteHandlerClient({ cookies });
    // جلب المستخدم فقط برقم الهاتف
    const { data: user, error } = await supabase
      .from("users")
      .select("*")
      .eq("phone", identifier)
      .maybeSingle();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "بيانات الدخول غير صحيحة" },
        { status: 401 }
      );
    }
    // مقارنة كلمة المرور مباشرة
    if (user.password !== password) {
      return NextResponse.json(
        { success: false, error: "بيانات الدخول غير صحيحة" },
        { status: 401 }
      );
    }
    if (user.is_active === false) {
      return NextResponse.json(
        { success: false, error: "الحساب غير مفعل بعد" },
        { status: 403 }
      );
    }
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        name: user.name,
        phone: user.phone,
        role: user.role,
      },
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: error.message || "حدث خطأ أثناء تسجيل الدخول" },
      { status: 500 }
    );
  }
}
