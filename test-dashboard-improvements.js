/**
 * اختبار تحسينات لوحة التحكم
 * يختبر جلب البيانات وعرض الإحصائيات والدعوات
 */

import { supabase } from './lib/supabase.js';
import { fetchUserStats } from './lib/dashboardHelpers.js';

async function testDashboardImprovements() {
  console.log('🧪 بدء اختبار تحسينات لوحة التحكم...\n');

  try {
    // 1. اختبار جلب المستخدمين
    console.log('1️⃣ اختبار جلب المستخدمين...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, name, email, phone')
      .limit(1);

    if (usersError) {
      console.error('❌ خطأ في جلب المستخدمين:', usersError);
      return;
    }

    if (!users || users.length === 0) {
      console.log('⚠️ لا توجد مستخدمين في قاعدة البيانات');
      return;
    }

    const testUser = users[0];
    console.log('✅ تم جلب المستخدم:', testUser.name);

    // 2. اختبار جلب إحصائيات المستخدم
    console.log('\n2️⃣ اختبار جلب إحصائيات المستخدم...');
    const userStats = await fetchUserStats(testUser.id);
    
    console.log('📊 الإحصائيات الإجمالية:');
    console.log(`   - عدد الدعوات: ${userStats.stats.invitations}`);
    console.log(`   - عدد المدعوين: ${userStats.stats.guests}`);
    console.log(`   - عدد المقبولين: ${userStats.stats.accepted}`);
    console.log(`   - عدد المرفوضين: ${userStats.stats.declined}`);
    console.log(`   - عدد المنتظرين: ${userStats.stats.pending}`);
    console.log(`   - عدد الحاضرين: ${userStats.stats.attendance}`);

    // 3. اختبار تفاصيل الدعوات
    console.log('\n3️⃣ اختبار تفاصيل الدعوات...');
    if (userStats.invitations.length > 0) {
      userStats.invitations.forEach((invitation, index) => {
        console.log(`\n📋 الدعوة ${index + 1}:`);
        console.log(`   - العنوان: ${invitation.title || 'بدون عنوان'}`);
        console.log(`   - المكان: ${invitation.location || 'غير محدد'}`);
        console.log(`   - التاريخ: ${invitation.date || invitation.event_date || 'غير محدد'}`);
        console.log(`   - الوقت: ${invitation.time || 'غير محدد'}`);
        console.log(`   - عدد المدعوين: ${invitation.guest_count || 0}`);
        console.log(`   - عدد المقبولين: ${invitation.accepted_count || 0}`);
        console.log(`   - عدد المرفوضين: ${invitation.declined_count || 0}`);
        console.log(`   - عدد الحاضرين: ${invitation.attended_count || 0}`);
        
        // حساب النسب المئوية
        const acceptanceRate = invitation.guest_count > 0 
          ? Math.round((invitation.accepted_count / invitation.guest_count) * 100)
          : 0;
        const attendanceRate = invitation.accepted_count > 0
          ? Math.round((invitation.attended_count / invitation.accepted_count) * 100)
          : 0;
        
        console.log(`   - معدل القبول: ${acceptanceRate}%`);
        console.log(`   - معدل الحضور: ${attendanceRate}%`);
      });
    } else {
      console.log('📭 لا توجد دعوات لهذا المستخدم');
    }

    // 4. اختبار حساب النسب المئوية الإجمالية
    console.log('\n4️⃣ اختبار حساب النسب المئوية...');
    const overallAcceptanceRate = userStats.stats.guests > 0
      ? Math.round((userStats.stats.accepted / userStats.stats.guests) * 100)
      : 0;
    const overallAttendanceRate = userStats.stats.accepted > 0
      ? Math.round((userStats.stats.attendance / userStats.stats.accepted) * 100)
      : 0;
    const averageGuestsPerInvitation = userStats.stats.invitations > 0
      ? Math.round(userStats.stats.guests / userStats.stats.invitations)
      : 0;

    console.log(`📈 النسب المئوية الإجمالية:`);
    console.log(`   - معدل القبول الإجمالي: ${overallAcceptanceRate}%`);
    console.log(`   - معدل الحضور الإجمالي: ${overallAttendanceRate}%`);
    console.log(`   - متوسط المدعوين لكل دعوة: ${averageGuestsPerInvitation}`);

    // 5. اختبار البيانات المطلوبة للواجهة
    console.log('\n5️⃣ اختبار البيانات المطلوبة للواجهة...');
    
    // بيانات بطاقات الإحصائيات
    const statCards = [
      {
        title: 'إجمالي الدعوات',
        value: userStats.stats.invitations,
        subtitle: 'دعوة نشطة'
      },
      {
        title: 'إجمالي المدعوين',
        value: userStats.stats.guests,
        subtitle: `${userStats.stats.pending} في الانتظار`
      },
      {
        title: 'تأكيد الحضور',
        value: userStats.stats.accepted,
        subtitle: `${overallAcceptanceRate}% من المدعوين`
      },
      {
        title: 'الحضور الفعلي',
        value: userStats.stats.attendance,
        subtitle: `${overallAttendanceRate}% من المقبولين`
      }
    ];

    console.log('🎯 بيانات بطاقات الإحصائيات:');
    statCards.forEach((card, index) => {
      console.log(`   ${index + 1}. ${card.title}: ${card.value} (${card.subtitle})`);
    });

    // 6. اختبار البحث في الدعوات
    console.log('\n6️⃣ اختبار البحث في الدعوات...');
    const searchTerm = 'دعوة';
    const filteredInvitations = userStats.invitations.filter(invitation =>
      invitation.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    console.log(`🔍 نتائج البحث عن "${searchTerm}": ${filteredInvitations.length} دعوة`);

    console.log('\n✅ تم اكتمال جميع الاختبارات بنجاح!');
    console.log('\n📋 ملخص النتائج:');
    console.log(`   - المستخدم: ${testUser.name}`);
    console.log(`   - عدد الدعوات: ${userStats.stats.invitations}`);
    console.log(`   - إجمالي المدعوين: ${userStats.stats.guests}`);
    console.log(`   - معدل القبول: ${overallAcceptanceRate}%`);
    console.log(`   - معدل الحضور: ${overallAttendanceRate}%`);
    console.log(`   - جميع البيانات متوفرة للواجهة: ✅`);

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    console.error('تفاصيل الخطأ:', error.message);
  }
}

// تشغيل الاختبار
testDashboardImprovements();
