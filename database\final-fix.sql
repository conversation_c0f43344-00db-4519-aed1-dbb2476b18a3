-- الإصلاح النهائي لقاعدة البيانات - بدون قيود معقدة

-- 1. التأكد من وجود العمود attended
ALTER TABLE public.guests ADD COLUMN IF NOT EXISTS attended BOOLEAN DEFAULT false;
ALTER TABLE public.guests ADD COLUMN IF NOT EXISTS attended_at TIMESTAMP WITH TIME ZONE;

-- 2. إزالة أي قيود تحقق قد تسبب مشاكل
ALTER TABLE public.guests DROP CONSTRAINT IF EXISTS guests_status_check;

-- 3. تنظيف قيم status (بدون إضافة قيود جديدة)
UPDATE public.guests 
SET status = 'pending' 
WHERE status IS NULL OR status = '';

-- 4. إصلاح جدول notifications
ALTER TABLE public.notifications ALTER COLUMN message DROP NOT NULL;
ALTER TABLE public.notifications ALTER COLUMN message SET DEFAULT 'إشعار جديد';
UPDATE public.notifications 
SET message = 'إشعا<PERSON> جديد'
WHERE message IS NULL;

-- 5. إ<PERSON><PERSON><PERSON>ة تعليقات توضيحية
COMMENT ON COLUMN public.guests.status IS 'رد الضيف على الدعوة: pending = لم يرد، accepted = قبل، declined = اعتذر';
COMMENT ON COLUMN public.guests.attended IS 'الحضور الفعلي (يحدده صاحب الدعوة): true = حضر، false = لم يحضر';
COMMENT ON COLUMN public.guests.attended_at IS 'وقت تسجيل الحضور الفعلي';

-- 6. إنشاء trigger لتحديث attended_at تلقائياً
CREATE OR REPLACE FUNCTION update_attended_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.attended = true AND (OLD.attended IS NULL OR OLD.attended = false) THEN
        NEW.attended_at = CURRENT_TIMESTAMP;
    ELSIF NEW.attended = false THEN
        NEW.attended_at = NULL;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_attended_timestamp ON public.guests;
CREATE TRIGGER trigger_update_attended_timestamp
    BEFORE UPDATE ON public.guests
    FOR EACH ROW
    EXECUTE FUNCTION update_attended_timestamp();

-- 7. إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_guests_status ON public.guests(status);
CREATE INDEX IF NOT EXISTS idx_guests_attended ON public.guests(attended);
CREATE INDEX IF NOT EXISTS idx_guests_invitation_id ON public.guests(invitation_id);

-- 8. إعادة تحميل schema cache
NOTIFY pgrst, 'reload schema';

-- 9. التحقق من نجاح العملية
SELECT 
    'تم الإصلاح بنجاح!' as status,
    COUNT(*) as total_guests,
    COUNT(CASE WHEN attended IS NOT NULL THEN 1 END) as guests_with_attended_column
FROM public.guests;
