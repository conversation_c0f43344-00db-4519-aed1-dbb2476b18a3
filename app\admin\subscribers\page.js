"use client";

import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import {
  FiUsers,
  FiSearch,
  FiPlus,
  FiEdit,
  FiTrash2,
  FiMail,
  FiCalendar,
  FiEye,
  FiLock,
  FiFilter,
  FiRefreshCw,
  FiDownload,
  FiX,
  FiCheck,
  FiAlertCircle,
} from "react-icons/fi";
import Header from "@/components/Header";

export default function SubscribersList() {
  const [subscribers, setSubscribers] = useState([]);
  const [filteredSubscribers, setFilteredSubscribers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState("created_at");
  const [sortDirection, setSortDirection] = useState("desc");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [subscriberToDelete, setSubscriberToDelete] = useState(null);
  const [subscriberStats, setSubscriberStats] = useState({});
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [selectedSubscriber, setSelectedSubscriber] = useState(null);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [passwordSuccess, setPasswordSuccess] = useState("");
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  const router = useRouter();

  // تعريف الأنيميشنات
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.05,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.3 },
    },
  };

  useEffect(() => {
    const checkUser = async () => {
      try {
        const userData = localStorage.getItem("user");
        if (!userData) {
          router.push("/login");
          return;
        }

        const parsedUser = JSON.parse(userData);
        if (parsedUser.role !== "admin") {
          router.push("/login");
          return;
        }

        fetchSubscribers();
      } catch (error) {
        console.error("Error checking user:", error);
        router.push("/login");
      }
    };

    checkUser();
  }, [router]);

  useEffect(() => {
    if (subscribers.length > 0) {
      fetchSubscriberStats();
    }
  }, [subscribers]);

  useEffect(() => {
    filterSubscribers();
  }, [searchTerm, subscribers, sortField, sortDirection]);

  const fetchSubscribers = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from("users")
        .select("*")
        .eq("role", "subscriber")
        .order(sortField, { ascending: sortDirection === "asc" });

      if (error) throw error;

      setSubscribers(data || []);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching subscribers:", error);
      setLoading(false);
    }
  };

  // تحسين جلب الإحصائيات: دفعة واحدة لكل المشتركين
  const fetchSubscriberStats = async () => {
    try {
      // جلب جميع الدعوات دفعة واحدة
      const { data: allInvitations, error: invitationsError } = await supabase
        .from("invitations")
        .select("id, user_id");
      if (invitationsError) throw invitationsError;
      // جلب جميع الضيوف دفعة واحدة
      const { data: allGuests, error: guestsError } = await supabase
        .from("guests")
        .select("invitation_id, attended");
      if (guestsError) throw guestsError;
      // بناء إحصائيات لكل مشترك
      const stats = {};
      for (const subscriber of subscribers) {
        const invitations = allInvitations.filter(
          (inv) => inv.user_id === subscriber.id
        );
        const invitationIds = invitations.map((inv) => inv.id);
        const guests = allGuests.filter((g) =>
          invitationIds.includes(g.invitation_id)
        );
        const attendance = guests.filter((g) => g.attended === true);
        stats[subscriber.id] = {
          invitations: invitations.length,
          guests: guests.length,
          attendance: attendance.length,
        };
      }
      setSubscriberStats(stats);
    } catch (error) {
      console.error("Error fetching subscriber stats:", error);
    }
  };

  const filterSubscribers = () => {
    let filtered = [...subscribers];

    if (searchTerm) {
      filtered = filtered.filter(
        (subscriber) =>
          subscriber.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          subscriber.phone?.includes(searchTerm)
      );
    }

    filtered.sort((a, b) => {
      if (sortDirection === "asc") {
        return a[sortField] > b[sortField] ? 1 : -1;
      } else {
        return a[sortField] < b[sortField] ? 1 : -1;
      }
    });

    setFilteredSubscribers(filtered);
  };

  const handleSort = (field) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  const handleDeleteClick = (subscriber) => {
    setSubscriberToDelete(subscriber);
    setShowDeleteModal(true);
  };

  const handlePasswordClick = (subscriber) => {
    setSelectedSubscriber(subscriber);
    setNewPassword("");
    setConfirmPassword("");
    setPasswordError("");
    setPasswordSuccess("");
    setShowPasswordModal(true);
  };

  const handleDeleteSubscriber = async () => {
    if (!subscriberToDelete) return;
    try {
      setDeleteLoading(true);
      // جلب كل الدعوات الخاصة بالمشترك
      const { data: invitations, error: invitationsError } = await supabase
        .from("invitations")
        .select("id")
        .eq("user_id", subscriberToDelete.id);
      if (invitationsError) throw invitationsError;
      // حذف جميع الضيوف المرتبطين بهذه الدعوات
      if (invitations && invitations.length > 0) {
        const invitationIds = invitations.map((inv) => inv.id);
        const { error: guestsError } = await supabase
          .from("guests")
          .delete()
          .in("invitation_id", invitationIds);
        if (guestsError) throw guestsError;
      }
      // حذف جميع الدعوات المرتبطة بالمشترك
      const { error: invitationsDelError } = await supabase
        .from("invitations")
        .delete()
        .eq("user_id", subscriberToDelete.id);
      if (invitationsDelError) throw invitationsDelError;
      // حذف المشترك
      const { error } = await supabase
        .from("users")
        .delete()
        .eq("id", subscriberToDelete.id);
      if (error) throw error;
      setSubscribers(subscribers.filter((s) => s.id !== subscriberToDelete.id));
      setShowDeleteModal(false);
      setSubscriberToDelete(null);
      setDeleteLoading(false);
    } catch (error) {
      console.error("Error deleting subscriber:", error);
      setDeleteLoading(false);
    }
  };

  const handleChangePassword = async () => {
    if (!selectedSubscriber) return;

    try {
      setPasswordError("");
      setPasswordSuccess("");

      if (!newPassword || !confirmPassword) {
        setPasswordError("يرجى ملء جميع الحقول");
        return;
      }

      if (newPassword !== confirmPassword) {
        setPasswordError("كلمات المرور غير متطابقة");
        return;
      }

      if (newPassword.length < 6) {
        setPasswordError("يجب أن تكون كلمة المرور 6 أحرف على الأقل");
        return;
      }

      setPasswordLoading(true);

      // تحديث كلمة المرور
      const { error } = await supabase
        .from("users")
        .update({ password: newPassword })
        .eq("id", selectedSubscriber.id);

      if (error) throw error;

      setPasswordSuccess("تم تغيير كلمة المرور بنجاح");
      setTimeout(() => {
        setShowPasswordModal(false);
        setSelectedSubscriber(null);
        setNewPassword("");
        setConfirmPassword("");
        setPasswordSuccess("");
      }, 2000);

      setPasswordLoading(false);
    } catch (error) {
      console.error("Error changing password:", error);
      setPasswordError("حدث خطأ أثناء تغيير كلمة المرور");
      setPasswordLoading(false);
    }
  };

  const exportToCSV = () => {
    if (filteredSubscribers.length === 0) return;

    const headers = [
      "الاسم",
      "رقم الهاتف",
      "تاريخ التسجيل",
      "عدد الدعوات",
      "عدد المدعوين",
      "عدد الحضور",
    ];

    const rows = filteredSubscribers.map((subscriber) => [
      subscriber.name || "",
      subscriber.phone || "",
      new Date(subscriber.created_at).toLocaleDateString("ar-SA"),
      subscriberStats[subscriber.id]?.invitations || 0,
      subscriberStats[subscriber.id]?.guests || 0,
      subscriberStats[subscriber.id]?.attendance || 0,
    ]);

    let csvContent = "\uFEFF"; // BOM for proper Arabic encoding
    csvContent += headers.join(",") + "\n";

    rows.forEach((row) => {
      csvContent += row.join(",") + "\n";
    });

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `المشتركين-${new Date().toLocaleDateString("ar-SA")}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // مكون نافذة حذف المشترك
  const DeleteModal = () => (
    <AnimatePresence>
      {showDeleteModal && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-lg shadow-xl max-w-md w-full p-6"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
          >
            <div className="text-center mb-6">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <FiAlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                تأكيد حذف المشترك
              </h3>
              <p className="text-sm text-gray-500">
                هل أنت متأكد من حذف المشترك "{subscriberToDelete?.name}"؟ سيتم
                حذف جميع الدعوات والمدعوين المرتبطين به.
              </p>
            </div>
            <div className="flex justify-center space-x-4 space-x-reverse">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                disabled={deleteLoading}
              >
                إلغاء
              </button>
              <button
                onClick={handleDeleteSubscriber}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center justify-center"
                disabled={deleteLoading}
              >
                {deleteLoading ? (
                  <>
                    <span className="ml-2">جاري الحذف...</span>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                  </>
                ) : (
                  <>
                    <FiTrash2 className="ml-2" />
                    تأكيد الحذف
                  </>
                )}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  // مكون نافذة تغيير كلمة المرور
  const PasswordModal = () => (
    <AnimatePresence>
      {showPasswordModal && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-lg shadow-xl max-w-md w-full p-6"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
          >
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-gray-900">
                تغيير كلمة المرور
              </h3>
              <button
                onClick={() => setShowPasswordModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <FiX size={24} />
              </button>
            </div>

            {passwordError && (
              <div className="bg-red-50 border-r-4 border-red-500 text-red-700 p-3 rounded mb-4">
                {passwordError}
              </div>
            )}

            {passwordSuccess && (
              <div className="bg-green-50 border-r-4 border-green-500 text-green-700 p-3 rounded mb-4 flex items-center">
                <FiCheck className="ml-2" />
                {passwordSuccess}
              </div>
            )}

            <div className="mb-4">
              <label
                className="block text-gray-700 text-sm font-bold mb-2"
                htmlFor="new-password"
              >
                كلمة المرور الجديدة
              </label>
              <input
                id="new-password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="أدخل كلمة المرور الجديدة"
                disabled={passwordLoading}
              />
            </div>

            <div className="mb-6">
              <label
                className="block text-gray-700 text-sm font-bold mb-2"
                htmlFor="confirm-password"
              >
                تأكيد كلمة المرور
              </label>
              <input
                id="confirm-password"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="أدخل كلمة المرور مرة أخرى"
                disabled={passwordLoading}
              />
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleChangePassword}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors flex items-center justify-center"
                disabled={passwordLoading}
              >
                {passwordLoading ? (
                  <>
                    <span className="ml-2">جاري التغيير...</span>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                  </>
                ) : (
                  <>
                    <FiLock className="ml-2" />
                    تغيير كلمة المرور
                  </>
                )}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  const handleToggleActive = async (subscriber) => {
    try {
      const newStatus = subscriber.is_active === false ? true : false;
      const { error } = await supabase
        .from("users")
        .update({ is_active: newStatus })
        .eq("id", subscriber.id);
      if (error) throw error;
      setSubscribers(
        subscribers.map((s) =>
          s.id === subscriber.id ? { ...s, is_active: newStatus } : s
        )
      );
    } catch (error) {
      alert("حدث خطأ أثناء تغيير حالة الحساب");
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Header userRole="admin" />
      <DeleteModal />
      <PasswordModal />

      {/* الرأس */}
      <motion.div
        className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center mb-4 md:mb-0">
              <div className="bg-white p-3 rounded-full shadow-md">
                <FiUsers className="h-6 w-6 text-indigo-600" />
              </div>
              <div className="mr-4">
                <h1 className="text-2xl font-bold">إدارة المشتركين</h1>
                <p className="text-indigo-200">
                  عرض وإدارة جميع المشتركين في النظام
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <motion.button
                onClick={exportToCSV}
                className="flex items-center justify-center bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-colors duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                disabled={filteredSubscribers.length === 0}
              >
                <FiDownload className="ml-2" />
                تصدير CSV
              </motion.button>
              <Link href="/admin/subscribers/add">
                <motion.button
                  className="flex items-center justify-center bg-white text-indigo-600 px-4 py-2 rounded-lg transition-colors duration-300 hover:bg-opacity-90"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FiPlus className="ml-2" />
                  إضافة مشترك
                </motion.button>
              </Link>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* شريط البحث والفلترة */}
        <motion.div
          className="bg-white rounded-lg shadow-md p-4 mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <FiSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="البحث عن مشتركين..."
                className="block w-full pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-right"
              />
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="flex items-center">
                <span className="text-sm text-gray-500 ml-2">ترتيب حسب:</span>
                <select
                  value={sortField}
                  onChange={(e) => setSortField(e.target.value)}
                  className="border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 py-1 px-2"
                >
                  <option value="created_at">تاريخ التسجيل</option>
                  <option value="name">الاسم</option>

                  <option value="phone">رقم الهاتف</option>
                </select>
              </div>
              <button
                onClick={() =>
                  setSortDirection(sortDirection === "asc" ? "desc" : "asc")
                }
                className="p-1 rounded-md hover:bg-gray-100"
              >
                {sortDirection === "asc" ? "↑" : "↓"}
              </button>
              <button
                onClick={fetchSubscribers}
                className="flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md transition-colors duration-300"
              >
                <FiRefreshCw className="ml-1" size={16} />
                تحديث
              </button>
            </div>
          </div>
        </motion.div>

        {/* جدول المشتركين */}
        {loading ? (
          <div className="bg-white shadow-md rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل بيانات المشتركين...</p>
          </div>
        ) : filteredSubscribers.length === 0 ? (
          <div className="bg-white shadow-md rounded-lg p-6 text-center">
            <div className="bg-gray-100 p-4 rounded-full inline-block mb-4">
              <FiUsers className="h-8 w-8 text-gray-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              لا يوجد مشتركين
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm
                ? "لا توجد نتائج مطابقة لبحثك"
                : "لم يتم العثور على أي مشتركين في النظام"}
            </p>
            <Link href="/admin/subscribers/add">
              <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <FiPlus className="ml-2 -mr-1 h-5 w-5" />
                إضافة مشترك جديد
              </button>
            </Link>
          </div>
        ) : (
          <motion.div
            className="bg-white shadow-md rounded-lg overflow-hidden"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      المشترك
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      رقم الهاتف
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      الإحصائيات
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      تاريخ التسجيل
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      حالة الحساب
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSubscribers.map((subscriber) => (
                    <motion.tr
                      key={subscriber.id}
                      variants={itemVariants}
                      whileHover={{ backgroundColor: "#f9fafb" }}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center">
                            <span className="text-indigo-600 font-semibold text-lg">
                              {subscriber.name
                                ? subscriber.name.charAt(0).toUpperCase()
                                : "U"}
                            </span>
                          </div>
                          <div className="mr-4">
                            <div className="text-sm font-medium text-gray-900">
                              {subscriber.name || "بدون اسم"}
                            </div>
                            <div className="text-sm text-gray-500">
                              مشترك منذ{" "}
                              {new Date(
                                subscriber.created_at
                              ).toLocaleDateString("ar-SA")}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          <span className="inline-flex items-center">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4 ml-1 text-gray-400"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                            </svg>
                            {subscriber.phone || "لا يوجد رقم هاتف"}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          <div className="text-sm text-gray-900 flex items-center">
                            <FiMail className="ml-1 text-indigo-500" />
                            <span>الدعوات: </span>
                            <span className="font-semibold mr-1">
                              {subscriberStats[subscriber.id]?.invitations || 0}
                            </span>
                          </div>
                          <div className="text-sm text-gray-900 flex items-center">
                            <FiUsers className="ml-1 text-green-500" />
                            <span>المدعوين: </span>
                            <span className="font-semibold mr-1">
                              {subscriberStats[subscriber.id]?.guests || 0}
                            </span>
                          </div>
                          <div className="text-sm text-gray-900 flex items-center">
                            <FiCheck className="ml-1 text-purple-500" />
                            <span>الحضور: </span>
                            <span className="font-semibold mr-1">
                              {subscriberStats[subscriber.id]?.attendance || 0}
                            </span>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          <FiCalendar className="ml-1 text-gray-400" />
                          {new Date(subscriber.created_at).toLocaleDateString(
                            "ar-SA"
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span
                          className={`inline-block px-2 py-1 rounded-full text-xs font-bold ${
                            subscriber.is_active === false
                              ? "bg-yellow-100 text-yellow-700"
                              : "bg-green-100 text-green-700"
                          }`}
                        >
                          {subscriber.is_active === false ? "معلق" : "نشط"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => handlePasswordClick(subscriber)}
                            className="text-indigo-600 hover:text-indigo-900 bg-indigo-50 p-2 rounded-md transition-colors"
                            title="تغيير كلمة المرور"
                          >
                            <FiLock size={18} />
                          </button>
                          <Link
                            href={`/admin/subscribers/${subscriber.id}`}
                            className="text-blue-600 hover:text-blue-900 bg-blue-50 p-2 rounded-md transition-colors"
                            title="عرض التفاصيل"
                          >
                            <FiEye size={18} />
                          </Link>
                          <Link
                            href={`/admin/subscribers/edit/${subscriber.id}`}
                            className="text-green-600 hover:text-green-900 bg-green-50 p-2 rounded-md transition-colors"
                            title="تعديل"
                          >
                            <FiEdit size={18} />
                          </Link>
                          <button
                            onClick={() => handleDeleteClick(subscriber)}
                            className="text-red-600 hover:text-red-900 bg-red-50 p-2 rounded-md transition-colors"
                            title="حذف"
                          >
                            <FiTrash2 size={18} />
                          </button>
                          <button
                            onClick={() => handleToggleActive(subscriber)}
                            className={`ml-2 ${
                              subscriber.is_active === false
                                ? "bg-green-500 text-white hover:bg-green-600"
                                : "bg-yellow-500 text-white hover:bg-yellow-600"
                            } px-3 py-1 rounded-md`}
                          >
                            {subscriber.is_active === false ? "تفعيل" : "تعليق"}
                          </button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200">
              <div className="text-sm text-gray-700">
                إجمالي المشتركين:{" "}
                <span className="font-medium">
                  {filteredSubscribers.length}
                </span>
                {searchTerm && (
                  <span className="mr-2">
                    (تم تصفية النتائج من أصل{" "}
                    <span className="font-medium">{subscribers.length}</span>)
                  </span>
                )}
              </div>
              <button
                onClick={() => setSearchTerm("")}
                className={`text-sm text-indigo-600 hover:text-indigo-900 ${
                  !searchTerm && "opacity-0 pointer-events-none"
                }`}
              >
                إلغاء التصفية
              </button>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
