// دوال مساعدة للداشبورد
import { supabase } from "./supabase";

/**
 * جلب إحصائيات المستخدم بطريقة آمنة
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Object>} - الإحصائيات
 */
export async function fetchUserStats(userId) {
  try {
    if (!userId) {
      throw new Error("معرف المستخدم مطلوب");
    }

    // جلب الدعوات
    const { data: invitationsData, error: invitationsError } = await supabase
      .from("invitations")
      .select("id, title, status, created_at")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (invitationsError) {
      console.error("Error fetching invitations:", invitationsError);
      throw new Error("فشل في جلب الدعوات");
    }

    const invitations = invitationsData || [];
    const invitationIds = invitations.map((inv) => inv.id);

    // إذا لم توجد دعوات، إرجاع إحصائيات فارغة
    if (invitationIds.length === 0) {
      return {
        invitations,
        stats: {
          invitations: 0,
          guests: 0,
          attendance: 0,
          accepted: 0,
          declined: 0,
          pending: 0,
        },
      };
    }

    // جلب إحصائيات الضيوف بطريقة آمنة
    const guestsStats = await fetchGuestsStats(invitationIds);

    return {
      invitations,
      stats: {
        invitations: invitations.length,
        guests: guestsStats.total,
        attendance: guestsStats.attended,
        accepted: guestsStats.accepted,
        declined: guestsStats.declined,
        pending: guestsStats.pending,
      },
    };
  } catch (error) {
    console.error("Error in fetchUserStats:", error);
    throw error;
  }
}

/**
 * جلب إحصائيات الضيوف بطريقة آمنة مع معالجة الأخطاء
 * @param {Array} invitationIds - معرفات الدعوات
 * @returns {Promise<Object>} - إحصائيات الضيوف
 */
async function fetchGuestsStats(invitationIds) {
  const defaultStats = {
    total: 0,
    accepted: 0,
    attended: 0,
    declined: 0,
    pending: 0,
  };

  try {
    // جلب جميع الضيوف مرة واحدة لتحسين الأداء
    const { data: allGuests, error: guestsError } = await supabase
      .from("guests")
      .select("status, attended")
      .in("invitation_id", invitationIds);

    if (guestsError) {
      console.error("Error fetching guests:", guestsError);
      return defaultStats;
    }

    if (!allGuests || allGuests.length === 0) {
      return defaultStats;
    }

    // حساب الإحصائيات من البيانات المجلبة
    const stats = {
      total: allGuests.length,
      accepted: 0,
      declined: 0,
      pending: 0,
      attended: 0,
    };

    allGuests.forEach((guest) => {
      // تطبيع الحالة
      const status = normalizeGuestStatus(guest.status);

      if (status === "accepted") {
        stats.accepted++;
      } else if (status === "declined") {
        stats.declined++;
      } else {
        stats.pending++;
      }

      // حساب الحضور الفعلي
      if (guest.attended === true) {
        stats.attended++;
      }
    });

    return stats;
  } catch (error) {
    console.error("Error in fetchGuestsStats:", error);
    return defaultStats;
  }
}

/**
 * تطبيع حالة الضيف
 * @param {string} status - الحالة الأصلية
 * @returns {string} - الحالة المطبعة
 */
function normalizeGuestStatus(status) {
  if (!status || status === "") return "pending";

  const normalized = status.toLowerCase().trim();

  // تحويل confirmed إلى accepted
  if (normalized === "confirmed") return "accepted";

  // التأكد من أن الحالة صحيحة
  if (["pending", "accepted", "declined"].includes(normalized)) {
    return normalized;
  }

  // إذا كانت غير معروفة، إرجاع pending
  return "pending";
}

/**
 * التحقق من صحة معرف المستخدم وإرجاع بيانات المستخدم
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Object|null>} - بيانات المستخدم أو null
 */
export async function validateUserId(userId) {
  try {
    if (!userId) {
      return null;
    }

    const { data: user, error } = await supabase
      .from("users")
      .select("id, name, phone, role, is_active")
      .eq("id", userId)
      .single();

    if (error || !user) {
      console.error("User validation failed:", error);
      return null;
    }

    if (!user.is_active) {
      console.warn("User is not active:", userId);
      return null;
    }

    return user;
  } catch (error) {
    console.error("Error validating user ID:", error);
    return null;
  }
}

/**
 * معالج أخطاء عام للداشبورد
 * @param {Error} error - الخطأ
 * @param {string} context - سياق الخطأ
 * @returns {string} - رسالة خطأ مناسبة للمستخدم
 */
export function handleDashboardError(error, context = "عملية غير محددة") {
  console.error(`Dashboard error in ${context}:`, error);

  // رسائل خطأ مخصصة حسب نوع الخطأ
  if (error.message?.includes("JWT")) {
    return "انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.";
  }

  if (error.message?.includes("network")) {
    return "مشكلة في الاتصال بالإنترنت. يرجى المحاولة مرة أخرى.";
  }

  if (error.message?.includes("permission")) {
    return "ليس لديك صلاحية للوصول إلى هذه البيانات.";
  }

  // رسالة عامة
  return `حدث خطأ أثناء ${context}. يرجى المحاولة مرة أخرى.`;
}
