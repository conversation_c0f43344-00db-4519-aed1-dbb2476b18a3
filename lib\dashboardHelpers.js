// دوال مساعدة للداشبورد
import { supabase } from "./supabase";

/**
 * جلب إحصائيات المستخدم بطريقة آمنة
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Object>} - الإحصائيات
 */
export async function fetchUserStats(userId) {
  console.log("fetchUserStats called with userId:", userId);

  try {
    if (!userId) {
      console.error("No userId provided to fetchUserStats");
      throw new Error("معرف المستخدم مطلوب");
    }

    // التحقق من صحة معرف المستخدم أولاً
    const userValidation = await validateUserId(userId);
    if (!userValidation) {
      console.error("User validation failed for userId:", userId);
      throw new Error("المستخدم غير موجود أو غير مفعل");
    }

    console.log("User validation successful:", userValidation);

    // جلب الدعوات مع تفاصيل إضافية
    console.log("Fetching invitations for user:", userId);
    const { data: invitationsData, error: invitationsError } = await supabase
      .from("invitations")
      .select(
        `
        id,
        title,
        status,
        created_at,
        updated_at,
        event_date,
        location,
        description,
        template_id,
        additional_data
      `
      )
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (invitationsError) {
      console.error("Error fetching invitations:", invitationsError);
      throw new Error(`فشل في جلب الدعوات: ${invitationsError.message}`);
    }

    console.log("Invitations fetched:", invitationsData?.length || 0);
    const invitations = invitationsData || [];
    const invitationIds = invitations.map((inv) => inv.id);

    // إذا لم توجد دعوات، إرجاع إحصائيات فارغة
    if (invitationIds.length === 0) {
      return {
        invitations,
        stats: {
          invitations: 0,
          guests: 0,
          attendance: 0,
          accepted: 0,
          declined: 0,
          pending: 0,
        },
      };
    }

    // جلب إحصائيات الضيوف لكل دعوة
    const invitationsWithStats = await Promise.all(
      invitations.map(async (invitation) => {
        try {
          const guestStats = await fetchGuestsStats([invitation.id]);
          return {
            ...invitation,
            guest_count: guestStats.total,
            accepted_count: guestStats.accepted,
            declined_count: guestStats.declined,
            pending_count: guestStats.pending,
            attended_count: guestStats.attended,
          };
        } catch (error) {
          console.error(
            `Error fetching stats for invitation ${invitation.id}:`,
            error
          );
          return {
            ...invitation,
            guest_count: 0,
            accepted_count: 0,
            declined_count: 0,
            pending_count: 0,
            attended_count: 0,
          };
        }
      })
    );

    // جلب إحصائيات الضيوف الإجمالية
    console.log("Fetching overall guest stats for all invitations");
    const guestsStats = await fetchGuestsStats(invitationIds);

    const result = {
      invitations: invitationsWithStats,
      stats: {
        invitations: invitations.length,
        guests: guestsStats.total,
        attendance: guestsStats.attended,
        accepted: guestsStats.accepted,
        declined: guestsStats.declined,
        pending: guestsStats.pending,
      },
    };

    console.log("fetchUserStats completed successfully:", result);
    return result;
  } catch (error) {
    console.error("Error in fetchUserStats:", error);

    // إرجاع بيانات افتراضية بدلاً من رمي الخطأ
    const defaultResult = {
      invitations: [],
      stats: {
        invitations: 0,
        guests: 0,
        attendance: 0,
        accepted: 0,
        declined: 0,
        pending: 0,
      },
    };

    console.log("Returning default result due to error:", defaultResult);
    return defaultResult;
  }
}

/**
 * جلب إحصائيات الضيوف بطريقة آمنة مع معالجة الأخطاء
 * @param {Array} invitationIds - معرفات الدعوات
 * @returns {Promise<Object>} - إحصائيات الضيوف
 */
async function fetchGuestsStats(invitationIds) {
  console.log("fetchGuestsStats called with invitationIds:", invitationIds);

  const defaultStats = {
    total: 0,
    accepted: 0,
    attended: 0,
    declined: 0,
    pending: 0,
  };

  try {
    if (!invitationIds || invitationIds.length === 0) {
      console.log("No invitation IDs provided, returning default stats");
      return defaultStats;
    }

    // جلب جميع الضيوف مرة واحدة لتحسين الأداء
    console.log("Fetching guests for invitations:", invitationIds);
    const { data: allGuests, error: guestsError } = await supabase
      .from("guests")
      .select("status, attended")
      .in("invitation_id", invitationIds);

    if (guestsError) {
      console.error("Error fetching guests:", guestsError);
      return defaultStats;
    }

    console.log("Guests fetched:", allGuests?.length || 0);

    if (!allGuests || allGuests.length === 0) {
      console.log("No guests found, returning default stats");
      return defaultStats;
    }

    // حساب الإحصائيات من البيانات المجلبة
    const stats = {
      total: allGuests.length,
      accepted: 0,
      declined: 0,
      pending: 0,
      attended: 0,
    };

    allGuests.forEach((guest, index) => {
      // تطبيع الحالة
      const status = normalizeGuestStatus(guest.status);
      console.log(
        `Guest ${index + 1}: status=${
          guest.status
        } -> normalized=${status}, attended=${guest.attended}`
      );

      if (status === "accepted") {
        stats.accepted++;
      } else if (status === "declined") {
        stats.declined++;
      } else {
        stats.pending++;
      }

      // حساب الحضور الفعلي
      if (guest.attended === true) {
        stats.attended++;
      }
    });

    console.log("Final guest stats:", stats);
    return stats;
  } catch (error) {
    console.error("Error in fetchGuestsStats:", error);
    return defaultStats;
  }
}

/**
 * تطبيع حالة الضيف
 * @param {string} status - الحالة الأصلية
 * @returns {string} - الحالة المطبعة
 */
function normalizeGuestStatus(status) {
  if (!status || status === "") return "pending";

  const normalized = status.toLowerCase().trim();

  // تحويل confirmed إلى accepted
  if (normalized === "confirmed") return "accepted";

  // التأكد من أن الحالة صحيحة
  if (["pending", "accepted", "declined"].includes(normalized)) {
    return normalized;
  }

  // إذا كانت غير معروفة، إرجاع pending
  return "pending";
}

/**
 * التحقق من صحة معرف المستخدم وإرجاع بيانات المستخدم
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Object|null>} - بيانات المستخدم أو null
 */
export async function validateUserId(userId) {
  try {
    if (!userId) {
      return null;
    }

    const { data: user, error } = await supabase
      .from("users")
      .select("id, name, phone, role, is_active")
      .eq("id", userId)
      .single();

    if (error || !user) {
      console.error("User validation failed:", error);
      return null;
    }

    if (!user.is_active) {
      console.warn("User is not active:", userId);
      return null;
    }

    return user;
  } catch (error) {
    console.error("Error validating user ID:", error);
    return null;
  }
}

/**
 * معالج أخطاء عام للداشبورد
 * @param {Error} error - الخطأ
 * @param {string} context - سياق الخطأ
 * @returns {string} - رسالة خطأ مناسبة للمستخدم
 */
export function handleDashboardError(error, context = "عملية غير محددة") {
  console.error(`Dashboard error in ${context}:`, error);

  // رسائل خطأ مخصصة حسب نوع الخطأ
  if (error.message?.includes("JWT")) {
    return "انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.";
  }

  if (error.message?.includes("network")) {
    return "مشكلة في الاتصال بالإنترنت. يرجى المحاولة مرة أخرى.";
  }

  if (error.message?.includes("permission")) {
    return "ليس لديك صلاحية للوصول إلى هذه البيانات.";
  }

  // رسالة عامة
  return `حدث خطأ أثناء ${context}. يرجى المحاولة مرة أخرى.`;
}
