// اختبار عرض حالات المدعوين بعد الإصلاح

const { supabase } = require('./lib/supabase');

async function testStatusDisplay() {
  console.log('🧪 بدء اختبار عرض حالات المدعوين...\n');

  try {
    // 1. اختبار جلب جميع الضيوف
    console.log('1️⃣ اختبار جلب جميع الضيوف...');
    const { data: allGuests, error: guestsError } = await supabase
      .from('guests')
      .select('name, phone, status, attended, attended_at');

    if (guestsError) {
      console.error('❌ خطأ في جلب الضيوف:', guestsError);
      return;
    }

    console.log(`✅ تم جلب ${allGuests.length} ضيف بنجاح`);
    
    // عرض عينة من البيانات
    allGuests.slice(0, 3).forEach((guest, index) => {
      console.log(`   ${index + 1}. ${guest.name} - الحالة: ${guest.status} - الحضور: ${guest.attended}`);
    });

    // 2. اختبار إحصائيات الحالات
    console.log('\n2️⃣ اختبار إحصائيات الحالات...');
    const statusStats = {
      total: allGuests.length,
      pending: allGuests.filter(g => !g.status || g.status === 'pending').length,
      accepted: allGuests.filter(g => g.status === 'accepted').length,
      declined: allGuests.filter(g => g.status === 'declined').length,
      attended: allGuests.filter(g => g.attended === true).length
    };

    console.log('📊 الإحصائيات:');
    console.log(`   المجموع: ${statusStats.total}`);
    console.log(`   في الانتظار: ${statusStats.pending}`);
    console.log(`   قبلوا: ${statusStats.accepted}`);
    console.log(`   اعتذروا: ${statusStats.declined}`);
    console.log(`   حضروا فعلياً: ${statusStats.attended}`);

    // 3. اختبار view الإحصائيات
    console.log('\n3️⃣ اختبار view الإحصائيات...');
    const { data: statsView, error: statsError } = await supabase
      .from('guest_stats_view')
      .select('*');

    if (statsError) {
      console.error('❌ خطأ في جلب إحصائيات view:', statsError);
    } else {
      console.log(`✅ تم جلب إحصائيات ${statsView.length} دعوة من view`);
      statsView.forEach((stat, index) => {
        console.log(`   ${index + 1}. دعوة ${stat.invitation_id}: ${stat.total_guests} ضيف، ${stat.accepted_count} قبلوا، ${stat.attended_count} حضروا`);
      });
    }

    // 4. اختبار الحالات المختلفة
    console.log('\n4️⃣ اختبار الحالات المختلفة...');
    const statusTypes = ['pending', 'accepted', 'declined'];
    
    for (const status of statusTypes) {
      const count = allGuests.filter(g => g.status === status).length;
      console.log(`   ${status}: ${count} ضيف`);
    }

    // 5. اختبار الحضور الفعلي
    console.log('\n5️⃣ اختبار الحضور الفعلي...');
    const attendedGuests = allGuests.filter(g => g.attended === true);
    const notAttendedGuests = allGuests.filter(g => g.attended === false);
    
    console.log(`   حضروا: ${attendedGuests.length} ضيف`);
    console.log(`   لم يحضروا: ${notAttendedGuests.length} ضيف`);

    // 6. اختبار البيانات الشاذة
    console.log('\n6️⃣ اختبار البيانات الشاذة...');
    const invalidStatuses = allGuests.filter(g => 
      g.status && !['pending', 'accepted', 'declined'].includes(g.status)
    );
    
    if (invalidStatuses.length > 0) {
      console.log(`⚠️  وجدت ${invalidStatuses.length} حالة غير صحيحة:`);
      invalidStatuses.forEach(guest => {
        console.log(`   - ${guest.name}: ${guest.status}`);
      });
    } else {
      console.log('✅ جميع الحالات صحيحة');
    }

    // 7. اختبار النسب المئوية
    console.log('\n7️⃣ اختبار النسب المئوية...');
    const acceptanceRate = statusStats.total > 0 ? 
      Math.round((statusStats.accepted / statusStats.total) * 100) : 0;
    const attendanceRate = statusStats.accepted > 0 ? 
      Math.round((statusStats.attended / statusStats.accepted) * 100) : 0;

    console.log(`   معدل القبول: ${acceptanceRate}%`);
    console.log(`   معدل الحضور: ${attendanceRate}%`);

    console.log('\n🎉 تم الانتهاء من جميع الاختبارات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ عام في الاختبار:', error);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testStatusDisplay();
}

module.exports = { testStatusDisplay };
