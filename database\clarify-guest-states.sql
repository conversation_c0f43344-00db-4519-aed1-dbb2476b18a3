-- توضيح حالات المدعوين في النظام
-- هناك فرق بين حالة الرد على الدعوة وحالة الحضور الفعلي

-- 1. إضافة تعليقات توضيحية للأعمدة الموجودة
COMMENT ON COLUMN public.guests.status IS 'حالة الرد على الدعوة (يحددها الضيف): pending = لم يرد بعد، accepted = قبل الدعوة، declined = اعتذر عن الحضور';
COMMENT ON COLUMN public.guests.attended IS 'حالة الحضور الفعلي (يحددها المستخدم/صاحب الدعوة): true = حضر فعلاً، false = لم يحضر';
COMMENT ON COLUMN public.guests.attended_at IS 'وقت تسجيل الحضور الفعلي';

-- 2. التأكد من وجود العمود attended
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'guests' 
        AND column_name = 'attended'
    ) THEN
        ALTER TABLE public.guests ADD COLUMN attended BOOLEAN DEFAULT false;
        RAISE NOTICE 'تم إضافة العمود attended';
    ELSE
        RAISE NOTICE 'العمود attended موجود بالفعل';
    END IF;
END $$;

-- 3. التأكد من وجود العمود attended_at
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'guests' 
        AND column_name = 'attended_at'
    ) THEN
        ALTER TABLE public.guests ADD COLUMN attended_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'تم إضافة العمود attended_at';
    ELSE
        RAISE NOTICE 'العمود attended_at موجود بالفعل';
    END IF;
END $$;

-- 4. إضافة قيود للتأكد من صحة البيانات
-- التأكد من أن status يحتوي على قيم صحيحة فقط
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'guests_status_check'
    ) THEN
        ALTER TABLE public.guests 
        ADD CONSTRAINT guests_status_check 
        CHECK (status IN ('pending', 'accepted', 'declined'));
        RAISE NOTICE 'تم إضافة قيد التحقق من status';
    ELSE
        RAISE NOTICE 'قيد التحقق من status موجود بالفعل';
    END IF;
END $$;

-- 5. إنشاء view لتوضيح الحالات المختلفة
CREATE OR REPLACE VIEW guests_status_summary AS
SELECT 
    g.id,
    g.name,
    g.phone,
    g.invitation_id,
    g.status as invitation_response,
    CASE 
        WHEN g.status = 'pending' THEN 'لم يرد على الدعوة بعد'
        WHEN g.status = 'accepted' THEN 'قبل الدعوة'
        WHEN g.status = 'declined' THEN 'اعتذر عن الحضور'
        ELSE 'حالة غير معروفة'
    END as invitation_response_arabic,
    g.attended as actually_attended,
    CASE 
        WHEN g.attended = true THEN 'حضر فعلياً'
        WHEN g.attended = false THEN 'لم يحضر'
        ELSE 'غير محدد'
    END as attendance_status_arabic,
    g.attended_at,
    g.created_at,
    g.updated_at,
    -- حالة مركبة توضح الوضع الكامل
    CASE 
        WHEN g.status = 'declined' THEN 'اعتذر عن الحضور'
        WHEN g.status = 'pending' THEN 'لم يرد على الدعوة'
        WHEN g.status = 'accepted' AND g.attended = true THEN 'قبل الدعوة وحضر فعلياً'
        WHEN g.status = 'accepted' AND g.attended = false THEN 'قبل الدعوة لكن لم يحضر'
        ELSE 'حالة غير واضحة'
    END as complete_status
FROM public.guests g;

-- 6. إنشاء دالة لتحديث حالة الحضور
CREATE OR REPLACE FUNCTION mark_guest_attendance(guest_id UUID, attended_status BOOLEAN)
RETURNS BOOLEAN AS $$
DECLARE
    guest_exists BOOLEAN;
BEGIN
    -- التحقق من وجود المدعو
    SELECT EXISTS(SELECT 1 FROM public.guests WHERE id = guest_id) INTO guest_exists;
    
    IF NOT guest_exists THEN
        RAISE EXCEPTION 'المدعو غير موجود';
    END IF;
    
    -- تحديث حالة الحضور
    UPDATE public.guests 
    SET 
        attended = attended_status,
        attended_at = CASE 
            WHEN attended_status = true THEN CURRENT_TIMESTAMP 
            ELSE NULL 
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = guest_id;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql;

-- 7. إنشاء دالة للحصول على إحصائيات الدعوة
CREATE OR REPLACE FUNCTION get_invitation_stats(invitation_id UUID)
RETURNS TABLE(
    total_guests INTEGER,
    pending_responses INTEGER,
    accepted_invitations INTEGER,
    declined_invitations INTEGER,
    actually_attended INTEGER,
    accepted_but_not_attended INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_guests,
        COUNT(CASE WHEN status = 'pending' THEN 1 END)::INTEGER as pending_responses,
        COUNT(CASE WHEN status = 'accepted' THEN 1 END)::INTEGER as accepted_invitations,
        COUNT(CASE WHEN status = 'declined' THEN 1 END)::INTEGER as declined_invitations,
        COUNT(CASE WHEN attended = true THEN 1 END)::INTEGER as actually_attended,
        COUNT(CASE WHEN status = 'accepted' AND attended = false THEN 1 END)::INTEGER as accepted_but_not_attended
    FROM public.guests 
    WHERE guests.invitation_id = get_invitation_stats.invitation_id;
END;
$$ LANGUAGE plpgsql;

-- 8. عرض الهيكل النهائي مع التعليقات
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    COALESCE(col_description(pgc.oid, a.attnum), 'لا يوجد تعليق') as column_comment
FROM information_schema.columns a
JOIN pg_class pgc ON pgc.relname = a.table_name
WHERE a.table_schema = 'public' 
    AND a.table_name = 'guests'
ORDER BY a.ordinal_position;

-- 9. مثال على الاستخدام
SELECT 'مثال على الحالات المختلفة:' as info;
SELECT * FROM guests_status_summary LIMIT 5;

-- 10. إعادة تحميل schema cache
NOTIFY pgrst, 'reload schema';
