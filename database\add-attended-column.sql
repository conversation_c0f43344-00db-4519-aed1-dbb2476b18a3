-- إضافة العمود المفقود 'attended' إلى جدول guests
-- هذا سيحل خطأ "Could not find the 'attended' column"

-- 1. التحقق من الهيكل الحالي لجدول guests
SELECT 
    'Current guests table columns:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'guests'
ORDER BY ordinal_position;

-- 2. إض<PERSON><PERSON><PERSON> العمود attended إذا لم يكن موجوداً
DO $$ 
BEGIN
    -- التحقق من وجود العمود
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'guests' 
        AND column_name = 'attended'
    ) THEN
        -- إضافة العمود
        ALTER TABLE public.guests ADD COLUMN attended BOOLEAN DEFAULT false;
        RAISE NOTICE 'تم إضافة العمود attended بنجاح';
        
        -- إضافة تعليق للعمود
        COMMENT ON COLUMN public.guests.attended IS 'حالة الحضور الفعلي - يحددها المستخدم (صاحب الدعوة)';
        
    ELSE
        RAISE NOTICE 'العمود attended موجود بالفعل';
    END IF;
END $$;

-- 3. التأكد من وجود العمود attended_at أيضاً
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'guests' 
        AND column_name = 'attended_at'
    ) THEN
        ALTER TABLE public.guests ADD COLUMN attended_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'تم إضافة العمود attended_at بنجاح';
        
        COMMENT ON COLUMN public.guests.attended_at IS 'وقت تسجيل الحضور الفعلي';
    ELSE
        RAISE NOTICE 'العمود attended_at موجود بالفعل';
    END IF;
END $$;

-- 4. إضافة فهرس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_guests_attended ON public.guests(attended);
CREATE INDEX IF NOT EXISTS idx_guests_attended_at ON public.guests(attended_at);

-- 5. تحديث البيانات الموجودة للتوافق
-- إذا كان attended_at موجود، نحدث attended إلى true
UPDATE public.guests 
SET attended = true 
WHERE attended_at IS NOT NULL AND (attended IS NULL OR attended = false);

-- 6. إنشاء trigger لتحديث attended_at تلقائياً عند تغيير attended
CREATE OR REPLACE FUNCTION update_attended_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    -- إذا تم تغيير attended إلى true وattended_at فارغ
    IF NEW.attended = true AND OLD.attended = false AND NEW.attended_at IS NULL THEN
        NEW.attended_at = CURRENT_TIMESTAMP;
    -- إذا تم تغيير attended إلى false
    ELSIF NEW.attended = false AND OLD.attended = true THEN
        NEW.attended_at = NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger
DROP TRIGGER IF EXISTS trigger_update_attended_timestamp ON public.guests;
CREATE TRIGGER trigger_update_attended_timestamp
    BEFORE UPDATE ON public.guests
    FOR EACH ROW
    EXECUTE FUNCTION update_attended_timestamp();

-- 7. عرض الهيكل النهائي
SELECT 
    'Final guests table structure:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'guests'
ORDER BY ordinal_position;

-- 8. عرض إحصائيات للتأكد من صحة البيانات
SELECT 
    'Guests statistics after update:' as info,
    COUNT(*) as total_guests,
    COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_invitations,
    COUNT(CASE WHEN status = 'declined' THEN 1 END) as declined_invitations,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_invitations,
    COUNT(CASE WHEN attended = true THEN 1 END) as actually_attended,
    COUNT(CASE WHEN attended_at IS NOT NULL THEN 1 END) as attended_with_timestamp
FROM public.guests;

-- 9. إعادة تحميل schema cache في Supabase
NOTIFY pgrst, 'reload schema';
