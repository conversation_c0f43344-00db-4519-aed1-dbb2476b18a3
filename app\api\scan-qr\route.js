import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request) {
  try {
    const { qrCode, invitationId } = await request.json();

    if (!qrCode || !invitationId) {
      return NextResponse.json(
        { error: 'بيانات غير مكتملة' },
        { status: 400 }
      );
    }

    console.log('Scanning QR Code:', qrCode, 'for invitation:', invitationId);

    // البحث عن المدعو باستخدام رمز QR أولاً
    let { data: guestData, error: guestError } = await supabase
      .from('guests')
      .select('*')
      .eq('qr_code', qrCode)
      .eq('invitation_id', invitationId)
      .single();

    // إذا لم يجد بـ QR code، جرب البحث برقم الهاتف
    if (guestError || !guestData) {
      console.log('QR code not found, trying phone search...');
      
      const { data: phoneGuestData, error: phoneGuestError } = await supabase
        .from('guests')
        .select('*')
        .eq('phone', qrCode)
        .eq('invitation_id', invitationId)
        .single();

      if (phoneGuestError || !phoneGuestData) {
        console.log('Guest not found by phone either');
        return NextResponse.json(
          { 
            success: false, 
            error: 'رمز QR غير صالح أو لا ينتمي لهذه الدعوة' 
          },
          { status: 404 }
        );
      }

      guestData = phoneGuestData;
    }

    console.log('Guest found:', guestData.name, guestData.phone);

    // التحقق من حالة القبول
    if (guestData.status !== 'accepted') {
      return NextResponse.json(
        { 
          success: false, 
          error: 'هذا المدعو لم يقبل الدعوة بعد' 
        },
        { status: 400 }
      );
    }

    // التحقق من الحضور السابق
    if (guestData.attended) {
      console.log('Guest already attended');
      return NextResponse.json({
        success: true,
        alreadyAttended: true,
        guest: guestData,
        message: 'تم تسجيل حضور هذا المدعو مسبقًا'
      });
    }

    // تسجيل الحضور
    const { error: updateError } = await supabase
      .from('guests')
      .update({ 
        attended: true, 
        attended_at: new Date().toISOString() 
      })
      .eq('id', guestData.id);

    if (updateError) {
      console.error('Error updating attendance:', updateError);
      throw new Error('فشل في تسجيل الحضور');
    }

    console.log('Attendance recorded successfully');

    // إرجاع البيانات المحدثة
    const updatedGuest = {
      ...guestData,
      attended: true,
      attended_at: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      alreadyAttended: false,
      guest: updatedGuest,
      message: 'تم تسجيل الحضور بنجاح'
    });

  } catch (error) {
    console.error('Error in scan-qr API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'حدث خطأ في الخادم' 
      },
      { status: 500 }
    );
  }
}
