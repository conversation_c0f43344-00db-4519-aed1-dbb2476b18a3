-- إصلاح سريع فقط للعمود attended بدون إضافة بيانات مكررة

-- 1. التأكد من وجود العمود attended
ALTER TABLE public.guests ADD COLUMN IF NOT EXISTS attended BOOLEAN DEFAULT false;
ALTER TABLE public.guests ADD COLUMN IF NOT EXISTS attended_at TIMESTAMP WITH TIME ZONE;

-- 2. تنظيف قيم status غير الصحيحة
UPDATE public.guests 
SET status = 'pending' 
WHERE status IS NULL OR status = '' OR status NOT IN ('pending', 'accepted', 'declined');

-- 3. إض<PERSON><PERSON>ة قيود التحقق بأمان
ALTER TABLE public.guests DROP CONSTRAINT IF EXISTS guests_status_check;
ALTER TABLE public.guests ADD CONSTRAINT guests_status_check 
CHECK (status IN ('pending', 'accepted', 'declined'));

-- 4. إضا<PERSON>ة تعليقات توضيحية
COMMENT ON COLUMN public.guests.status IS 'رد الضيف على الدعوة (يحدده الضيف): pending = لم يرد، accepted = قبل، declined = اعتذر';
COMMENT ON COLUMN public.guests.attended IS 'الحضور الفعلي (يحدده صاحب الدعوة): true = حضر، false = لم يحضر';
COMMENT ON COLUMN public.guests.attended_at IS 'وقت تسجيل الحضور الفعلي';

-- 5. إنشاء trigger لتحديث attended_at تلقائياً
CREATE OR REPLACE FUNCTION update_attended_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.attended = true AND (OLD.attended IS NULL OR OLD.attended = false) THEN
        NEW.attended_at = CURRENT_TIMESTAMP;
    ELSIF NEW.attended = false THEN
        NEW.attended_at = NULL;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_attended_timestamp ON public.guests;
CREATE TRIGGER trigger_update_attended_timestamp
    BEFORE UPDATE ON public.guests
    FOR EACH ROW
    EXECUTE FUNCTION update_attended_timestamp();

-- 6. إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_guests_status ON public.guests(status);
CREATE INDEX IF NOT EXISTS idx_guests_attended ON public.guests(attended);
CREATE INDEX IF NOT EXISTS idx_guests_attended_at ON public.guests(attended_at);

-- 7. إعادة تحميل schema cache
NOTIFY pgrst, 'reload schema';

-- 8. التحقق من نجاح العملية
SELECT 
    'Database fixed successfully!' as status,
    COUNT(*) as total_guests,
    COUNT(CASE WHEN attended IS NOT NULL THEN 1 END) as guests_with_attended_column,
    COUNT(CASE WHEN status IN ('pending', 'accepted', 'declined') THEN 1 END) as guests_with_valid_status
FROM public.guests;
