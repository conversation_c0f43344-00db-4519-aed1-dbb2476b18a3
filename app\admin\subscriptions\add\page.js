"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  FiUser,
  FiDollarSign,
  FiCalendar,
  FiSave,
  FiArrowLeft,
  FiCreditCard,
} from "react-icons/fi";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { supabase } from "@/lib/supabase";
import { getValidatedUser } from "@/lib/userValidation";

export default function AddSubscription() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [users, setUsers] = useState([]);
  const [formData, setFormData] = useState({
    user_id: "",
    plan_name: "basic",
    monthly_fee: 9.99,
    currency: "USD",
    start_date: new Date().toISOString().split("T")[0],
    end_date: "",
    status: "active",
    auto_renew: true,
    payment_method: "",
    notes: "",
  });

  useEffect(() => {
    checkUserAndLoadData();
  }, []);

  const checkUserAndLoadData = async () => {
    try {
      setLoading(true);

      const validatedUser = await getValidatedUser();
      if (!validatedUser || validatedUser.role !== "admin") {
        router.push("/login");
        return;
      }

      await loadUsers();
    } catch (error) {
      console.error("Error loading data:", error);
      toast.error("خطأ في تحميل البيانات");
    } finally {
      setLoading(false);
    }
  };

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from("users")
        .select("id, name, phone")
        .eq("role", "subscriber")
        .order("name");

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error("Error loading users:", error);
      toast.error("خطأ في تحميل المستخدمين");
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    // تحديث الرسوم تلقائياً حسب الخطة
    if (name === "plan_name") {
      const fees = {
        basic: 9.99,
        standard: 19.99,
        premium: 29.99,
      };
      setFormData((prev) => ({
        ...prev,
        monthly_fee: fees[value] || 9.99,
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.user_id) {
      toast.error("يرجى اختيار المستخدم");
      return;
    }

    try {
      setSaving(true);

      // التحقق من وجود اشتراك نشط للمستخدم
      const { data: existingSubscription } = await supabase
        .from("subscriptions")
        .select("id")
        .eq("user_id", formData.user_id)
        .eq("status", "active")
        .single();

      if (existingSubscription) {
        toast.error("يوجد اشتراك نشط بالفعل لهذا المستخدم");
        return;
      }

      // إنشاء الاشتراك الجديد
      const { data, error } = await supabase
        .from("subscriptions")
        .insert([
          {
            ...formData,
            monthly_fee: parseFloat(formData.monthly_fee),
          },
        ])
        .select()
        .single();

      if (error) throw error;

      // إضافة أول دفعة إذا كان الاشتراك نشط
      if (formData.status === "active") {
        const { error: paymentError } = await supabase
          .from("subscription_payments")
          .insert([
            {
              subscription_id: data.id,
              amount: parseFloat(formData.monthly_fee),
              currency: formData.currency,
              payment_date: formData.start_date,
              payment_method: formData.payment_method || "manual",
              status: "completed",
              notes: "دفعة أولى - إضافة يدوية",
            },
          ]);

        if (paymentError) {
          console.error("Error creating payment:", paymentError);
          // لا نوقف العملية، فقط نسجل الخطأ
        }
      }

      toast.success("تم إنشاء الاشتراك بنجاح");
      router.push("/admin/subscriptions");
    } catch (error) {
      console.error("Error creating subscription:", error);
      toast.error("خطأ في إنشاء الاشتراك");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100"
      dir="rtl"
    >
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-800 mb-2">
              إضافة اشتراك جديد
            </h1>
            <p className="text-gray-600">إنشاء اشتراك جديد لأحد المستخدمين</p>
          </div>
          <button
            onClick={() => router.back()}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <FiArrowLeft className="h-4 w-4" />
            رجوع
          </button>
        </div>

        {/* Form */}
        <motion.div
          className="bg-white rounded-2xl shadow-lg p-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* User Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FiUser className="inline h-4 w-4 ml-1" />
                المستخدم
              </label>
              <select
                name="user_id"
                value={formData.user_id}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">اختر المستخدم</option>
                {users.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.name} - {user.phone || "لا يوجد رقم"}
                  </option>
                ))}
              </select>
            </div>

            {/* Plan Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع الخطة
                </label>
                <select
                  name="plan_name"
                  value={formData.plan_name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="basic">أساسي</option>
                  <option value="standard">قياسي</option>
                  <option value="premium">مميز</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FiDollarSign className="inline h-4 w-4 ml-1" />
                  الرسوم الشهرية
                </label>
                <input
                  type="number"
                  name="monthly_fee"
                  value={formData.monthly_fee}
                  onChange={handleInputChange}
                  step="0.01"
                  min="0"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Dates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FiCalendar className="inline h-4 w-4 ml-1" />
                  تاريخ البداية
                </label>
                <input
                  type="date"
                  name="start_date"
                  value={formData.start_date}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ الانتهاء (اختياري)
                </label>
                <input
                  type="date"
                  name="end_date"
                  value={formData.end_date}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Status and Payment Method */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  حالة الاشتراك
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="active">نشط</option>
                  <option value="inactive">غير نشط</option>
                  <option value="cancelled">ملغي</option>
                  <option value="expired">منتهي الصلاحية</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FiCreditCard className="inline h-4 w-4 ml-1" />
                  طريقة الدفع
                </label>
                <select
                  name="payment_method"
                  value={formData.payment_method}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">اختر طريقة الدفع</option>
                  <option value="credit_card">بطاقة ائتمان</option>
                  <option value="bank_transfer">تحويل بنكي</option>
                  <option value="paypal">PayPal</option>
                  <option value="cash">نقداً</option>
                  <option value="manual">إدخال يدوي</option>
                </select>
              </div>
            </div>

            {/* Auto Renew */}
            <div className="flex items-center">
              <input
                type="checkbox"
                name="auto_renew"
                checked={formData.auto_renew}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="mr-2 block text-sm text-gray-700">
                تجديد تلقائي
              </label>
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ملاحظات
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أي ملاحظات إضافية..."
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={saving}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-8 py-3 rounded-lg flex items-center gap-2 transition-colors"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <FiSave className="h-4 w-4" />
                )}
                {saving ? "جاري الحفظ..." : "حفظ الاشتراك"}
              </button>
            </div>
          </form>
        </motion.div>
      </div>

      <ToastContainer position="top-right" />
    </div>
  );
}
