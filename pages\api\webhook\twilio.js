// pages/api/webhook/twilio.js
import { supabase } from '@/lib/supabase';
import twilio from 'twilio';

export default async function handler(req, res) {
  // التحقق من أن الطلب هو POST
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // استخراج بيانات الرسالة من طلب Twilio
    const { Body, From, To } = req.body;
    
    console.log('Received WhatsApp message:', { Body, From, To });
    
    // استخراج رقم الهاتف من معرف WhatsApp
    const phone = From.replace('whatsapp:', '');
    
    // التحقق من أن الرد هو قبول أو اعتذار
    if (Body.toLowerCase() === 'تأكيد الحضور' || Body.toLowerCase().includes('accept_')) {
      // استخراج معرف الدعوة إذا كان الرد يحتوي عليه
      let invitationId;
      
      if (Body.includes('accept_')) {
        invitationId = Body.split('_')[1];
      } else {
        // البحث عن آخر دعوة تم إرسالها لهذا الرقم
        const { data: latestInvitation, error: invitationError } = await supabase
          .from('attendance')
          .select('invitation_id')
          .eq('phone', phone)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();
        
        if (invitationError || !latestInvitation) {
          console.error('Error finding invitation:', invitationError);
          return res.status(404).json({ error: 'Invitation not found' });
        }
        
        invitationId = latestInvitation.invitation_id;
      }
      
      // تحديث حالة المدعو
      const { error: updateError } = await supabase
        .from('attendance')
        .update({ status: 'accepted' })
        .eq('invitation_id', invitationId)
        .eq('phone', phone);
      
      if (updateError) {
        console.error('Error updating attendee status:', updateError);
        return res.status(500).json({ error: 'Error updating status' });
      }
      
      // إرسال رسالة تأكيد
      const twiml = new twilio.twiml.MessagingResponse();
      twiml.message('شكراً لتأكيد حضوركم! نتطلع لرؤيتكم في المناسبة السعيدة.');
      
      res.setHeader('Content-Type', 'text/xml');
      return res.status(200).send(twiml.toString());
    } 
    else if (Body.toLowerCase() === 'اعتذار' || Body.toLowerCase().includes('decline_')) {
      // استخراج معرف الدعوة إذا كان الرد يحتوي عليه
      let invitationId;
      
      if (Body.includes('decline_')) {
        invitationId = Body.split('_')[1];
      } else {
        // البحث عن آخر دعوة تم إرسالها لهذا الرقم
        const { data: latestInvitation, error: invitationError } = await supabase
          .from('attendance')
          .select('invitation_id')
          .eq('phone', phone)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();
        
        if (invitationError || !latestInvitation) {
          console.error('Error finding invitation:', invitationError);
          return res.status(404).json({ error: 'Invitation not found' });
        }
        
        invitationId = latestInvitation.invitation_id;
      }
      
      // تحديث حالة المدعو
      const { error: updateError } = await supabase
        .from('attendance')
        .update({ status: 'declined' })
        .eq('invitation_id', invitationId)
        .eq('phone', phone);
      
      if (updateError) {
        console.error('Error updating attendee status:', updateError);
        return res.status(500).json({ error: 'Error updating status' });
      }
      
      // إرسال رسالة تأكيد
      const twiml = new twilio.twiml.MessagingResponse();
      twiml.message('شكراً لإعلامنا. نأمل أن نراك في مناسبة أخرى.');
      
      res.setHeader('Content-Type', 'text/xml');
      return res.status(200).send(twiml.toString());
    }
    
    // إذا لم يكن الرد قبول أو اعتذار، نرجع استجابة عامة
    const twiml = new twilio.twiml.MessagingResponse();
    twiml.message('شكراً لرسالتك. للرد على الدعوة، يرجى الرد بكلمة "قبول" أو "اعتذار".');
    
    res.setHeader('Content-Type', 'text/xml');
    return res.status(200).send(twiml.toString());
  } catch (error) {
    console.error('Error processing webhook:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

