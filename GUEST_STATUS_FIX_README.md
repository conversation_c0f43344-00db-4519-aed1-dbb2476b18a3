# إصلاح مشاكل عرض حالات المدعوين

## المشاكل التي تم حلها

### 1. مشاكل قاعدة البيانات
- ✅ إصلاح خطأ `null value in column "message" of relation "notifications" violates not-null constraint`
- ✅ إصلاح خطأ `new row for relation "guests" violates check constraint "guests_status_check"`
- ✅ تنظيف الحالات غير الصحيحة (تحويل "confirmed" إلى "accepted")
- ✅ التأكد من وجود عمود `attended` في جدول `guests`

### 2. مشاكل عرض الحالات
- ✅ إصلاح عرض النصوص العربية للحالات المختلفة
- ✅ إضافة معالجة للحالات الفارغة أو غير المحددة
- ✅ تحسين عرض حالة الحضور الفعلي
- ✅ إضافة مكونات React قابلة لإعادة الاستخدام

### 3. تحسينات الأداء
- ✅ إنشاء فهارس لتحسين سرعة الاستعلامات
- ✅ إنشاء view للإحصائيات السريعة
- ✅ تحسين دوال جلب البيانات

## الملفات المحدثة

### 1. ملفات قاعدة البيانات
- `database/fix-guest-status-display.sql` - إصلاح شامل لقاعدة البيانات
- `database/final-fix.sql` - إصلاح مبسط بدون قيود معقدة

### 2. ملفات المساعدة الجديدة
- `lib/statusHelpers.js` - دوال مساعدة لعرض الحالات والنصوص العربية
- `lib/dashboardHelpers.js` - محدث لاستخدام الدوال الجديدة

### 3. ملفات واجهة المستخدم
- `app/subscriber/invitations/[id]/guests/page.js` - محدث لاستخدام المكونات الجديدة

## المكونات الجديدة

### StatusBadge
مكون لعرض حالة رد المدعو على الدعوة:
```jsx
<StatusBadge status="accepted" size="xs" />
```

### AttendanceBadge  
مكون لعرض حالة الحضور الفعلي:
```jsx
<AttendanceBadge attended={true} size="xs" />
```

### calculateGuestStats
دالة لحساب إحصائيات المدعوين:
```javascript
const stats = calculateGuestStats(guests);
// returns: { total, pending, accepted, declined, attended, percentages... }
```

## الحالات المدعومة

### حالات رد المدعو (status)
- `pending` - لم يرد بعد (أصفر)
- `accepted` - قبل الدعوة (أخضر)  
- `declined` - اعتذر (أحمر)
- `null/empty` - غير محدد (رمادي)

### حالات الحضور الفعلي (attended)
- `true` - حضر فعلياً (أخضر)
- `false` - لم يحضر (رمادي)
- `null` - غير محدد (رمادي)

## الإحصائيات المتاحة

### إحصائيات أساسية
- العدد الإجمالي للمدعوين
- عدد المنتظرين للرد
- عدد الذين قبلوا
- عدد الذين اعتذروا  
- عدد الذين حضروا فعلياً

### النسب المئوية
- معدل القبول = (المقبولين / المجموع) × 100
- معدل الحضور = (الحاضرين / المقبولين) × 100

## قاعدة البيانات

### جدول guests
```sql
- id: UUID (primary key)
- invitation_id: UUID (foreign key)
- name: TEXT
- phone: TEXT
- status: TEXT ('pending', 'accepted', 'declined')
- attended: BOOLEAN (default: false)
- attended_at: TIMESTAMP (auto-updated)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### View: guest_stats_view
```sql
- invitation_id: UUID
- total_guests: BIGINT
- pending_count: BIGINT
- accepted_count: BIGINT
- declined_count: BIGINT
- attended_count: BIGINT
- acceptance_rate: NUMERIC
- attendance_rate: NUMERIC
```

### Triggers
- `trigger_update_attended_timestamp` - يحدث `attended_at` تلقائياً عند تغيير `attended`

### Indexes
- `idx_guests_status_attended` - فهرس مركب على (status, attended)
- `idx_guests_invitation_status` - فهرس مركب على (invitation_id, status)
- `idx_guests_invitation_attended` - فهرس مركب على (invitation_id, attended)

## الاختبار

تشغيل اختبار شامل:
```bash
node test-status-display.js
```

## الاستخدام

### في React Components
```jsx
import { StatusBadge, AttendanceBadge } from '@/lib/statusHelpers';

// عرض حالة المدعو
<StatusBadge status={guest.status} size="xs" />

// عرض حالة الحضور مع إمكانية التفاعل
<button onClick={() => toggleAttendance(guest)}>
  <AttendanceBadge attended={guest.attended} size="xs" />
</button>
```

### في دوال الإحصائيات
```javascript
import { calculateGuestStats, normalizeGuestStatuses } from '@/lib/statusHelpers';

// تنظيف البيانات
const cleanGuests = normalizeGuestStatuses(rawGuests);

// حساب الإحصائيات
const stats = calculateGuestStats(cleanGuests);
```

## ملاحظات مهمة

1. **الفرق بين الحالتين:**
   - `status` = رد الضيف على الدعوة (يحدده الضيف)
   - `attended` = الحضور الفعلي (يحدده صاحب الدعوة)

2. **التوافق مع البيانات القديمة:**
   - يتم تحويل "confirmed" إلى "accepted" تلقائياً
   - القيم الفارغة تصبح "pending"

3. **الأداء:**
   - استخدام الفهارس يحسن سرعة الاستعلامات
   - view الإحصائيات يقلل من تعقيد الاستعلامات

4. **الأمان:**
   - جميع الدوال تتعامل مع القيم الفارغة بأمان
   - معالجة الأخطاء في جميع العمليات
