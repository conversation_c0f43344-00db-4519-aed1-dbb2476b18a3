-- إن<PERSON><PERSON>ء جدول الاشتراكات
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plan_name VARCHAR(100) NOT NULL DEFAULT 'basic',
    monthly_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    start_date DATE NOT NULL DEFAULT CURRENT_DATE,
    end_date DATE,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'cancelled', 'expired')),
    auto_renew BOOLEAN DEFAULT true,
    payment_method VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_start_date ON subscriptions(start_date);
CREATE INDEX IF NOT EXISTS idx_subscriptions_end_date ON subscriptions(end_date);

-- إنشاء جدول سجل المدفوعات
CREATE TABLE IF NOT EXISTS subscription_payments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    subscription_id UUID NOT NULL REFERENCES subscriptions(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50),
    transaction_id VARCHAR(255),
    status VARCHAR(20) NOT NULL DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لجدول المدفوعات
CREATE INDEX IF NOT EXISTS idx_payments_subscription_id ON subscription_payments(subscription_id);
CREATE INDEX IF NOT EXISTS idx_payments_payment_date ON subscription_payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_payments_status ON subscription_payments(status);

-- إضافة بيانات تجريبية للاشتراكات
INSERT INTO subscriptions (user_id, plan_name, monthly_fee, currency, start_date, status, notes)
SELECT 
    u.id,
    CASE 
        WHEN RANDOM() < 0.3 THEN 'premium'
        WHEN RANDOM() < 0.6 THEN 'standard'
        ELSE 'basic'
    END as plan_name,
    CASE 
        WHEN RANDOM() < 0.3 THEN 29.99
        WHEN RANDOM() < 0.6 THEN 19.99
        ELSE 9.99
    END as monthly_fee,
    'USD',
    CURRENT_DATE - INTERVAL '30 days' * FLOOR(RANDOM() * 12),
    CASE 
        WHEN RANDOM() < 0.9 THEN 'active'
        ELSE 'inactive'
    END as status,
    'اشتراك تجريبي'
FROM users u 
WHERE u.role = 'subscriber'
AND NOT EXISTS (SELECT 1 FROM subscriptions s WHERE s.user_id = u.id);

-- إضافة بيانات تجريبية للمدفوعات
INSERT INTO subscription_payments (subscription_id, amount, currency, payment_date, payment_method, status)
SELECT 
    s.id,
    s.monthly_fee,
    s.currency,
    s.start_date + INTERVAL '1 month' * generate_series(0, 
        CASE 
            WHEN s.status = 'active' THEN EXTRACT(MONTH FROM AGE(CURRENT_DATE, s.start_date))::INTEGER
            ELSE FLOOR(RANDOM() * 6)::INTEGER
        END
    ),
    CASE 
        WHEN RANDOM() < 0.4 THEN 'credit_card'
        WHEN RANDOM() < 0.7 THEN 'bank_transfer'
        ELSE 'paypal'
    END,
    'completed'
FROM subscriptions s
WHERE s.status = 'active';

-- إنشاء دالة لحساب الدخل الشهري
CREATE OR REPLACE FUNCTION calculate_monthly_revenue(target_month DATE DEFAULT CURRENT_DATE)
RETURNS TABLE (
    month_year TEXT,
    total_revenue DECIMAL(10,2),
    payment_count INTEGER,
    active_subscriptions INTEGER,
    average_payment DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        TO_CHAR(target_month, 'YYYY-MM') as month_year,
        COALESCE(SUM(sp.amount), 0) as total_revenue,
        COUNT(sp.id)::INTEGER as payment_count,
        COUNT(DISTINCT s.id)::INTEGER as active_subscriptions,
        COALESCE(AVG(sp.amount), 0) as average_payment
    FROM subscription_payments sp
    JOIN subscriptions s ON sp.subscription_id = s.id
    WHERE DATE_TRUNC('month', sp.payment_date) = DATE_TRUNC('month', target_month)
    AND sp.status = 'completed';
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لحساب إحصائيات الاشتراكات
CREATE OR REPLACE FUNCTION get_subscription_stats()
RETURNS TABLE (
    total_subscriptions INTEGER,
    active_subscriptions INTEGER,
    inactive_subscriptions INTEGER,
    monthly_revenue DECIMAL(10,2),
    average_monthly_fee DECIMAL(10,2),
    most_popular_plan TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_subscriptions,
        COUNT(CASE WHEN status = 'active' THEN 1 END)::INTEGER as active_subscriptions,
        COUNT(CASE WHEN status != 'active' THEN 1 END)::INTEGER as inactive_subscriptions,
        COALESCE(SUM(CASE WHEN status = 'active' THEN monthly_fee ELSE 0 END), 0) as monthly_revenue,
        COALESCE(AVG(CASE WHEN status = 'active' THEN monthly_fee END), 0) as average_monthly_fee,
        (
            SELECT plan_name 
            FROM subscriptions 
            WHERE status = 'active' 
            GROUP BY plan_name 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
        ) as most_popular_plan
    FROM subscriptions;
END;
$$ LANGUAGE plpgsql;

-- تحديث الطوابع الزمنية تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_subscriptions_updated_at 
    BEFORE UPDATE ON subscriptions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- منح الصلاحيات
GRANT ALL ON subscriptions TO authenticated;
GRANT ALL ON subscription_payments TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_monthly_revenue TO authenticated;
GRANT EXECUTE ON FUNCTION get_subscription_stats TO authenticated;
