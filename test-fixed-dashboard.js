/**
 * اختبار إصلاح مشكلة الداشبورد
 */

import { supabase } from './lib/supabase.js';

async function testFixedDashboard() {
  console.log('🔧 اختبار إصلاح مشكلة الداشبورد...\n');

  try {
    // 1. اختبار الاستعلام الجديد للدعوات
    console.log('1️⃣ اختبار الاستعلام الجديد للدعوات...');
    
    const { data: invitationsTest, error: invitationsError } = await supabase
      .from("invitations")
      .select(`
        id,
        title,
        status,
        created_at,
        updated_at,
        event_date,
        location,
        description,
        template_id,
        additional_data
      `)
      .limit(5);

    if (invitationsError) {
      console.error('❌ خطأ في الاستعلام الجديد:', invitationsError);
      return;
    }

    console.log('✅ الاستعلام الجديد يعمل بشكل صحيح');
    console.log(`📊 تم جلب ${invitationsTest?.length || 0} دعوة`);
    
    if (invitationsTest && invitationsTest.length > 0) {
      console.log('\n📋 عينة من البيانات:');
      invitationsTest.forEach((inv, index) => {
        console.log(`   ${index + 1}. ${inv.title || 'بدون عنوان'}`);
        console.log(`      - التاريخ: ${inv.event_date || 'غير محدد'}`);
        console.log(`      - الموقع: ${inv.location || 'غير محدد'}`);
        console.log(`      - الحالة: ${inv.status || 'غير محدد'}`);
        console.log('');
      });
    }

    // 2. اختبار دالة fetchUserStats المحدثة
    console.log('2️⃣ اختبار دالة fetchUserStats المحدثة...');
    
    // جلب مستخدم للاختبار
    const { data: testUsers, error: usersError } = await supabase
      .from('users')
      .select('id, name')
      .eq('is_active', true)
      .limit(1);

    if (usersError || !testUsers || testUsers.length === 0) {
      console.log('⚠️ لا يوجد مستخدمون نشطون للاختبار');
      return;
    }

    const testUser = testUsers[0];
    console.log(`👤 اختبار مع المستخدم: ${testUser.name} (${testUser.id})`);

    // استيراد واختبار الدالة
    try {
      const { fetchUserStats } = await import('./lib/dashboardHelpers.js');
      
      console.log('📊 جاري اختبار fetchUserStats...');
      const result = await fetchUserStats(testUser.id);
      
      console.log('✅ fetchUserStats تعمل بنجاح!');
      console.log('📈 النتائج:');
      console.log(`   - عدد الدعوات: ${result.stats.invitations}`);
      console.log(`   - عدد المدعوين: ${result.stats.guests}`);
      console.log(`   - عدد المقبولين: ${result.stats.accepted}`);
      console.log(`   - عدد المرفوضين: ${result.stats.declined}`);
      console.log(`   - عدد المنتظرين: ${result.stats.pending}`);
      console.log(`   - عدد الحاضرين: ${result.stats.attendance}`);
      
      console.log('\n📋 تفاصيل الدعوات:');
      if (result.invitations && result.invitations.length > 0) {
        result.invitations.forEach((inv, index) => {
          console.log(`   ${index + 1}. ${inv.title || 'بدون عنوان'}`);
          console.log(`      - التاريخ: ${inv.event_date || 'غير محدد'}`);
          console.log(`      - المدعوين: ${inv.guest_count || 0}`);
          console.log(`      - المقبولين: ${inv.accepted_count || 0}`);
          console.log(`      - الحاضرين: ${inv.attended_count || 0}`);
        });
      } else {
        console.log('   لا توجد دعوات لهذا المستخدم');
      }
      
    } catch (statsError) {
      console.error('❌ خطأ في fetchUserStats:', statsError);
      console.error('تفاصيل:', statsError.message);
    }

    // 3. اختبار تنسيق التاريخ والوقت
    console.log('\n3️⃣ اختبار تنسيق التاريخ والوقت...');
    
    if (invitationsTest && invitationsTest.length > 0) {
      const sampleInvitation = invitationsTest[0];
      
      if (sampleInvitation.event_date) {
        try {
          const formattedDate = new Date(sampleInvitation.event_date).toLocaleDateString("ar-SA", {
            year: "numeric",
            month: "long",
            day: "numeric",
            weekday: "long",
          });

          const formattedTime = new Date(sampleInvitation.event_date).toLocaleTimeString("ar-SA", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: true,
          });

          console.log('✅ تنسيق التاريخ والوقت يعمل بشكل صحيح');
          console.log(`📅 التاريخ المنسق: ${formattedDate}`);
          console.log(`🕐 الوقت المنسق: ${formattedTime}`);
          
        } catch (dateError) {
          console.error('❌ خطأ في تنسيق التاريخ:', dateError);
        }
      } else {
        console.log('⚠️ لا يوجد تاريخ للاختبار');
      }
    }

    // 4. اختبار البحث
    console.log('\n4️⃣ اختبار وظيفة البحث...');
    
    if (invitationsTest && invitationsTest.length > 0) {
      const searchTerm = 'test';
      const filteredResults = invitationsTest.filter(
        (invitation) =>
          invitation.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          invitation.location?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      console.log('✅ وظيفة البحث تعمل بشكل صحيح');
      console.log(`🔍 البحث عن "${searchTerm}": ${filteredResults.length} نتيجة`);
    }

    console.log('\n✅ جميع الاختبارات مكتملة بنجاح! 🎉');
    console.log('\n📝 الملخص:');
    console.log('   ✅ إصلاح مشكلة العمود المفقود (date, time)');
    console.log('   ✅ استخدام event_date بدلاً من date و time');
    console.log('   ✅ تحديث دالة fetchUserStats');
    console.log('   ✅ إصلاح تنسيق التاريخ والوقت');
    console.log('   ✅ اختبار وظيفة البحث');
    
    console.log('\n🚀 الداشبورد جاهز للاستخدام!');

  } catch (error) {
    console.error('❌ خطأ عام في الاختبار:', error);
    console.error('تفاصيل:', error.message);
    console.error('Stack:', error.stack);
  }
}

// تشغيل الاختبار
testFixedDashboard();
