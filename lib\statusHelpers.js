// دوال مساعدة لعرض حالات المدعوين

/**
 * تحويل حالة المدعو إلى نص عربي
 * @param {string} status - حالة المدعو
 * @returns {Object} - كائن يحتوي على النص والألوان
 */
export function getStatusDisplay(status) {
  const statusMap = {
    pending: {
      text: "لم يرد بعد",
      bgColor: "bg-yellow-100",
      textColor: "text-yellow-800",
      borderColor: "border-yellow-200"
    },
    accepted: {
      text: "قبل الدعوة",
      bgColor: "bg-green-100", 
      textColor: "text-green-800",
      borderColor: "border-green-200"
    },
    declined: {
      text: "اعتذر",
      bgColor: "bg-red-100",
      textColor: "text-red-800", 
      borderColor: "border-red-200"
    },
    confirmed: {
      text: "قبل الدعوة", // نفس accepted
      bgColor: "bg-green-100",
      textColor: "text-green-800",
      borderColor: "border-green-200"
    }
  };

  // إذا كانت الحالة غير معروفة أو فارغة
  if (!status || status === "" || !statusMap[status]) {
    return {
      text: "غير محدد",
      bgColor: "bg-gray-100",
      textColor: "text-gray-800",
      borderColor: "border-gray-200"
    };
  }

  return statusMap[status];
}

/**
 * تحويل حالة الحضور إلى نص عربي
 * @param {boolean} attended - هل حضر أم لا
 * @returns {Object} - كائن يحتوي على النص والألوان
 */
export function getAttendanceDisplay(attended) {
  if (attended === true) {
    return {
      text: "حضر فعلياً",
      bgColor: "bg-green-100",
      textColor: "text-green-800",
      borderColor: "border-green-200"
    };
  } else if (attended === false) {
    return {
      text: "لم يحضر",
      bgColor: "bg-gray-100", 
      textColor: "text-gray-800",
      borderColor: "border-gray-200"
    };
  } else {
    return {
      text: "غير محدد",
      bgColor: "bg-gray-100",
      textColor: "text-gray-600",
      borderColor: "border-gray-200"
    };
  }
}

/**
 * مكون لعرض حالة المدعو
 * @param {Object} props - الخصائص
 * @param {string} props.status - حالة المدعو
 * @param {string} props.size - حجم النص (xs, sm, md, lg)
 * @returns {JSX.Element} - عنصر JSX
 */
export function StatusBadge({ status, size = "xs" }) {
  const display = getStatusDisplay(status);
  
  const sizeClasses = {
    xs: "px-2 py-1 text-xs",
    sm: "px-3 py-1 text-sm", 
    md: "px-4 py-2 text-base",
    lg: "px-5 py-2 text-lg"
  };

  return (
    <span className={`${display.bgColor} ${display.textColor} ${sizeClasses[size]} rounded-full font-medium border ${display.borderColor}`}>
      {display.text}
    </span>
  );
}

/**
 * مكون لعرض حالة الحضور
 * @param {Object} props - الخصائص  
 * @param {boolean} props.attended - هل حضر أم لا
 * @param {string} props.size - حجم النص
 * @returns {JSX.Element} - عنصر JSX
 */
export function AttendanceBadge({ attended, size = "xs" }) {
  const display = getAttendanceDisplay(attended);
  
  const sizeClasses = {
    xs: "px-2 py-1 text-xs",
    sm: "px-3 py-1 text-sm",
    md: "px-4 py-2 text-base", 
    lg: "px-5 py-2 text-lg"
  };

  return (
    <span className={`${display.bgColor} ${display.textColor} ${sizeClasses[size]} rounded-full font-medium border ${display.borderColor}`}>
      {display.text}
    </span>
  );
}

/**
 * حساب إحصائيات المدعوين من مصفوفة البيانات
 * @param {Array} guests - مصفوفة المدعوين
 * @returns {Object} - إحصائيات مفصلة
 */
export function calculateGuestStats(guests) {
  if (!Array.isArray(guests) || guests.length === 0) {
    return {
      total: 0,
      pending: 0,
      accepted: 0,
      declined: 0,
      attended: 0,
      pendingPercentage: 0,
      acceptedPercentage: 0,
      declinedPercentage: 0,
      attendancePercentage: 0
    };
  }

  const total = guests.length;
  const pending = guests.filter(g => !g.status || g.status === "pending").length;
  const accepted = guests.filter(g => g.status === "accepted" || g.status === "confirmed").length;
  const declined = guests.filter(g => g.status === "declined").length;
  const attended = guests.filter(g => g.attended === true).length;

  return {
    total,
    pending,
    accepted,
    declined,
    attended,
    pendingPercentage: total > 0 ? Math.round((pending / total) * 100) : 0,
    acceptedPercentage: total > 0 ? Math.round((accepted / total) * 100) : 0,
    declinedPercentage: total > 0 ? Math.round((declined / total) * 100) : 0,
    attendancePercentage: accepted > 0 ? Math.round((attended / accepted) * 100) : 0
  };
}

/**
 * تنظيف وتوحيد حالات المدعوين
 * @param {Array} guests - مصفوفة المدعوين
 * @returns {Array} - مصفوفة منظفة
 */
export function normalizeGuestStatuses(guests) {
  if (!Array.isArray(guests)) return [];
  
  return guests.map(guest => ({
    ...guest,
    status: normalizeStatus(guest.status),
    attended: normalizeAttendance(guest.attended)
  }));
}

/**
 * تطبيع حالة المدعو
 * @param {string} status - الحالة الأصلية
 * @returns {string} - الحالة المطبعة
 */
function normalizeStatus(status) {
  if (!status || status === "") return "pending";
  
  const normalized = status.toLowerCase().trim();
  
  // تحويل confirmed إلى accepted
  if (normalized === "confirmed") return "accepted";
  
  // التأكد من أن الحالة صحيحة
  if (["pending", "accepted", "declined"].includes(normalized)) {
    return normalized;
  }
  
  // إذا كانت غير معروفة، إرجاع pending
  return "pending";
}

/**
 * تطبيع حالة الحضور
 * @param {any} attended - حالة الحضور الأصلية
 * @returns {boolean} - حالة الحضور المطبعة
 */
function normalizeAttendance(attended) {
  if (attended === true || attended === "true" || attended === 1) return true;
  if (attended === false || attended === "false" || attended === 0) return false;
  return false; // افتراضي
}

/**
 * تنسيق التاريخ للعرض العربي
 * @param {string|Date} date - التاريخ
 * @returns {string} - التاريخ منسق
 */
export function formatArabicDate(date) {
  if (!date) return "غير محدد";
  
  try {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString("ar-SA", {
      year: "numeric",
      month: "long", 
      day: "numeric",
      weekday: "long"
    });
  } catch (error) {
    console.error("Error formatting date:", error);
    return "تاريخ غير صحيح";
  }
}

/**
 * تنسيق الوقت للعرض العربي
 * @param {string|Date} time - الوقت
 * @returns {string} - الوقت منسق
 */
export function formatArabicTime(time) {
  if (!time) return "غير محدد";
  
  try {
    const timeObj = new Date(time);
    return timeObj.toLocaleTimeString("ar-SA", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true
    });
  } catch (error) {
    console.error("Error formatting time:", error);
    return "وقت غير صحيح";
  }
}
