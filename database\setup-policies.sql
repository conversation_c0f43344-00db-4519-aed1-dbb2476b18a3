-- سياسا<PERSON> جدول المستخدمين
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- السماح بالقراءة العامة للمستخدمين
DROP POLICY IF EXISTS "Allow public read for users" ON public.users;
CREATE POLICY "Allow public read for users" ON public.users
    FOR SELECT TO anon, authenticated
    USING (true);

-- السماح بإنشاء مستخدمين جدد فقط إذا كان التسجيل مفتوح أو إذا كان المستخدم أدمن
DROP POLICY IF EXISTS "Allow public insert" ON public.users;
CREATE POLICY "Allow public insert" ON public.users
    FOR INSERT TO anon, authenticated
    WITH CHECK (
        (
            EXISTS (
                SELECT 1 FROM system_settings WHERE allow_new_registrations = true
            )
            AND role = 'subscriber'
        )
        OR (
            auth.jwt() ->> 'role' = 'admin'
        )
    );

-- السماح للمستخدمين بتحديث بياناتهم الخاصة
DROP POLICY IF EXISTS "Users can update own data" ON public.users;
CREATE POLICY "Users can update own data" ON public.users
    FOR UPDATE TO authenticated
    USING (auth.uid() = id);

-- إعادة تعيين سياسات جدول الدعوات
ALTER TABLE public.invitations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.invitations ENABLE ROW LEVEL SECURITY;

-- حذف السياسات القديمة إذا كانت موجودة
DROP POLICY IF EXISTS "Users can view their own invitations" ON public.invitations;
DROP POLICY IF EXISTS "Users can create their own invitations" ON public.invitations;
DROP POLICY IF EXISTS "Users can update their own invitations" ON public.invitations;
DROP POLICY IF EXISTS "Users can delete their own invitations" ON public.invitations;
DROP POLICY IF EXISTS "Admin can manage all invitations" ON public.invitations;

-- سياسة خاصة للمشرفين
CREATE POLICY "Admin can manage all invitations" ON public.invitations
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- سياسة القراءة للمستخدمين العاديين
CREATE POLICY "Users can view their own invitations" ON public.invitations
    FOR SELECT TO authenticated
    USING (
        auth.uid()::text = user_id::text
    );

-- سياسة الإنشاء للمستخدمين
CREATE POLICY "Users can create their own invitations" ON public.invitations
    FOR INSERT TO authenticated
    WITH CHECK (
        auth.uid()::text = user_id::text
    );

-- سياسة التحديث للمستخدمين
CREATE POLICY "Users can update their own invitations" ON public.invitations
    FOR UPDATE TO authenticated
    USING (
        auth.uid()::text = user_id::text
    );

-- سياسة الحذف للمستخدمين
CREATE POLICY "Users can delete their own invitations" ON public.invitations
    FOR DELETE TO authenticated
    USING (
        auth.uid()::text = user_id::text
    );

-- سياسات جدول القوالب
ALTER TABLE public.templates ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Templates are viewable by everyone" ON public.templates
    FOR SELECT
    USING (true);

CREATE POLICY "Only admins can create templates" ON public.templates
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات جدول الإشعارات
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications" ON public.notifications
    FOR INSERT
    WITH CHECK (true);

-- سياسات اضافة المستخدمين  