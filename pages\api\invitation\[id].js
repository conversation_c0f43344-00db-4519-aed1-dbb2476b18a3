// pages/api/invitation/[id].js
import { supabase } from '@/lib/supabase';

export default async function handler(req, res) {
  const { id } = req.query;
  
  // التحقق من وجود معرف الدعوة
  if (!id) {
    return res.status(400).json({ error: 'Invitation ID is required' });
  }
  
  // جلب بيانات الدعوة
  if (req.method === 'GET') {
    try {
      const { data, error } = await supabase
        .from('invitations')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        return res.status(404).json({ error: 'Invitation not found' });
      }
      
      return res.status(200).json(data);
    } catch (error) {
      return res.status(500).json({ error: 'Internal server error' });
    }
  }
  
  // تحديث حالة المدعو
  else if (req.method === 'POST') {
    try {
      const { phone, status } = req.body;
      
      // تحقق من وجود رقم الهاتف والحالة
      if (!phone || !status) {
        return res.status(400).json({ error: 'Phone and status are required' });
      }
      
      // التحقق من أن المدعو موجود
      const { data: attendee, error: attendeeError } = await supabase
        .from('attendance')
        .select('*')
        .eq('invitation_id', id)
        .eq('phone', phone)
        .single();
      
      if (attendeeError || !attendee) {
        // إذا لم يكن المدعو موجودًا، نقوم بإنشائه
        const { data: newAttendee, error: createError } = await supabase
          .from('attendance')
          .insert({
            invitation_id: id,
            phone,
            status,
            attended: false
          })
          .select()
          .single();
        
        if (createError) {
          return res.status(500).json({ error: createError.message });
        }
        
        return res.status(201).json(newAttendee);
      }
      
      // تحديث الحالة
      const { data: updatedAttendee, error: updateError } = await supabase
        .from('attendance')
        .update({ status })
        .eq('id', attendee.id)
        .select()
        .single();
      
      if (updateError) {
        return res.status(500).json({ error: updateError.message });
      }
      
      return res.status(200).json(updatedAttendee);
    } catch (error) {
      console.error('Error updating attendance:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }
  
  // طريقة غير مدعومة
  else {
    return res.status(405).json({ error: 'Method not allowed' });
  }
}


