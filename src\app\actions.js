"use server";

import { supabase } from "@/lib/supabase";
import { revalidatePath } from "next/cache";

// إضافة مشترك جديد
export async function addSubscriber(name, phone, password) {
  const { data, error } = await supabase
    .from("users")
    .insert([{ name, phone, password, role: "subscriber" }]);

  revalidatePath("/admin/subscribers");
  return { data, error };
}

// إنشاء دعوة جديدة
export async function createInvitation(
  userId,
  templateId,
  title,
  date,
  location,
  description
) {
  const { data, error } = await supabase
    .from("invitations")
    .insert([
      {
        user_id: userId,
        template_id: templateId,
        title,
        date,
        location,
        description,
      },
    ]);

  revalidatePath("/dashboard/invitations");
  return { data, error };
}

// إضافة مدعوين للدعوة
export async function addGuests(invitationId, guests) {
  const guestsData = guests.map((phone) => ({
    invitation_id: invitationId,
    phone,
    status: "pending",
    qr_code: generateQRCode(invitationId, phone),
  }));

  const { data, error } = await supabase.from("guests").insert(guestsData);

  revalidatePath("/dashboard/invitations");
  return { data, error };
}

// تحديث حالة المدعو
export async function updateGuestStatus(guestId, status) {
  const { data, error } = await supabase
    .from("guests")
    .update({ status })
    .eq("id", guestId);

  return { data, error };
}

// تسجيل حضور المدعو
export async function markGuestAttendance(qrCode) {
  const { data, error } = await supabase
    .from("guests")
    .update({ attended: true })
    .eq("qr_code", qrCode);

  return { data, error };
}

// دالة مساعدة لإنشاء QR Code
function generateQRCode(invitationId, phone) {
  // هنا يمكنك استخدام مكتبة لإنشاء QR Code
  return `${invitationId}-${phone}-${Date.now()}`;
}

export async function manualSignIn({ phone, password }) {
  // جلب المستخدم من قاعدة البيانات بناءً على رقم الهاتف
  const { data: user, error } = await supabase
    .from("users")
    .select("*")
    .eq("phone", phone)
    .single();

  if (error || !user) {
    return { success: false, message: "رقم الهاتف غير صحيح" };
  }

  // مقارنة كلمة المرور نصيًا فقط
  if (user.password !== password) {
    return { success: false, message: "كلمة المرور غير صحيحة" };
  }

  // تسجيل الدخول ناجح
  return { success: true, user };
}

export async function manualRegisterUser({ name, phone, password }) {
  // حفظ كلمة المرور كنص عادي فقط
  const { data, error } = await supabase
    .from("users")
    .insert([{ name, phone, password, role: "subscriber" }]);
  return { data, error };
}
