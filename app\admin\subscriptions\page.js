"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  FiUsers,
  FiDollarSign,
  FiTrendingUp,
  FiEdit,
  FiTrash2,
  FiPlus,
  FiSearch,
  FiRefreshCw,
} from "react-icons/fi";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { supabase } from "@/lib/supabase";
import { getValidatedUser } from "@/lib/userValidation";

export default function AdminSubscriptions() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [subscriptions, setSubscriptions] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    monthlyRevenue: 0,
    averageFee: 0,
    popularPlan: "",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");

  useEffect(() => {
    checkUserAndLoadData();
  }, []);

  const checkUserAndLoadData = async () => {
    try {
      setLoading(true);

      const validatedUser = await getValidatedUser();
      if (!validatedUser || validatedUser.role !== "admin") {
        router.push("/login");
        return;
      }

      await Promise.all([loadSubscriptions(), loadStats()]);
    } catch (error) {
      console.error("Error loading data:", error);
      toast.error("خطأ في تحميل البيانات");
    } finally {
      setLoading(false);
    }
  };

  const loadSubscriptions = async () => {
    try {
      const { data, error } = await supabase
        .from("subscriptions")
        .select(
          `
          *,
          users (
            id,
            name,
            email,
            phone
          )
        `
        )
        .order("created_at", { ascending: false });

      if (error) throw error;
      setSubscriptions(data || []);
    } catch (error) {
      console.error("Error loading subscriptions:", error);
      toast.error("خطأ في تحميل الاشتراكات");
    }
  };

  const loadStats = async () => {
    try {
      const { data, error } = await supabase.rpc("get_subscription_stats");

      if (error) throw error;

      if (data && data.length > 0) {
        const statsData = data[0];
        setStats({
          total: statsData.total_subscriptions || 0,
          active: statsData.active_subscriptions || 0,
          inactive: statsData.inactive_subscriptions || 0,
          monthlyRevenue: parseFloat(statsData.monthly_revenue || 0),
          averageFee: parseFloat(statsData.average_monthly_fee || 0),
          popularPlan: statsData.most_popular_plan || "غير محدد",
        });
      }
    } catch (error) {
      console.error("Error loading stats:", error);
      toast.error("خطأ في تحميل الإحصائيات");
    }
  };

  const filteredSubscriptions = subscriptions.filter((sub) => {
    const matchesSearch =
      sub.users?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sub.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sub.plan_name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterStatus === "all" || sub.status === filterStatus;

    return matchesSearch && matchesFilter;
  });

  const formatCurrency = (amount, currency = "USD") => {
    return new Intl.NumberFormat("ar-SA", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "expired":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case "active":
        return "نشط";
      case "inactive":
        return "غير نشط";
      case "cancelled":
        return "ملغي";
      case "expired":
        return "منتهي الصلاحية";
      default:
        return status;
    }
  };

  const getPlanColor = (plan) => {
    switch (plan) {
      case "premium":
        return "bg-purple-100 text-purple-800";
      case "standard":
        return "bg-blue-100 text-blue-800";
      case "basic":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPlanText = (plan) => {
    switch (plan) {
      case "premium":
        return "مميز";
      case "standard":
        return "قياسي";
      case "basic":
        return "أساسي";
      default:
        return plan;
    }
  };

  const handleDelete = async (subscriptionId) => {
    if (!confirm("هل أنت متأكد من حذف هذا الاشتراك؟")) return;

    try {
      const { error } = await supabase
        .from("subscriptions")
        .delete()
        .eq("id", subscriptionId);

      if (error) throw error;

      toast.success("تم حذف الاشتراك بنجاح");
      await loadSubscriptions();
      await loadStats();
    } catch (error) {
      console.error("Error deleting subscription:", error);
      toast.error("خطأ في حذف الاشتراك");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">جاري تحميل الاشتراكات...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100"
      dir="rtl"
    >
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            إدارة الاشتراكات
          </h1>
          <p className="text-gray-600">
            إدارة اشتراكات المستخدمين وحساب الدخل الشهري
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">إجمالي الاشتراكات</p>
                <p className="text-3xl font-bold text-gray-800">
                  {stats.total}
                </p>
              </div>
              <FiUsers className="h-12 w-12 text-blue-500" />
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">الاشتراكات النشطة</p>
                <p className="text-3xl font-bold text-green-600">
                  {stats.active}
                </p>
              </div>
              <FiTrendingUp className="h-12 w-12 text-green-500" />
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">الدخل الشهري</p>
                <p className="text-3xl font-bold text-purple-600">
                  {formatCurrency(stats.monthlyRevenue)}
                </p>
              </div>
              <FiDollarSign className="h-12 w-12 text-purple-500" />
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">متوسط الرسوم</p>
                <p className="text-3xl font-bold text-orange-600">
                  {formatCurrency(stats.averageFee)}
                </p>
              </div>
              <FiDollarSign className="h-12 w-12 text-orange-500" />
            </div>
          </motion.div>
        </div>

        {/* Controls */}
        <div className="bg-white rounded-2xl p-6 shadow-lg mb-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-4 flex-1">
              <div className="relative">
                <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث في الاشتراكات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
                <option value="cancelled">ملغي</option>
                <option value="expired">منتهي الصلاحية</option>
              </select>
            </div>

            <div className="flex gap-2">
              <button
                onClick={() => router.push("/admin/subscriptions/add")}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiPlus className="h-4 w-4" />
                إضافة اشتراك
              </button>

              <button
                onClick={checkUserAndLoadData}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiRefreshCw className="h-4 w-4" />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* Subscriptions Table */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                    المستخدم
                  </th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                    الخطة
                  </th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                    الرسوم الشهرية
                  </th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                    الحالة
                  </th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                    تاريخ البداية
                  </th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredSubscriptions.map((subscription) => (
                  <motion.tr
                    key={subscription.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4">
                      <div>
                        <div className="font-medium text-gray-900">
                          {subscription.users?.name || "غير محدد"}
                        </div>
                        <div className="text-sm text-gray-500">
                          {subscription.users?.phone ||
                            subscription.users?.email}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPlanColor(
                          subscription.plan_name
                        )}`}
                      >
                        {getPlanText(subscription.plan_name)}
                      </span>
                    </td>
                    <td className="px-6 py-4 font-medium">
                      {formatCurrency(
                        subscription.monthly_fee,
                        subscription.currency
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                          subscription.status
                        )}`}
                      >
                        {getStatusText(subscription.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {new Date(subscription.start_date).toLocaleDateString(
                        "ar-SA"
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleDelete(subscription.id)}
                          className="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
                          title="حذف الاشتراك"
                        >
                          <FiTrash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {filteredSubscriptions.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">
              لا توجد اشتراكات مطابقة للبحث
            </p>
          </div>
        )}
      </div>

      <ToastContainer position="top-right" />
    </div>
  );
}
