-- تشخيص مشكلة foreign key constraint
-- تشغيل هذا السكريبت سيساعد في فهم المشكلة

-- 1. التحقق من وجود الجداول
SELECT 
    'Table exists check:' as check_type,
    table_name,
    table_schema,
    CASE WHEN table_name IS NOT NULL THEN 'EXISTS' ELSE 'NOT EXISTS' END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_name IN ('users', 'invitations', 'notifications', 'guests')
ORDER BY table_name;

-- 2. التحقق من foreign key constraints الحالية
SELECT 
    'Current foreign keys:' as check_type,
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    tc.constraint_name
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema='public'
    AND tc.table_name IN ('invitations', 'notifications', 'guests');

-- 3. التحقق من هيكل جدول users
SELECT 
    'Users table structure:' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'users'
ORDER BY ordinal_position;

-- 4. التحقق من هيكل جدول invitations
SELECT 
    'Invitations table structure:' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'invitations'
ORDER BY ordinal_position;

-- 5. التحقق من وجود بيانات في جدول users
SELECT 
    'Users data check:' as check_type,
    COUNT(*) as total_users,
    COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
    COUNT(CASE WHEN role = 'subscriber' THEN 1 END) as subscriber_users,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_users
FROM public.users;

-- 6. التحقق من وجود بيانات في جدول invitations
SELECT 
    'Invitations data check:' as check_type,
    COUNT(*) as total_invitations,
    COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_invitations,
    COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as invitations_with_user_id
FROM public.invitations;

-- 7. التحقق من user_ids في invitations التي لا توجد في users
SELECT 
    'Orphaned invitations check:' as check_type,
    i.id as invitation_id,
    i.user_id,
    i.title,
    'User not found in users table' as issue
FROM public.invitations i
LEFT JOIN public.users u ON i.user_id = u.id
WHERE u.id IS NULL;

-- 8. عرض أمثلة من user_ids الموجودة
SELECT 
    'Sample user IDs:' as check_type,
    id,
    name,
    phone,
    role
FROM public.users 
LIMIT 5;

-- 9. التحقق من extensions المطلوبة
SELECT 
    'Extensions check:' as check_type,
    extname as extension_name,
    CASE WHEN extname IS NOT NULL THEN 'INSTALLED' ELSE 'NOT INSTALLED' END as status
FROM pg_extension 
WHERE extname IN ('uuid-ossp', 'pgcrypto');

-- 10. اقتراحات الحل
SELECT 
    'Solution suggestions:' as check_type,
    '1. Run final-fix-foreign-key.sql to fix the issue' as suggestion
UNION ALL
SELECT 
    'Solution suggestions:' as check_type,
    '2. Make sure all user_ids in invitations exist in users table' as suggestion
UNION ALL
SELECT 
    'Solution suggestions:' as check_type,
    '3. Check that foreign key references public.users not auth.users' as suggestion;
