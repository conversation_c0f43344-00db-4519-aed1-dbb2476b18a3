"use client";

import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { getValidatedUser } from "@/lib/userValidation";
import { fetchUserStats, handleDashboardError } from "@/lib/dashboardHelpers";
import Link from "next/link";
import { useRouter } from "next/navigation";
import Header from "@/components/Header";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  FiMail,
  FiUsers,
  FiCheckCircle,
  FiBarChart2,
  FiCamera,
  FiPlus,
  FiEye,
  FiCalendar,
  FiMapPin,
  FiClock,
  FiAlertCircle,
  FiTrash2,
  FiCopy,
  FiInfo,
  FiSearch,
} from "react-icons/fi";

// مكون النافذة المنبثقة للحذف
const DeleteModal = ({ isOpen, onClose, onConfirm, title, message }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" onClick={onClose}>
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>
        &#8203;
        <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <FiAlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <div className="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  {title}
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-gray-500">{message}</p>
                </div>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={onConfirm}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              تأكيد الحذف
            </button>
            <button
              type="button"
              onClick={onClose}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              إلغاء
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// مكون بطاقة الإحصائيات المحسن
const StatCard = ({
  title,
  value,
  icon,
  iconBgColor,
  subtitle,
  percentage,
}) => {
  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border-t-4 border-t-blue-500">
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <div className="flex items-baseline">
              <p className="text-3xl font-bold text-gray-900">{value}</p>
              {percentage !== undefined && (
                <span
                  className={`mr-2 text-sm font-medium ${
                    percentage >= 0 ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {percentage >= 0 ? "+" : ""}
                  {percentage}%
                </span>
              )}
            </div>
            {subtitle && (
              <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
          <div
            className={`flex-shrink-0 ${iconBgColor} rounded-full p-3 shadow-md`}
          >
            <div className="text-white">{icon}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

// مكون بطاقة الدعوة المحسن
const InvitationCard = ({ invitation, onDelete, onCopyLink }) => {
  const formattedDate = new Date(
    invitation.date || invitation.event_date
  ).toLocaleDateString("ar-SA", {
    year: "numeric",
    month: "long",
    day: "numeric",
    weekday: "long",
  });

  const formattedTime = invitation.time
    ? new Date(`2000-01-01T${invitation.time}`).toLocaleTimeString("ar-SA", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    : null;

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-blue-200">
      {/* Header with gradient */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white">
        <h3 className="font-bold text-xl mb-2 line-clamp-2">
          {invitation.title || "دعوة بدون عنوان"}
        </h3>
        <div className="flex items-center text-blue-100 text-sm">
          <FiCalendar className="ml-1" />
          {formattedDate}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <div className="space-y-3 mb-4">
          {invitation.location && (
            <div className="flex items-center text-sm text-gray-600">
              <FiMapPin className="ml-2 text-gray-400 flex-shrink-0" />
              <span className="line-clamp-1">{invitation.location}</span>
            </div>
          )}

          {formattedTime && (
            <div className="flex items-center text-sm text-gray-600">
              <FiClock className="ml-2 text-gray-400 flex-shrink-0" />
              <span>{formattedTime}</span>
            </div>
          )}

          {invitation.description && (
            <div className="flex items-start text-sm text-gray-600">
              <FiInfo className="ml-2 mt-0.5 text-gray-400 flex-shrink-0" />
              <span className="line-clamp-2">{invitation.description}</span>
            </div>
          )}
        </div>

        {/* Quick Stats */}
        <div className="bg-gray-50 rounded-lg p-3 mb-4">
          <div className="grid grid-cols-3 gap-2 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">
                {invitation.guest_count || 0}
              </div>
              <div className="text-xs text-gray-500">مدعوين</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">
                {invitation.accepted_count || 0}
              </div>
              <div className="text-xs text-gray-500">قبلوا</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-600">
                {invitation.attended_count || 0}
              </div>
              <div className="text-xs text-gray-500">حضروا</div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Link
            href={`/subscriber/invitations/${invitation.id}`}
            className="flex items-center justify-center px-3 py-2 bg-blue-50 text-blue-600 rounded-lg text-sm font-medium hover:bg-blue-100 transition-colors"
          >
            <FiEye className="ml-1" />
            عرض التفاصيل
          </Link>
          <Link
            href={`/subscriber/invitations/${invitation.id}/guests`}
            className="flex items-center justify-center px-3 py-2 bg-green-50 text-green-600 rounded-lg text-sm font-medium hover:bg-green-100 transition-colors"
          >
            <FiUsers className="ml-1" />
            إدارة المدعوين
          </Link>
        </div>

        {/* Secondary Actions */}
        <div className="grid grid-cols-3 gap-1 mt-2">
          <Link
            href={`/subscriber/invitations/${invitation.id}/scan`}
            className="flex items-center justify-center px-2 py-1.5 bg-orange-50 text-orange-600 rounded-md text-xs hover:bg-orange-100 transition-colors"
          >
            <FiCamera className="ml-1" />
            مسح
          </Link>
          <button
            onClick={() => onCopyLink(invitation.id)}
            className="flex items-center justify-center px-2 py-1.5 bg-purple-50 text-purple-600 rounded-md text-xs hover:bg-purple-100 transition-colors"
          >
            <FiCopy className="ml-1" />
            نسخ
          </button>
          <button
            onClick={() => onDelete(invitation.id)}
            className="flex items-center justify-center px-2 py-1.5 bg-red-50 text-red-600 rounded-md text-xs hover:bg-red-100 transition-colors"
          >
            <FiTrash2 className="ml-1" />
            حذف
          </button>
        </div>
      </div>
    </div>
  );
};

export default function SubscriberDashboard() {
  const [user, setUser] = useState(null);
  const [stats, setStats] = useState({
    invitations: 0,
    guests: 0,
    attendance: 0,
    accepted: 0,
  });
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [invitationToDelete, setInvitationToDelete] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    const checkUser = async () => {
      try {
        setLoading(true);

        // استخدام دالة التحقق الجديدة
        const validatedUser = await getValidatedUser();
        if (!validatedUser) {
          console.log("No validated user found, redirecting to login");
          router.push("/login");
          return;
        }

        console.log("Validated user:", validatedUser);
        setUser(validatedUser);

        // جلب الدعوات والإحصائيات باستخدام الدالة الجديدة الآمنة
        console.log("Fetching user stats for user:", validatedUser.id);
        const userStatsResult = await fetchUserStats(validatedUser.id);

        console.log("User stats result:", userStatsResult);

        if (
          userStatsResult &&
          userStatsResult.invitations &&
          userStatsResult.stats
        ) {
          setInvitations(userStatsResult.invitations);
          setStats(userStatsResult.stats);
          console.log("Successfully set invitations and stats");
        } else {
          console.warn("Invalid user stats result:", userStatsResult);
          // تعيين قيم افتراضية
          setInvitations([]);
          setStats({
            invitations: 0,
            guests: 0,
            attendance: 0,
            accepted: 0,
            declined: 0,
            pending: 0,
          });
        }
      } catch (error) {
        console.error("Error in dashboard useEffect:", error);
        const errorMessage = handleDashboardError(error, "جلب بيانات المستخدم");
        toast.error(errorMessage);

        // تعيين قيم افتراضية في حالة الخطأ
        setInvitations([]);
        setStats({
          invitations: 0,
          guests: 0,
          attendance: 0,
          accepted: 0,
          declined: 0,
          pending: 0,
        });
      } finally {
        setLoading(false);
      }
    };

    checkUser();
  }, [router]);

  const copyInvitationLink = (invitationId) => {
    const link = `${window.location.origin}/invitation/${invitationId}`;
    navigator.clipboard
      .writeText(link)
      .then(() => {
        toast.success("تم نسخ رابط الدعوة بنجاح!", {
          position: "top-center",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      })
      .catch((err) => {
        console.error("فشل نسخ الرابط:", err);
        toast.error("فشل نسخ الرابط");
      });
  };

  const handleDeleteClick = (invitationId) => {
    setInvitationToDelete(invitationId);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (!invitationToDelete) return;

    try {
      setLoading(true);

      // حذف المدعوين أولاً
      const { error: guestsError } = await supabase
        .from("guests")
        .delete()
        .eq("invitation_id", invitationToDelete);

      if (guestsError) throw guestsError;

      // ثم حذف الدعوة
      const { error: invitationError } = await supabase
        .from("invitations")
        .delete()
        .eq("id", invitationToDelete);

      if (invitationError) throw invitationError;

      // تحديث القائمة
      setInvitations(
        invitations.filter((inv) => inv.id !== invitationToDelete)
      );

      // تحديث الإحصائيات باستخدام الدالة الجديدة
      if (user) {
        const { invitations: updatedInvitations, stats: updatedStats } =
          await fetchUserStats(user.id);
        setInvitations(updatedInvitations);
        setStats(updatedStats);
      }

      toast.success("تم حذف الدعوة بنجاح");
    } catch (error) {
      console.error("Error deleting invitation:", error);
      const errorMessage = handleDashboardError(error, "حذف الدعوة");
      toast.error(errorMessage);
    } finally {
      setIsDeleteModalOpen(false);
      setInvitationToDelete(null);
      setLoading(false);
    }
  };

  const filteredInvitations = invitations.filter(
    (invitation) =>
      invitation.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.location?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-700 mx-auto mb-4"></div>
          <p className="text-lg font-medium text-gray-700">
            جاري تحميل البيانات...
          </p>
          <p className="text-sm text-gray-500 mt-2">يرجى الانتظار...</p>
        </div>
      </div>
    );
  }

  // إذا لم يكن هناك مستخدم بعد انتهاء التحميل
  if (!loading && !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md">
          <div className="bg-red-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <FiAlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">
            خطأ في تسجيل الدخول
          </h3>
          <p className="text-gray-600 mb-6">
            حدث خطأ أثناء جلب بيانات المستخدم. يرجى تسجيل الدخول مرة أخرى.
          </p>
          <Link
            href="/login"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            تسجيل الدخول
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <ToastContainer rtl position="top-center" />

      {/* الهيدر */}
      <Header userRole="subscriber" />

      {/* الرأس */}
      <div className="bg-gradient-to-r from-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center">
            <div className="bg-white p-3 rounded-full shadow-md">
              <FiBarChart2 className="h-6 w-6 text-purple-600" />
            </div>
            <div className="mr-4">
              <h1 className="text-2xl font-bold">لوحة التحكم</h1>
              <p className="text-purple-200">مرحباً {user?.name}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* بطاقات الإحصائيات المحسنة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="إجمالي الدعوات"
            value={stats.invitations}
            icon={<FiMail className="h-7 w-7" />}
            iconBgColor="bg-gradient-to-r from-blue-500 to-blue-600"
            subtitle="دعوة نشطة"
          />
          <StatCard
            title="إجمالي المدعوين"
            value={stats.guests}
            icon={<FiUsers className="h-7 w-7" />}
            iconBgColor="bg-gradient-to-r from-green-500 to-green-600"
            subtitle={`${stats.pending || 0} في الانتظار`}
          />
          <StatCard
            title="تأكيد الحضور"
            value={stats.accepted}
            icon={<FiCheckCircle className="h-7 w-7" />}
            iconBgColor="bg-gradient-to-r from-yellow-500 to-orange-500"
            subtitle={`${
              stats.guests > 0
                ? Math.round((stats.accepted / stats.guests) * 100)
                : 0
            }% من المدعوين`}
            percentage={
              stats.guests > 0
                ? Math.round((stats.accepted / stats.guests) * 100) - 50
                : 0
            }
          />
          <StatCard
            title="الحضور الفعلي"
            value={stats.attendance}
            icon={<FiBarChart2 className="h-7 w-7" />}
            iconBgColor="bg-gradient-to-r from-purple-500 to-purple-600"
            subtitle={`${
              stats.accepted > 0
                ? Math.round((stats.attendance / stats.accepted) * 100)
                : 0
            }% من المقبولين`}
            percentage={
              stats.accepted > 0
                ? Math.round((stats.attendance / stats.accepted) * 100) - 70
                : 0
            }
          />
        </div>

        {/* مخطط الإحصائيات */}
        <div className="bg-white rounded-xl shadow-md p-6 mb-8 hover:shadow-lg transition-shadow duration-300">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-800 flex items-center">
              <FiBarChart2 className="ml-2 text-purple-600" />
              إحصائيات الدعوات
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow duration-300">
              <p className="text-sm text-gray-500 mb-1 flex items-center justify-center">
                <FiCheckCircle className="ml-1 text-blue-500" />
                نسبة تأكيد الحضور
              </p>
              <div className="relative h-32 w-32 mx-auto">
                <svg viewBox="0 0 36 36" className="circular-chart">
                  <path
                    className="circle-bg"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#eee"
                    strokeWidth="3"
                  />
                  <path
                    className="circle"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#4f46e5"
                    strokeWidth="3"
                    strokeDasharray={`${
                      stats.guests > 0
                        ? (stats.accepted / stats.guests) * 100
                        : 0
                    }, 100`}
                  />
                  <text
                    x="18"
                    y="20.35"
                    textAnchor="middle"
                    className="percentage"
                    fill="#4f46e5"
                    fontSize="8"
                    fontWeight="bold"
                  >
                    {stats.guests > 0
                      ? Math.round((stats.accepted / stats.guests) * 100)
                      : 0}
                    %
                  </text>
                </svg>
              </div>
              <p className="font-bold text-lg">
                {stats.accepted} / {stats.guests}
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow duration-300">
              <p className="text-sm text-gray-500 mb-1 flex items-center justify-center">
                <FiCheckCircle className="ml-1 text-purple-500" />
                نسبة الحضور الفعلي
              </p>
              <div className="relative h-32 w-32 mx-auto">
                <svg viewBox="0 0 36 36" className="circular-chart">
                  <path
                    className="circle-bg"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#eee"
                    strokeWidth="3"
                  />
                  <path
                    className="circle"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#8b5cf6"
                    strokeWidth="3"
                    strokeDasharray={`${
                      stats.accepted > 0
                        ? (stats.attendance / stats.accepted) * 100
                        : 0
                    }, 100`}
                  />
                  <text
                    x="18"
                    y="20.35"
                    textAnchor="middle"
                    className="percentage"
                    fill="#8b5cf6"
                    fontSize="8"
                    fontWeight="bold"
                  >
                    {stats.accepted > 0
                      ? Math.round((stats.attendance / stats.accepted) * 100)
                      : 0}
                    %
                  </text>
                </svg>
              </div>
              <p className="font-bold text-lg">
                {stats.attendance} / {stats.accepted}
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow duration-300">
              <p className="text-sm text-gray-500 mb-1 flex items-center justify-center">
                <FiUsers className="ml-1 text-indigo-500" />
                متوسط المدعوين لكل دعوة
              </p>
              <div className="flex items-center justify-center h-32">
                <div className="text-4xl font-bold text-indigo-600">
                  {stats.invitations > 0
                    ? Math.round(stats.guests / stats.invitations)
                    : 0}
                </div>
              </div>
              <p className="font-bold text-lg">مدعو / دعوة</p>
            </div>
          </div>
        </div>

        {/* قسم الدعوات المحسن */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <h2 className="text-2xl font-bold flex items-center mb-4 md:mb-0">
                <FiMail className="ml-3" />
                إدارة الدعوات
              </h2>
              <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2 md:space-x-reverse w-full md:w-auto">
                <div className="relative w-full md:w-64">
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <FiSearch className="h-5 w-5 text-blue-200" />
                  </div>
                  <input
                    type="text"
                    className="block w-full pr-10 py-2 border border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent bg-white/10 backdrop-blur-sm text-white placeholder-blue-200"
                    placeholder="بحث في الدعوات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Link
                  href="/subscriber/invitations/create"
                  className="inline-flex items-center justify-center px-6 py-2 border border-white/20 text-sm font-medium rounded-lg shadow-sm text-white bg-white/10 backdrop-blur-sm hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white transition-all duration-200"
                >
                  <FiPlus className="ml-2" />
                  إنشاء دعوة جديدة
                </Link>
              </div>
            </div>
          </div>

          {filteredInvitations.length === 0 ? (
            <div className="p-12 text-center">
              {searchTerm ? (
                <div className="max-w-md mx-auto">
                  <div className="bg-gray-100 rounded-full p-6 w-24 h-24 mx-auto mb-6 flex items-center justify-center">
                    <FiInfo className="h-12 w-12 text-gray-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    لا توجد نتائج
                  </h3>
                  <p className="text-gray-500 mb-6">
                    لم نجد أي دعوات تطابق بحثك "{searchTerm}"
                  </p>
                  <button
                    onClick={() => setSearchTerm("")}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    عرض جميع الدعوات
                  </button>
                </div>
              ) : (
                <div className="max-w-md mx-auto">
                  <div className="bg-blue-100 rounded-full p-6 w-24 h-24 mx-auto mb-6 flex items-center justify-center">
                    <FiMail className="h-12 w-12 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    ابدأ بإنشاء دعوتك الأولى
                  </h3>
                  <p className="text-gray-500 mb-6">
                    لا توجد دعوات حتى الآن. أنشئ دعوتك الأولى وابدأ في دعوة
                    الأصدقاء والعائلة.
                  </p>
                  <Link
                    href="/subscriber/invitations/create"
                    className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                  >
                    <FiPlus className="ml-2" />
                    إنشاء دعوة جديدة
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {filteredInvitations.map((invitation) => (
                  <InvitationCard
                    key={invitation.id}
                    invitation={invitation}
                    onDelete={handleDeleteClick}
                    onCopyLink={copyInvitationLink}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* نافذة تأكيد الحذف */}
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="تأكيد حذف الدعوة"
        message="هل أنت متأكد من رغبتك في حذف هذه الدعوة؟ سيتم حذف جميع بيانات المدعوين المرتبطة بها. هذا الإجراء لا يمكن التراجع عنه."
      />

      {/* أنماط CSS محسنة */}
      <style jsx>{`
        .circular-chart {
          display: block;
          margin: 10px auto;
          max-width: 100%;
          max-height: 100%;
        }
        .circle {
          stroke-linecap: round;
          transition: stroke-dasharray 1s ease-in-out;
        }
        .line-clamp-1 {
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
        .line-clamp-2 {
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        .backdrop-blur-sm {
          backdrop-filter: blur(4px);
        }
      `}</style>
    </div>
  );
}
