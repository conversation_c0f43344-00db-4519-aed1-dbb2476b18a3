// اختبار سريع لنظام الاشتراكات
// Quick test for subscription system

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testSubscriptionSystem() {
  console.log('🧪 اختبار نظام الاشتراكات...');
  
  try {
    // 1. اختبار تحميل الاشتراكات
    console.log('\n1️⃣ اختبار تحميل الاشتراكات...');
    const { data: subscriptions, error: subError } = await supabase
      .from("subscriptions")
      .select(`
        *,
        users (
          id,
          name,
          phone
        )
      `)
      .order("created_at", { ascending: false });

    if (subError) {
      console.error('❌ خطأ في تحميل الاشتراكات:', subError);
      return;
    }

    console.log(`✅ تم تحميل ${subscriptions.length} اشتراك بنجاح`);
    console.log('📋 عينة من البيانات:', subscriptions[0]);

    // 2. اختبار تحميل المستخدمين
    console.log('\n2️⃣ اختبار تحميل المستخدمين...');
    const { data: users, error: usersError } = await supabase
      .from("users")
      .select("id, name, phone")
      .eq("role", "subscriber")
      .order("name");

    if (usersError) {
      console.error('❌ خطأ في تحميل المستخدمين:', usersError);
      return;
    }

    console.log(`✅ تم تحميل ${users.length} مستخدم بنجاح`);

    // 3. اختبار دالة الإحصائيات
    console.log('\n3️⃣ اختبار دالة الإحصائيات...');
    const { data: stats, error: statsError } = await supabase.rpc("get_subscription_stats");

    if (statsError) {
      console.error('❌ خطأ في تحميل الإحصائيات:', statsError);
      return;
    }

    console.log('✅ إحصائيات النظام:');
    console.log(`   📊 إجمالي الاشتراكات: ${stats.total_subscriptions}`);
    console.log(`   🟢 الاشتراكات النشطة: ${stats.active_subscriptions}`);
    console.log(`   💰 الدخل الشهري: $${stats.monthly_revenue}`);
    console.log(`   📈 متوسط الرسوم: $${stats.average_monthly_fee}`);
    console.log(`   🏆 الخطة الأكثر شعبية: ${stats.most_popular_plan}`);

    // 4. اختبار البحث والتصفية
    console.log('\n4️⃣ اختبار البحث والتصفية...');
    const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
    console.log(`✅ تم العثور على ${activeSubscriptions.length} اشتراك نشط`);

    const premiumSubscriptions = subscriptions.filter(sub => sub.plan_name === 'premium');
    console.log(`✅ تم العثور على ${premiumSubscriptions.length} اشتراك مميز`);

    console.log('\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام');

  } catch (error) {
    console.error('💥 خطأ عام في النظام:', error);
  }
}

// تشغيل الاختبار
if (typeof window === 'undefined') {
  // Node.js environment
  testSubscriptionSystem();
} else {
  // Browser environment
  window.testSubscriptionSystem = testSubscriptionSystem;
  console.log('🌐 استخدم testSubscriptionSystem() في وحدة التحكم لتشغيل الاختبار');
}

export default testSubscriptionSystem;
