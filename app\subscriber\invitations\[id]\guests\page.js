"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { supabase } from "@/lib/supabase";
import Header from "@/components/Header";
import {
  FiUser,
  FiPhone,
  FiCheck,
  FiX,
  FiSearch,
  FiDownload,
  FiRefreshCw,
  FiTrash2,
  FiEye,
  FiEdit,
  FiFilter,
  FiCamera,
} from "react-icons/fi";
import {
  StatusBadge,
  AttendanceBadge,
  calculateGuestStats,
  normalizeGuestStatuses,
} from "@/lib/statusHelpers";

// إضافة مكون نافذة تأكيد الحذف
const DeleteConfirmModal = ({ guest, onClose, onConfirm, loading }) => {
  if (!guest) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <h3 className="text-xl font-bold mb-4">تأكيد الحذف</h3>
        <p className="mb-6">
          هل أنت متأكد من رغبتك في حذف المدعو {guest.name || guest.phone}؟
        </p>
        <div className="flex justify-end space-x-2 space-x-reverse">
          <button
            onClick={onClose}
            className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded"
            disabled={loading}
          >
            إلغاء
          </button>
          <button
            onClick={() => onConfirm(guest.id)}
            className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded flex items-center"
            disabled={loading}
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white ml-2"></div>
            ) : (
              <FiTrash2 className="ml-2" />
            )}
            حذف
          </button>
        </div>
      </div>
    </div>
  );
};

// تعديل مكون نافذة عرض تفاصيل المدعو
const GuestDetailsModal = ({ guest, onClose }) => {
  if (!guest) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold">تفاصيل المدعو</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <FiX size={24} />
          </button>
        </div>

        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">الاسم</p>
            <p className="font-medium text-lg">{guest.name || "غير محدد"}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">رقم الهاتف</p>
            <p className="font-medium text-lg font-mono">{guest.phone}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">حالة الرد</p>
            <div>
              {guest.status === "pending" && (
                <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full">
                  في الانتظار
                </span>
              )}
              {guest.status === "sent" && (
                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full">
                  تم الإرسال
                </span>
              )}
              {guest.status === "accepted" && (
                <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full">
                  مؤكد
                </span>
              )}
              {guest.status === "declined" && (
                <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full">
                  اعتذار
                </span>
              )}
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">حالة الحضور</p>
            <div>
              {guest.attended ? (
                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full">
                  حضر
                </span>
              ) : (
                <span className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full">
                  لم يحضر
                </span>
              )}
            </div>
          </div>

          {guest.notes && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-500 mb-1">ملاحظات</p>
              <p>{guest.notes}</p>
            </div>
          )}
        </div>

        <div className="mt-6">
          <button
            onClick={onClose}
            className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded w-full"
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  );
};

export default function GuestsList({ params }) {
  const invitationId = params.id;
  const [invitation, setInvitation] = useState(null);
  const [guests, setGuests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [user, setUser] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [attendanceFilter, setAttendanceFilter] = useState("all");
  const [showGuestDetails, setShowGuestDetails] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const router = useRouter();

  useEffect(() => {
    const fetchUserAndData = async () => {
      try {
        // التحقق من وجود المستخدم في localStorage
        const userData = localStorage.getItem("user");
        if (!userData) {
          router.push("/login");
          return;
        }

        const parsedUser = JSON.parse(userData);

        if (!parsedUser || parsedUser.role !== "subscriber") {
          router.push("/login");
          return;
        }

        setUser(parsedUser);

        // الحصول على بيانات الدعوة
        const { data: invitationData, error: invitationError } = await supabase
          .from("invitations")
          .select("*")
          .eq("id", invitationId)
          .single();

        if (invitationError) throw invitationError;
        setInvitation(invitationData);

        // الحصول على بيانات المدعوين
        await refreshGuests();
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("حدث خطأ أثناء جلب البيانات");
      } finally {
        setLoading(false);
      }
    };

    fetchUserAndData();
  }, [invitationId, router]);

  const refreshGuests = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from("guests")
        .select("*")
        .eq("invitation_id", invitationId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      setGuests(data);
      setSuccess("تم تحديث البيانات بنجاح");

      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error("Error refreshing guests:", error);
      setError("حدث خطأ أثناء تحديث البيانات");
    } finally {
      setLoading(false);
    }
  };

  const exportToCSV = () => {
    try {
      // تحضير البيانات للتصدير
      const csvData = guests.map((guest) => ({
        الاسم: guest.name || "",
        الهاتف: guest.phone || "",
        الحالة:
          guest.status === "accepted"
            ? "مؤكد"
            : guest.status === "declined"
            ? "معتذر"
            : "في الانتظار",
        الحضور: guest.attended ? "حضر" : "لم يحضر",
        ملاحظات: guest.notes || "",
      }));

      // تحويل البيانات إلى CSV
      const headers = Object.keys(csvData[0]);
      const csvContent = [
        headers.join(","),
        ...csvData.map((row) =>
          headers.map((header) => `"${row[header]}"`).join(",")
        ),
      ].join("\n");

      // إنشاء ملف للتنزيل
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", `guests-${invitationId}.csv`);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setSuccess("تم تصدير البيانات بنجاح");

      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error("Error exporting to CSV:", error);
      setError("حدث خطأ أثناء تصدير البيانات");
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    setSuccess("تم نسخ الرابط بنجاح");

    // إخفاء رسالة النجاح بعد 3 ثوان
    setTimeout(() => {
      setSuccess(null);
    }, 3000);
  };

  const getInvitationLink = (guestId) => {
    return `${window.location.origin}/invitation/${invitationId}?guest=${guestId}`;
  };

  const handleDeleteGuest = async (guestId) => {
    try {
      setLoading(true);

      const { error } = await supabase
        .from("guests")
        .delete()
        .eq("id", guestId);

      if (error) throw error;

      // تحديث قائمة المدعوين
      await refreshGuests();

      setSuccess("تم حذف المدعو بنجاح");
      setShowDeleteConfirm(null);

      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error("Error deleting guest:", error);
      setError("حدث خطأ أثناء حذف المدعو");
    } finally {
      setLoading(false);
    }
  };

  const toggleAttendance = async (guest) => {
    try {
      setLoading(true);

      const { error } = await supabase
        .from("guests")
        .update({ attended: !guest.attended })
        .eq("id", guest.id);

      if (error) throw error;

      // تحديث قائمة المدعوين
      await refreshGuests();

      setSuccess(`تم تغيير حالة الحضور بنجاح`);

      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error("Error updating attendance:", error);
      setError("حدث خطأ أثناء تحديث حالة الحضور");
    } finally {
      setLoading(false);
    }
  };

  const filteredGuests = guests.filter((guest) => {
    // تطبيق البحث
    const searchMatch =
      guest.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.phone?.toLowerCase().includes(searchTerm.toLowerCase());

    // تطبيق فلتر الحالة
    const statusMatch = statusFilter === "all" || guest.status === statusFilter;

    // تطبيق فلتر الحضور
    const attendanceMatch =
      attendanceFilter === "all" ||
      (attendanceFilter === "attended" && guest.attended) ||
      (attendanceFilter === "not_attended" && !guest.attended);

    return searchMatch && statusMatch && attendanceMatch;
  });

  if (loading && guests.length === 0) {
    return (
      <>
        <Header userName={user?.name} userRole="subscriber" />
        <div className="p-8 text-center">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            <span className="mr-4 text-lg font-medium">
              جاري تحميل البيانات...
            </span>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header userName={user?.name} userRole="subscriber" />
      <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <div className="p-4 sm:p-6 bg-gradient-to-r from-purple-600 to-indigo-600 text-white">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold mb-2">قائمة المدعوين</h1>
                <p className="text-purple-100">للدعوة: {invitation?.title}</p>
              </div>
              <div className="flex space-x-2 space-x-reverse">
                <Link
                  href={`/subscriber/invitations/${params.id}`}
                  className="bg-white text-purple-700 hover:bg-purple-100 font-bold py-2 px-4 rounded transition-colors duration-200"
                >
                  العودة للدعوة
                </Link>
                <Link
                  href={`/subscriber/invitations/${params.id}/scan`}
                  className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"
                >
                  <FiCamera className="inline ml-1" />
                  مسح الحضور
                </Link>
              </div>
            </div>
          </div>

          {error && (
            <div className="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 m-4 rounded">
              <div className="flex items-center">
                <FiX className="ml-2 text-red-500" />
                <p>{error}</p>
              </div>
            </div>
          )}

          {success && (
            <div className="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 m-4 rounded">
              <div className="flex items-center">
                <FiCheck className="ml-2 text-green-500" />
                <p>{success}</p>
              </div>
            </div>
          )}

          <div className="p-4 sm:p-6">
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <div className="flex flex-col md:flex-row md:items-center mb-4 gap-4">
                <div className="flex items-center flex-1">
                  <div className="relative w-full">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <FiSearch className="text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="بحث بالاسم أو رقم الهاتف..."
                      className="border border-gray-300 rounded-lg pr-10 py-3 w-full focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex items-center">
                  <FiFilter className="ml-2 text-gray-500" />
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="pending">في الانتظار</option>
                    <option value="accepted">مؤكد</option>
                    <option value="declined">معتذر</option>
                  </select>
                </div>

                <div className="flex items-center">
                  <FiCheck className="ml-2 text-gray-500" />
                  <select
                    value={attendanceFilter}
                    onChange={(e) => setAttendanceFilter(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="all">جميع المدعوين</option>
                    <option value="attended">حضر</option>
                    <option value="not_attended">لم يحضر</option>
                  </select>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                <button
                  onClick={refreshGuests}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200 flex items-center"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white ml-2"></div>
                  ) : (
                    <FiRefreshCw className="ml-2" />
                  )}
                  تحديث
                </button>

                <button
                  onClick={exportToCSV}
                  className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200 flex items-center"
                >
                  <FiDownload className="ml-2" />
                  تصدير CSV
                </button>

                <Link
                  href={`/subscriber/invitations/${params.id}/guests/add`}
                  className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200 flex items-center"
                >
                  <FiUser className="ml-2" />
                  إضافة مدعوين
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="p-4 border-b">
                <h2 className="text-lg font-bold">
                  قائمة المدعوين ({filteredGuests.length})
                </h2>
                <div className="mt-2 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
                  <p className="font-medium text-blue-800 mb-1">
                    توضيح الحالات:
                  </p>
                  <div className="space-y-1">
                    <p>
                      <span className="font-medium">رد الضيف:</span> يحدده الضيف
                      نفسه (قبول/رفض/لم يرد)
                    </p>
                    <p>
                      <span className="font-medium">الحضور الفعلي:</span> تحدده
                      أنت كصاحب الدعوة (حضر فعلياً أم لا)
                    </p>
                  </div>
                </div>
              </div>

              {filteredGuests.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الاسم
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          رقم الهاتف
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          رد الضيف
                          <div className="text-xs normal-case text-gray-400 mt-1">
                            قبول/رفض الدعوة
                          </div>
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الحضور الفعلي
                          <div className="text-xs normal-case text-gray-400 mt-1">
                            يحدده المستخدم
                          </div>
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الرابط
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredGuests.map((guest) => (
                        <tr key={guest.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4">{guest.name || "-"}</td>
                          <td className="px-6 py-4 font-mono">{guest.phone}</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <StatusBadge status={guest.status} size="xs" />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <button
                              onClick={() => toggleAttendance(guest)}
                              className="hover:scale-105 transition-transform"
                              title="انقر لتغيير حالة الحضور"
                            >
                              <AttendanceBadge
                                attended={guest.attended}
                                size="xs"
                              />
                            </button>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <button
                              onClick={() =>
                                copyToClipboard(getInvitationLink(guest.id))
                              }
                              className="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-xs transition-colors duration-200"
                            >
                              نسخ الرابط
                            </button>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex space-x-2 space-x-reverse">
                              <button
                                onClick={() => setShowGuestDetails(guest)}
                                className="bg-purple-100 hover:bg-purple-200 text-purple-700 p-1.5 rounded-full transition-colors duration-200"
                                title="عرض التفاصيل"
                              >
                                <FiEye size={16} />
                              </button>
                              <button
                                onClick={() => setShowDeleteConfirm(guest)}
                                className="bg-red-100 hover:bg-red-200 text-red-700 p-1.5 rounded-full transition-colors duration-200"
                                title="حذف"
                              >
                                <FiTrash2 size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-gray-500 text-center py-8">
                  لا يوجد مدعوين حتى الآن. قم بإضافة مدعوين للدعوة.
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {showGuestDetails && (
        <GuestDetailsModal
          guest={showGuestDetails}
          onClose={() => setShowGuestDetails(null)}
        />
      )}

      {showDeleteConfirm && (
        <DeleteConfirmModal
          guest={showDeleteConfirm}
          onClose={() => setShowDeleteConfirm(null)}
          onConfirm={handleDeleteGuest}
          loading={loading}
        />
      )}
    </>
  );
}
