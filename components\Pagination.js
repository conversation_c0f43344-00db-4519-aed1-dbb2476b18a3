export default function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  className = '',
}) {
  // إنشاء مصفوفة بأرقام الصفحات
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;
    
    if (totalPages <= maxPagesToShow) {
      // إذا كان عدد الصفحات أقل من أو يساوي الحد الأقصى، عرض جميع الصفحات
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // دائمًا عرض الصفحة الأولى
      pages.push(1);
      
      // حساب نطاق الصفحات المتوسطة
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);
      
      // ضبط النطاق إذا كان قريبًا من البداية أو النهاية
      if (currentPage <= 2) {
        endPage = 4;
      } else if (currentPage >= totalPages - 1) {
        startPage = totalPages - 3;
      }
      
      // إضافة "..." إذا كانت هناك فجوة بين الصفحة الأولى والصفحات المتوسطة
      if (startPage > 2) {
        pages.push('...');
      }
      
      // إضافة الصفحات المتوسطة
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      // إضافة "..." إذا كانت هناك فجوة بين الصفحات المتوسطة والصفحة الأخيرة
      if (endPage < totalPages - 1) {
        pages.push('...');
      }
      
      // دائمًا عرض الصفحة الأخيرة
      pages.push(totalPages);
    }
    
    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <nav className={`flex justify-center mt-4 ${className}`}>
      <ul className="flex space-x-2 space-x-reverse">
        {/* زر الصفحة السابقة */}
        <li>
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={`
              px-3 py-1 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500
              ${currentPage === 1 
                ? 'bg-neutral-100 text-neutral-400 cursor-not-allowed dark:bg-neutral-800 dark:text-neutral-600' 
                : 'bg-white text-neutral-700 hover:bg-neutral-50 dark:bg-neutral-800 dark:text-neutral-300 dark:hover:bg-neutral-700'}
            `}
          >
            السابق
          </button>
        </li>
        
        {/* أرقام الصفحات */}
        {pageNumbers.map((page, index) => (
          <li key={index}>
            {page === '...' ? (
              <span className="px-3 py-1 text-neutral-500">...</span>
            ) : (
              <button
                onClick={() => onPageChange(page)}
                className={`
                  px-3 py-1 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500
                  ${currentPage === page
                    ? 'bg-primary-600 text-white'
                    : 'bg-white text