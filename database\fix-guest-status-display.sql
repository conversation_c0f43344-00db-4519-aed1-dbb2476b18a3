-- إصلاح شامل لمشاكل عرض حالات المدعوين

-- 1. تنظيف وتوحيد حالات المدعوين
UPDATE public.guests 
SET status = CASE 
    WHEN status = 'confirmed' THEN 'accepted'
    WHEN status IS NULL OR status = '' THEN 'pending'
    WHEN status NOT IN ('pending', 'accepted', 'declined') THEN 'pending'
    ELSE status
END;

-- 2. التأكد من وجود العمود attended
ALTER TABLE public.guests ADD COLUMN IF NOT EXISTS attended BOOLEAN DEFAULT false;
ALTER TABLE public.guests ADD COLUMN IF NOT EXISTS attended_at TIMESTAMP WITH TIME ZONE;

-- 3. تنظيف قيم الحضور
UPDATE public.guests 
SET attended = COALESCE(attended, false);

-- 4. إضافة تعليقات توضيحية للأعمدة
COMMENT ON COLUMN public.guests.status IS 'رد الضيف على الدعوة (يحدده الضيف): pending = لم يرد، accepted = قبل، declined = اعتذر';
COMMENT ON COLUMN public.guests.attended IS 'الحضور الفعلي (يحدده صاحب الدعوة): true = حضر، false = لم يحضر';
COMMENT ON COLUMN public.guests.attended_at IS 'وقت تسجيل الحضور الفعلي';

-- 5. إنشاء دالة لتحديث attended_at تلقائياً
CREATE OR REPLACE FUNCTION update_attended_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.attended = true AND (OLD.attended IS NULL OR OLD.attended = false) THEN
        NEW.attended_at = CURRENT_TIMESTAMP;
    ELSIF NEW.attended = false THEN
        NEW.attended_at = NULL;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. إنشاء trigger لتحديث attended_at
DROP TRIGGER IF EXISTS trigger_update_attended_timestamp ON public.guests;
CREATE TRIGGER trigger_update_attended_timestamp
    BEFORE UPDATE ON public.guests
    FOR EACH ROW
    EXECUTE FUNCTION update_attended_timestamp();

-- 7. إنشاء view للإحصائيات السريعة
CREATE OR REPLACE VIEW guest_stats_view AS
SELECT 
    invitation_id,
    COUNT(*) as total_guests,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
    COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_count,
    COUNT(CASE WHEN status = 'declined' THEN 1 END) as declined_count,
    COUNT(CASE WHEN attended = true THEN 1 END) as attended_count,
    ROUND(
        (COUNT(CASE WHEN status = 'accepted' THEN 1 END)::float / 
         NULLIF(COUNT(*), 0)) * 100, 1
    ) as acceptance_rate,
    ROUND(
        (COUNT(CASE WHEN attended = true THEN 1 END)::float / 
         NULLIF(COUNT(CASE WHEN status = 'accepted' THEN 1 END), 0)) * 100, 1
    ) as attendance_rate
FROM public.guests
GROUP BY invitation_id;

-- 8. إنشاء دالة للحصول على إحصائيات المستخدم
CREATE OR REPLACE FUNCTION get_user_stats(user_id_param UUID)
RETURNS TABLE(
    total_invitations BIGINT,
    total_guests BIGINT,
    total_accepted BIGINT,
    total_declined BIGINT,
    total_pending BIGINT,
    total_attended BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT i.id) as total_invitations,
        COUNT(g.id) as total_guests,
        COUNT(CASE WHEN g.status = 'accepted' THEN 1 END) as total_accepted,
        COUNT(CASE WHEN g.status = 'declined' THEN 1 END) as total_declined,
        COUNT(CASE WHEN g.status = 'pending' OR g.status IS NULL THEN 1 END) as total_pending,
        COUNT(CASE WHEN g.attended = true THEN 1 END) as total_attended
    FROM public.invitations i
    LEFT JOIN public.guests g ON i.id = g.invitation_id
    WHERE i.user_id = user_id_param;
END;
$$ LANGUAGE plpgsql;

-- 9. إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_guests_status_attended ON public.guests(status, attended);
CREATE INDEX IF NOT EXISTS idx_guests_invitation_status ON public.guests(invitation_id, status);
CREATE INDEX IF NOT EXISTS idx_guests_invitation_attended ON public.guests(invitation_id, attended);

-- 10. إصلاح جدول notifications
ALTER TABLE public.notifications ALTER COLUMN message DROP NOT NULL;
ALTER TABLE public.notifications ALTER COLUMN message SET DEFAULT 'إشعار جديد';
UPDATE public.notifications 
SET message = COALESCE(message, 'إشعار جديد')
WHERE message IS NULL;

-- 11. إعادة تحميل schema cache
NOTIFY pgrst, 'reload schema';

-- 12. عرض النتائج النهائية
SELECT 
    'تم إصلاح جميع مشاكل عرض الحالات بنجاح!' as status,
    COUNT(*) as total_guests,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_guests,
    COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_guests,
    COUNT(CASE WHEN status = 'declined' THEN 1 END) as declined_guests,
    COUNT(CASE WHEN attended = true THEN 1 END) as attended_guests
FROM public.guests;

-- 13. عرض إحصائيات مفصلة لكل دعوة
SELECT 
    i.title as invitation_title,
    gs.total_guests,
    gs.pending_count,
    gs.accepted_count,
    gs.declined_count,
    gs.attended_count,
    gs.acceptance_rate || '%' as acceptance_rate,
    gs.attendance_rate || '%' as attendance_rate
FROM public.invitations i
LEFT JOIN guest_stats_view gs ON i.id = gs.invitation_id
ORDER BY i.created_at DESC
LIMIT 10;
