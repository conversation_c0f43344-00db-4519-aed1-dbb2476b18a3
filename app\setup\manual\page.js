'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function ManualSetupPage() {
  const [copied, setCopied] = useState(false);
  
  const sqlScript = `
-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  role VARCHAR(50) NOT NULL DEFAULT 'subscriber',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إضافة بعض المستخدمين للاختبار
INSERT INTO public.users (name, phone, password, role) VALUES
('مدير النظام', '0500000000', 'admin123', 'admin'),
('مستخدم عادي', '0500000001', 'user123', 'subscriber');

-- إنشاء جدول الدعوات
CREATE TABLE IF NOT EXISTS public.invitations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.users(id),
  title VARCHAR(255) NOT NULL,
  event_date TIMESTAMP WITH TIME ZONE,
  location VARCHAR(255) NOT NULL,
  description TEXT,
  guest_name VARCHAR(255),
  guest_phone VARCHAR(20),
  status VARCHAR(50) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول الضيوف
CREATE TABLE IF NOT EXISTS public.guests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invitation_id UUID REFERENCES public.invitations(id),
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  attended BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.users(id),
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول القوالب
CREATE TABLE IF NOT EXISTS public.templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  image_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إضافة علاقة بين جدول invitations وجدول templates
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'invitations' AND column_name = 'template_id'
  ) THEN
    ALTER TABLE public.invitations 
    ADD COLUMN template_id INTEGER REFERENCES public.templates(id);
  END IF;
END $$;

-- إضافة عمود للبيانات الإضافية في جدول invitations
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'invitations' AND column_name = 'additional_data'
  ) THEN
    ALTER TABLE public.invitations 
    ADD COLUMN additional_data JSONB DEFAULT '{}'::jsonb;
  END IF;
END $$;

-- إضافة بعض القوالب الافتراضية للاختبار
INSERT INTO public.templates (name, image_url)
VALUES 
  ('قالب زفاف', 'https://example.com/wedding.jpg'),
  ('قالب تخرج', 'https://example.com/graduation.jpg'),
  ('قالب عيد ميلاد', 'https://example.com/birthday.jpg')
ON CONFLICT DO NOTHING;
  `;
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(sqlScript);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">إعداد قاعدة البيانات يدويًا</h1>
      
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-bold mb-4">تعليمات الإعداد</h2>
        <ol className="list-decimal pl-5 mb-4">
          <li className="mb-2">قم بتسجيل الدخول إلى لوحة تحكم Supabase</li>
          <li className="mb-2">انتقل إلى قسم "SQL Editor"</li>
          <li className="mb-2">انقر على "New Query"</li>
          <li className="mb-2">انسخ والصق الاستعلام أدناه</li>
          <li className="mb-2">انقر على "Run" لتنفيذ الاستعلام</li>
        </ol>
        
        <div className="relative">
          <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
            {sqlScript}
          </pre>
          <button
            onClick={copyToClipboard}
            className="absolute top-2 right-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
          >
            {copied ? 'تم النسخ!' : 'نسخ'}
          </button>
        </div>
      </div>
      
      <div className="flex justify-between">
        <Link href="/setup" className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
          رجوع
        </Link>
        <Link href="/login" className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          الانتقال إلى تسجيل الدخول
        </Link>
      </div>
    </div>
  );
}
