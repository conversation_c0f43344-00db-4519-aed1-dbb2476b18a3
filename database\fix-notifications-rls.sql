-- تفعيل RLS على جدول notifications
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- إضافة عمود type إذا لم يكن موجودًا
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS type VARCHAR(50);

-- حذف السياسات القديمة إذا لزم الأمر (اختياري)
DROP POLICY IF EXISTS "Admin can view all notifications" ON notifications;
DROP POLICY IF EXISTS "Admin can insert notifications" ON notifications;
DROP POLICY IF EXISTS "Admin can update notifications" ON notifications;
DROP POLICY IF EXISTS "Admin can delete notifications" ON notifications;
DROP POLICY IF EXISTS "User can view own notifications" ON notifications;

-- سياسة: يمكن للإدمن رؤية كل الإشعارات
CREATE POLICY "Admin can view all notifications"
  ON notifications
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
  ));

-- سياسة: يمكن للإدمن إضافة إشعارات
CREATE POLICY "Admin can insert notifications"
  ON notifications
  FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
  ));

-- سياسة: يمكن للإدمن تعديل الإشعارات
CREATE POLICY "Admin can update notifications"
  ON notifications
  FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
  ));

-- سياسة: يمكن للإدمن حذف الإشعارات
CREATE POLICY "Admin can delete notifications"
  ON notifications
  FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
  ));

-- سياسة: يمكن للمستخدم مشاهدة الإشعارات الخاصة به فقط (اختياري)
CREATE POLICY "User can view own notifications"
  ON notifications
  FOR SELECT
  USING (user_id = auth.uid());
