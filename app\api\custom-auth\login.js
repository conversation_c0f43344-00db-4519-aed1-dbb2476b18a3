import { NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import bcrypt from "bcryptjs";

export async function POST(request) {
  try {
    const { phone, password } = await request.json();
    if (!phone || !password) {
      return NextResponse.json(
        { success: false, error: "جميع الحقول مطلوبة" },
        { status: 400 }
      );
    }
    const supabase = createRouteHandlerClient({ cookies });
    // جلب المستخدم
    const { data: user, error } = await supabase
      .from("users")
      .select("*")
      .eq("phone", phone)
      .maybeSingle();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "رقم الهاتف أو كلمة المرور غير صحيحة" },
        { status: 401 }
      );
    }
    // تحقق من كلمة المرور
    const valid = await bcrypt.compare(password, user.password);
    if (!valid) {
      return NextResponse.json(
        { success: false, error: "رقم الهاتف أو كلمة المرور غير صحيحة" },
        { status: 401 }
      );
    }
    // تحقق من التفعيل
    if (!user.is_active) {
      return NextResponse.json(
        { success: false, error: "الحساب غير مفعل بعد" },
        { status: 403 }
      );
    }
    // إنشاء جلسة (يمكنك استخدام كوكيز أو localStorage في الواجهة)
    return NextResponse.json({
      success: true,
      user: { id: user.id, name: user.name, phone: user.phone },
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: error.message || "حدث خطأ أثناء تسجيل الدخول" },
      { status: 500 }
    );
  }
}
