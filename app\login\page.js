"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { supabase } from "@/lib/supabase";

export default function Login() {
  const [identifier, setIdentifier] = useState(""); // يمكن أن يكون رقم هاتف أو بريد إلكتروني
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [isDev, setIsDev] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // التحقق مما إذا كان المستخدم قادمًا من صفحة التسجيل
    const registered = searchParams.get("registered");
    const pending = searchParams.get("pending");
    if (registered === "true") {
      if (pending === "true") {
        setSuccess(
          "تم إنشاء الحساب بنجاح! حسابك في انتظار موافقة المدير. ستتمكن من تسجيل الدخول بعد التفعيل."
        );
      } else {
        setSuccess("تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.");
      }
    }

    // التحقق مما إذا كنا في بيئة التطوير
    setIsDev(process.env.NODE_ENV === "development");
  }, [searchParams]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    if (!identifier || !password) {
      setError("الرجاء إدخال رقم الهاتف أو البريد الإلكتروني وكلمة المرور");
      return;
    }

    setLoading(true);

    try {
      const response = await fetch("api/custom-auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ identifier, password }),
      });
      const data = await response.json();
      if (!response.ok || !data.success) {
        throw new Error(data.error || "بيانات الدخول غير صحيحة");
      }
      localStorage.setItem("user", JSON.stringify(data.user));
      if (data.user.role === "admin") {
        router.push("/admin/dashboard");
      } else {
        router.push("/subscriber/dashboard");
      }
    } catch (error) {
      setError(error.message || "حدث خطأ أثناء تسجيل الدخول");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-r from-indigo-600 to-purple-700">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold text-center text-indigo-700 mb-2">
          تسجيل الدخول
        </h1>
        <p className="text-center text-gray-500 mb-6">
          يرجى إدخال بيانات الدخول الخاصة بك
        </p>
        {error && (
          <div className="p-3 bg-red-100 text-red-700 rounded text-center">
            {error}
          </div>
        )}
        {success && (
          <div className="p-3 bg-green-100 text-green-700 rounded text-center">
            {success}
          </div>
        )}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              رقم الهاتف أو البريد الإلكتروني
            </label>
            <input
              type="text"
              value={identifier}
              onChange={(e) => setIdentifier(e.target.value)}
              required
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="05xxxxxxxx أو <EMAIL>"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">
              كلمة المرور
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="••••••••"
            />
          </div>
          <button
            type="submit"
            disabled={loading}
            className="w-full py-2 px-4 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:bg-indigo-400 font-bold text-lg"
          >
            {loading ? "جاري تسجيل الدخول..." : "تسجيل الدخول"}
          </button>
        </form>
        <div className="mt-4 text-center">
          <Link
            href="/register"
            className="text-indigo-600 hover:text-indigo-800 font-medium"
          >
            إنشاء حساب جديد
          </Link>
        </div>
      </div>
    </div>
  );
}
