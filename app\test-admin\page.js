"use client";

import { useEffect, useState } from "react";
import { fetchAdminStats } from "@/lib/adminDashboardHelpers";

export default function TestAdminPage() {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const testFetch = async () => {
      try {
        console.log("🔍 بدء اختبار جلب بيانات المدير...");
        setLoading(true);
        setError(null);
        
        const adminStats = await fetchAdminStats();
        console.log("📊 البيانات المُستلمة:", adminStats);
        
        setStats(adminStats);
      } catch (err) {
        console.error("❌ خطأ في جلب البيانات:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    testFetch();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-lg">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded max-w-md">
          <h2 className="font-bold text-lg mb-2">خطأ في جلب البيانات</h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">اختبار بيانات المدير</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">البيانات المُستلمة:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
            {JSON.stringify(stats, null, 2)}
          </pre>
        </div>

        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            {/* إحصائيات المستخدمين */}
            <div className="bg-blue-100 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-800 mb-4">المستخدمون</h3>
              <div className="space-y-2">
                <p>الإجمالي: <span className="font-bold">{stats.users.total}</span></p>
                <p>النشطون: <span className="font-bold">{stats.users.active}</span></p>
                <p>المشتركون: <span className="font-bold">{stats.users.subscribers}</span></p>
                <p>المدراء: <span className="font-bold">{stats.users.admins}</span></p>
              </div>
            </div>

            {/* إحصائيات الدعوات */}
            <div className="bg-green-100 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-800 mb-4">الدعوات</h3>
              <div className="space-y-2">
                <p>الإجمالي: <span className="font-bold">{stats.invitations.total}</span></p>
                <p>النشطة: <span className="font-bold">{stats.invitations.active}</span></p>
                <p>القادمة: <span className="font-bold">{stats.invitations.upcoming}</span></p>
              </div>
            </div>

            {/* إحصائيات الضيوف */}
            <div className="bg-purple-100 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-purple-800 mb-4">الضيوف</h3>
              <div className="space-y-2">
                <p>الإجمالي: <span className="font-bold">{stats.guests.total}</span></p>
                <p>المقبولون: <span className="font-bold">{stats.guests.accepted}</span></p>
                <p>المرفوضون: <span className="font-bold">{stats.guests.declined}</span></p>
                <p>الحاضرون: <span className="font-bold">{stats.guests.attended}</span></p>
                <p>معدل القبول: <span className="font-bold">{stats.guests.acceptanceRate}%</span></p>
                <p>معدل الحضور: <span className="font-bold">{stats.guests.attendanceRate}%</span></p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
