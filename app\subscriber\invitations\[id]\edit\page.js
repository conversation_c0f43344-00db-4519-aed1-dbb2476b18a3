'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/Header';
import { supabase } from '@/lib/supabase';
import { FiCalendar, FiClock, FiMapPin, FiMessageSquare, FiSave, FiX } from 'react-icons/fi';

export default function EditInvitationPage({ params }) {
  const invitationId = params.id;
  const [invitation, setInvitation] = useState(null);
  const [title, setTitle] = useState('');
  const [eventDate, setEventDate] = useState('');
  const [eventTime, setEventTime] = useState('');
  const [location, setLocation] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [user, setUser] = useState(null);
  const router = useRouter();

  useEffect(() => {
    // جلب بيانات المستخدم من التخزين المحلي
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    
    fetchInvitation();
  }, [invitationId]);

  const fetchInvitation = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('invitations')
        .select('*')
        .eq('id', invitationId)
        .single();
      
      if (error) throw error;
      
      setInvitation(data);
      setTitle(data.title || '');
      
      // تنسيق التاريخ والوقت
      if (data.event_date) {
        const date = new Date(data.event_date);
        setEventDate(date.toISOString().split('T')[0]);
        setEventTime(date.toTimeString().slice(0, 5));
      }
      
      setLocation(data.location || '');
      setDescription(data.description || '');
      
    } catch (error) {
      console.error('Error fetching invitation:', error);
      setError('حدث خطأ أثناء جلب بيانات الدعوة');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      setError('');
      setSuccess('');
      
      // التحقق من إدخال البيانات المطلوبة
      if (!title || !eventDate || !eventTime || !location) {
        setError('يرجى إدخال جميع البيانات المطلوبة');
        return;
      }
      
      // تنسيق التاريخ والوقت
      const formattedDate = new Date(`${eventDate}T${eventTime}`);
      
      // تحديث بيانات الدعوة
      const { error: updateError } = await supabase
        .from('invitations')
        .update({
          title,
          event_date: formattedDate.toISOString(),
          location,
          description
        })
        .eq('id', invitationId);
      
      if (updateError) throw updateError;
      
      setSuccess('تم تحديث بيانات الدعوة بنجاح');
      
      // العودة إلى صفحة تفاصيل الدعوة بعد 2 ثانية
      setTimeout(() => {
        router.push(`/subscriber/invitations/${invitationId}`);
      }, 2000);
      
    } catch (error) {
      console.error('Error updating invitation:', error);
      setError('حدث خطأ أثناء تحديث بيانات الدعوة');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <>
        <Header userName={user?.name} userRole="subscriber" />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header userName={user?.name} userRole="subscriber" />
      <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">تعديل الدعوة</h1>
            <Link
              href={`/subscriber/invitations/${invitationId}`}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg flex items-center"
            >
              <FiX className="ml-1" /> إلغاء
            </Link>
          </div>
          
          {error && (
            <div className="bg-red-50 border-r-4 border-red-500 text-red-700 p-4 rounded-lg mb-6">
              {error}
            </div>
          )}
          
          {success && (
            <div className="bg-green-50 border-r-4 border-green-500 text-green-700 p-4 rounded-lg mb-6">
              {success}
            </div>
          )}
          
          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="p-6">
              <form onSubmit={handleSubmit}>
                <div className="space-y-6">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                      عنوان الدعوة
                    </label>
                    <div className="relative rounded-md shadow-sm">
                      <input
                        type="text"
                        id="title"
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        className="block w-full pr-10 border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-right"
                        placeholder="أدخل عنوان الدعوة"
                        required
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <FiMessageSquare className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                        التاريخ
                      </label>
                      <div className="relative rounded-md shadow-sm">
                        <input
                          type="date"
                          id="date"
                          value={eventDate}
                          onChange={(e) => setEventDate(e.target.value)}
                          className="block w-full pr-10 border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                          required
                        />
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                          <FiCalendar className="h-5 w-5 text-gray-400" />
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="time" className="block text-sm font-medium text-gray-700 mb-1">
                        الوقت
                      </label>
                      <div className="relative rounded-md shadow-sm">
                        <input
                          type="time"
                          id="time"
                          value={eventTime}
                          onChange={(e) => setEventTime(e.target.value)}
                          className="block w-full pr-10 border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                          required
                        />
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                          <FiClock className="h-5 w-5 text-gray-400" />
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                      المكان
                    </label>
                    <div className="relative rounded-md shadow-sm">
                      <input
                        type="text"
                        id="location"
                        value={location}
                        onChange={(e) => setLocation(e.target.value)}
                        className="block w-full pr-10 border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-right"
                        placeholder="أدخل مكان الحدث"
                        required
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <FiMapPin className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                      الوصف (اختياري)
                    </label>
                    <textarea
                      id="description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      rows={4}
                      className="block w-full border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-right"
                      placeholder="أدخل وصفللدعوة"
                    />
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg flex items-center transition-colors"
                      disabled={saving}
                    >
                      {saving ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white ml-2"></div>
                          جاري الحفظ...
                        </>
                      ) : (
                        <>
                          <FiSave className="ml-1" /> حفظ التغييرات
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
