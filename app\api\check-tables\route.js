import { NextResponse } from 'next/server';
import { supabase } from '../../../lib/supabase';

export async function GET() {
  if (!supabase) {
    return NextResponse.json({
      success: false,
      error: 'Supabase client not initialized',
    }, { status: 500 });
  }

  try {
    const tablesToCheck = ['users', 'invitations', 'templates'];
    const results = {};

    for (const table of tablesToCheck) {
      const { error } = await supabase.from(table).select('id').limit(1);
      results[table] = error ? false : true;
    }

    return NextResponse.json({
      success: true,
      tablesExist: results,
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message,
    }, { status: 500 });
  }
}
