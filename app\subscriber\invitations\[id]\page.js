'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/Header';
import { supabase } from '@/lib/supabase';
import { sendDirectWhatsAppInvitation } from '@/app/actions';
import {
  FiCalendar,
  FiClock,
  FiMapPin,
  FiMessageSquare,
  FiUsers,
  FiSend,
  FiShare2,
  FiEdit,
  FiEye,
  FiRefreshCw,
  FiUser,
  FiPieChart,
  FiDownload
} from 'react-icons/fi';
import { BsQrCodeScan } from 'react-icons/bs';
import dynamic from 'next/dynamic';

// استيراد مكتبة الرسوم البيانية بشكل ديناميكي لتجنب أخطاء SSR
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

// إضافة مكون الرسم البياني الدائري
const Status<PERSON>ie<PERSON><PERSON> = ({ stats }) => {
  const chartOptions = {
    labels: ['قبول', 'اعتذار'],
    colors: ['#10B981', '#EF4444'],
    legend: {
      position: 'bottom',
      horizontalAlign: 'center',
      fontFamily: 'Tajawal, sans-serif',
    },
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          width: 200
        },
        legend: {
          position: 'bottom'
        }
      }
    }],
    dataLabels: {
      enabled: true,
      formatter: function(val, opts) {
        return opts.w.globals.labels[opts.seriesIndex] + ': ' + Math.round(val) + '%';
      },
      style: {
        fontFamily: 'Tajawal, sans-serif',
      }
    },
    tooltip: {
      y: {
        formatter: function(val) {
          return val + ' مدعو';
        }
      }
    }
  };

  const series = [
    stats.accepted || 0,
    stats.declined || 0,
  ];

  return (
    <div className="mt-4">
      {typeof window !== 'undefined' && (
        <Chart
          options={chartOptions}
          series={series}
          type="pie"
          height={300}
        />
      )}
    </div>
  );
};

export default function InvitationDetailsPage({ params }) {
  const invitationId = params.id;
  const [invitation, setInvitation] = useState(null);
  const [attendees, setAttendees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const router = useRouter();
  const [stats, setStats] = useState({
    total: 0,
    accepted: 0,
    declined: 0,
    pending: 0,
    attended: 0,
    acceptanceRate: 0,
    attendanceRate: 0,
    responseRate: 0
  });
  const [user, setUser] = useState(null);
  const [selectedGuest, setSelectedGuest] = useState(null);
  const [showQRModal, setShowQRModal] = useState(false);
  const [qrCodeImage, setQrCodeImage] = useState(null);

  useEffect(() => {
    // التحقق من وجود المستخدم في localStorage
    const userData = localStorage.getItem('user');
    if (userData) {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
    } else {
      router.push('/login');
    }
    
    fetchData();
  }, [invitationId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      console.log("Fetching data for invitation ID:", invitationId);

      // جلب بيانات الدعوة
      const { data: invitationData, error: invitationError } = await supabase
        .from("invitations")
        .select("*")
        .eq("id", invitationId)
        .single();

      if (invitationError) {
        console.error("Error fetching invitation:", invitationError);
        setError("لم يتم العثور على الدعوة المطلوبة");
        setLoading(false);
        return;
      }

      setInvitation(invitationData);
      console.log("Invitation data:", invitationData);

      // محاولة جلب البيانات من جدول attendance أولاً
      let { data: attendeesData, error: attendeesError } = await supabase
        .from("attendance")
        .select("*")
        .eq("invitation_id", invitationId);

      // إذا لم يكن هناك بيانات في جدول attendance، نحاول من جدول guests
      if (attendeesError || !attendeesData || attendeesData.length === 0) {
        console.log("No data in attendance table, trying guests table");
        const { data: guestsData, error: guestsError } = await supabase
          .from("guests")
          .select("*")
          .eq("invitation_id", invitationId);

        if (guestsError) {
          console.error("Error fetching guests:", guestsError);
          setError("حدث خطأ أثناء جلب بيانات المدعوين");
          setLoading(false);
          return;
        }

        // تحويل بيانات guests إلى نفس هيكل بيانات attendance
        attendeesData = guestsData?.map(guest => ({
          id: guest.id,
          invitation_id: guest.invitation_id,
          name: guest.name,
          phone: guest.phone,
          status: guest.status,
          attended: guest.attended || false,
          qr_code: guest.qr_code
        })) || [];
      }

      console.log("Attendees data:", attendeesData);
      setAttendees(attendeesData || []);

      // حساب الإحصائيات بشكل دقيق
      const total = attendeesData?.length || 0;
      const accepted = attendeesData?.filter((a) => a.status === "accepted").length || 0;
      const declined = attendeesData?.filter((a) => a.status === "declined").length || 0;
      const pending = attendeesData?.filter((a) => 
        a.status === "pending" || a.status === "sent" || !a.status
      ).length || 0;
      const attended = attendeesData?.filter((a) => a.attended).length || 0;

      // إضافة إحصائيات إضافية
      const acceptanceRate = total > 0 ? Math.round((accepted / total) * 100) : 0;
      const attendanceRate = accepted > 0 ? Math.round((attended / accepted) * 100) : 0;
      const responseRate = total > 0 ? Math.round(((accepted + declined) / total) * 100) : 0;

      const newStats = {
        total,
        accepted,
        declined,
        pending,
        attended,
        acceptanceRate,
        attendanceRate,
        responseRate
      };

      console.log("Calculated stats:", newStats);
      setStats(newStats);

      setLoading(false);
    } catch (error) {
      console.error("Error:", error);
      setError("حدث خطأ أثناء جلب البيانات");
      setLoading(false);
    }
  };

  // حذف دالة handleResendInvitation لأننا لن نستخدمها بعد الآن

  // إضافة دالة إنشاء رابط المشاركة
  const generateWhatsAppShareLink = (invitation) => {
    try {
      // تنسيق التاريخ والوقت
      const eventDate = new Date(invitation.event_date);
      const formattedDate = eventDate.toLocaleDateString("ar-SA", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      });
      const formattedTime = eventDate.toLocaleTimeString("ar-SA", {
        hour: "2-digit",
        minute: "2-digit",
      });

      // إنشاء رابط المشاركة
      const shareLink = `https://wa.me/?text=${encodeURIComponent(`
        🎉 دعوة! 🎉

        يسرنا دعوتكم لحضور ${invitation.title}

        🗓️ ${formattedDate}
        🕒 ${formattedTime}
        📍 ${invitation.location}

        ${invitation.description ? invitation.description + "\n" : ""}
        
        للتأكيد على الحضور، يرجى الضغط على الرابط:
        ${
          process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
        }/invitation/${invitation.id}
      `)}`;

      return shareLink;
    } catch (error) {
      console.error("Error generating WhatsApp share link:", error);
      return null;
    }
  };

  const copyInvitationLink = () => {
    const link = `${
      process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
    }/invitation/${invitationId}`;
    navigator.clipboard.writeText(link);
    setSuccess("تم نسخ رابط الدعوة!");
    setTimeout(() => setSuccess(""), 3000);
  };

  if (loading) {
    return (
      <>
        <Header userName={user?.name} userRole="subscriber" />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Header userName={user?.name} userRole="subscriber" />
        <div className="min-h-screen bg-gray-50 p-8">
          <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-md p-6">
            <div className="bg-red-50 border-r-4 border-red-500 p-4 rounded-lg">
              <p className="text-red-700">{error}</p>
            </div>
            <div className="mt-6 text-center">
              <Link
                href="/subscriber/invitations"
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                العودة للقائمة
              </Link>
            </div>
          </div>
        </div>
      </>
    );
  }

  // دالة عرض QR Code للضيف
  const showQRCode = async (guest) => {
    try {
      setSelectedGuest(guest);
      setShowQRModal(true);

      if (guest.qr_code) {
        // استيراد مكتبة QR Code
        const { generateQRCodeImage } = await import('@/lib/qrCodeHelpers');
        const qrImage = await generateQRCodeImage(guest.qr_code, {
          width: 300,
          color: {
            dark: '#1a365d',
            light: '#ffffff'
          }
        });
        setQrCodeImage(qrImage);
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      setError('فشل في إنتاج رمز QR');
    }
  };

  // دالة إغلاق مودال QR
  const closeQRModal = () => {
    setShowQRModal(false);
    setSelectedGuest(null);
    setQrCodeImage(null);
  };

  // دالة تحميل QR Code
  const downloadQRCode = () => {
    if (qrCodeImage && selectedGuest) {
      const link = document.createElement('a');
      link.download = `qr-code-${selectedGuest.name}.png`;
      link.href = qrCodeImage;
      link.click();
    }
  };

  // تنسيق التاريخ والوقت
  const eventDate = new Date(invitation.event_date);
  const formattedDate = eventDate.toLocaleDateString("ar-SA", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const formattedTime = eventDate.toLocaleTimeString("ar-SA", {
    hour: "2-digit",
    minute: "2-digit",
  });

  return (
    <>
      <Header userName={user?.name} userRole="subscriber" />
      <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* رسائل النجاح والخطأ */}
          {error && (
            <div className="bg-red-50 border-r-4 border-red-500 text-red-700 p-4 rounded-lg mb-6 flex items-center">
              <svg
                className="h-6 w-6 text-red-500 ml-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>{error}</span>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border-r-4 border-green-500 text-green-700 p-4 rounded-lg mb-6 flex items-center">
              <svg
                className="h-6 w-6 text-green-500 ml-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <span>{success}</span>
            </div>
          )}

          {/* رأس الصفحة المحسن */}
          <div className="bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-2xl shadow-2xl p-8 mb-8 text-white relative overflow-hidden">
            {/* خلفية ديكورية */}
            <div className="absolute inset-0 bg-black bg-opacity-10"></div>
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-white bg-opacity-10 rounded-full"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-white bg-opacity-5 rounded-full"></div>

            <div className="relative z-10">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div className="flex-1">
                  <div className="flex items-center mb-4">
                    <div className="bg-white bg-opacity-20 p-3 rounded-full ml-4">
                      <FiCalendar className="h-6 w-6" />
                    </div>
                    <div>
                      <h1 className="text-4xl font-bold mb-2 leading-tight">{invitation.title}</h1>
                      <div className="flex flex-wrap items-center gap-4 text-indigo-100">
                        <div className="flex items-center">
                          <FiCalendar className="ml-2" />
                          <span className="font-medium">{formattedDate}</span>
                        </div>
                        <div className="flex items-center">
                          <FiClock className="ml-2" />
                          <span className="font-medium">{formattedTime}</span>
                        </div>
                        {invitation.location && (
                          <div className="flex items-center">
                            <FiMapPin className="ml-2" />
                            <span className="font-medium">{invitation.location}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-wrap gap-3 mt-6 lg:mt-0">
                  <Link
                    href={`/subscriber/invitations/${invitationId}/scan`}
                    className="bg-white bg-opacity-20 backdrop-blur-sm text-white hover:bg-opacity-30 px-6 py-3 rounded-xl flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <BsQrCodeScan className="ml-2 h-5 w-5" />
                    <span className="font-semibold">مسح QR</span>
                  </Link>
                <Link
                  href={`/subscriber/invitations/${invitationId}/guests`}
                  className="bg-white text-indigo-700 hover:bg-indigo-50 px-4 py-2 rounded-lg flex items-center transition-colors"
                >
                  <FiUsers className="ml-1" /> المدعوون
                </Link>
                <Link
                  href={`/subscriber/invitations/${invitationId}/edit`}
                  className="bg-white text-indigo-700 hover:bg-indigo-50 px-4 py-2 rounded-lg flex items-center transition-colors"
                >
                  <FiEdit className="ml-1" /> تعديل
                </Link>
                <Link
                  href="/subscriber/invitations"
                  className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
                >
                  العودة للقائمة
                </Link>
              </div>
            </div>
          </div>

          {/* إعادة تنظيم المحتوى الرئيسي */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* القسم الأول: تفاصيل الدعوة (عرض 2 أعمدة) */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="border-b border-gray-200 bg-gray-50 px-6 py-4 flex justify-between items-center">
                  <h2 className="text-xl font-bold text-gray-800">
                    تفاصيل الدعوة
                  </h2>
                  <Link
                    href={`/subscriber/invitations/${invitationId}/edit`}
                    className="text-indigo-600 hover:text-indigo-800 flex items-center text-sm"
                  >
                    <FiEdit className="ml-1" /> تعديل
                  </Link>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">
                          عنوان الدعوة
                        </p>
                        <p className="font-medium text-gray-900">
                          {invitation.title}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">التاريخ</p>
                        <p className="font-medium text-gray-900 flex items-center">
                          <FiCalendar className="text-indigo-500 ml-1" />{" "}
                          {formattedDate}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">الوقت</p>
                        <p className="font-medium text-gray-900 flex items-center">
                          <FiClock className="text-indigo-500 ml-1" />{" "}
                          {formattedTime}
                        </p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">المكان</p>
                        <p className="font-medium text-gray-900 flex items-center">
                          <FiMapPin className="text-indigo-500 ml-1" />{" "}
                          {invitation.location}
                        </p>
                      </div>
                      {invitation.description && (
                        <div>
                          <p className="text-sm text-gray-500 mb-1">الرسالة</p>
                          <p className="font-medium text-gray-900 whitespace-pre-line">
                            {invitation.description}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mt-8 border-t border-gray-200 pt-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      مشاركة الدعوة
                    </h3>
                    <div className="flex flex-wrap gap-3">
                      <a
                        href={generateWhatsAppShareLink(invitation)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
                      >
                        <FiShare2 className="ml-1" /> مشاركة عبر واتساب
                      </a>
                      <button
                        onClick={copyInvitationLink}
                        className="bg-indigo-100 hover:bg-indigo-200 text-indigo-700 px-4 py-2 rounded-lg flex items-center transition-colors"
                      >
                        <svg
                          className="h-4 w-4 ml-1"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                          />
                        </svg>
                        نسخ رابط الدعوة
                      </button>
                      <Link
                        href={`/invitation/${invitationId}`}
                        target="_blank"
                        className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center transition-colors"
                      >
                        <FiEye className="ml-1" /> معاينة الدعوة
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* قسم المدعوين الأخيرين (مباشرة تحت تفاصيل الدعوة) */}
              <div className="mt-8">
                <div className="bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="border-b border-gray-200 bg-gray-50 px-6 py-4 flex justify-between items-center">
                    <h2 className="text-xl font-bold text-gray-800">
                      آخر المدعوين
                    </h2>
                    <Link
                      href={`/subscriber/invitations/${invitationId}/guests`}
                      className="text-indigo-600 hover:text-indigo-800 text-sm"
                    >
                      عرض الكل
                    </Link>
                  </div>
                  <div className="p-6">
                    {attendees.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th
                                scope="col"
                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                الاسم
                              </th>
                              <th
                                scope="col"
                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                رقم الهاتف
                              </th>
                              <th
                                scope="col"
                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                الحالة
                              </th>
                              <th
                                scope="col"
                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                الحضور
                              </th>
                              <th
                                scope="col"
                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                QR Code
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {attendees.slice(0, 5).map((attendee) => (
                              <tr key={attendee.id}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="text-sm font-medium text-gray-900">
                                    {attendee.name || "غير محدد"}
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="text-sm text-gray-500">
                                    {attendee.phone || "غير محدد"}
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span
                                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    ${
                                      attendee.status === "accepted"
                                        ? "bg-green-100 text-green-800"
                                        : attendee.status === "declined"
                                        ? "bg-red-100 text-red-800"
                                        : "bg-yellow-100 text-yellow-800"
                                    }`}
                                  >
                                    {attendee.status === "accepted"
                                      ? "قبول"
                                      : attendee.status === "declined"
                                      ? "اعتذار"
                                      : attendee.status === "sent"
                                      ? "تم الإرسال"
                                      : "في الانتظار"}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {attendee.attended ? (
                                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                                      حضر
                                    </span>
                                  ) : (
                                    <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs">
                                      لم يحضر
                                    </span>
                                  )}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {attendee.status === "accepted" && attendee.qr_code ? (
                                    <button
                                      onClick={() => showQRCode(attendee)}
                                      className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-2 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 flex items-center text-xs font-medium shadow-md"
                                    >
                                      <BsQrCodeScan className="ml-1 h-4 w-4" />
                                      عرض QR
                                    </button>
                                  ) : attendee.status === "accepted" ? (
                                    <span className="text-orange-600 text-xs">لا يوجد QR</span>
                                  ) : (
                                    <span className="text-gray-400 text-xs">غير متاح</span>
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <svg
                          className="mx-auto h-12 w-12 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                          />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">
                          لا يوجد مدعوين
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                          ابدأ بإضافة مدعوين لهذه الدعوة.
                        </p>
                        <div className="mt-6">
                          <Link
                            href={`/subscriber/invitations/${invitationId}/guests/add`}
                            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                          >
                            <svg
                              className="-mr-1 ml-2 h-5 w-5"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                              aria-hidden="true"
                            >
                              <path
                                fillRule="evenodd"
                                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                            إضافة مدعوين
                          </Link>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* القسم الثاني: الإحصائيات (عرض عمود واحد) */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
                  <h2 className="text-xl font-bold text-gray-800">
                    إحصائيات الدعوة
                  </h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="bg-indigo-50 rounded-lg p-4 text-center">
                      <p className="text-3xl font-bold text-indigo-700">{stats.total}</p>
                      <p className="text-sm text-gray-600">إجمالي المدعوين</p>
                    </div>
                    <div className="bg-green-50 rounded-lg p-4 text-center">
                      <p className="text-3xl font-bold text-green-700">{stats.accepted}</p>
                      <p className="text-sm text-gray-600">قبول الدعوة</p>
                    </div>
                    <div className="bg-red-50 rounded-lg p-4 text-center">
                      <p className="text-3xl font-bold text-red-700">{stats.declined}</p>
                      <p className="text-sm text-gray-600">اعتذار</p>
                    </div>
                    <div className="bg-purple-50 rounded-lg p-4 text-center">
                      <p className="text-3xl font-bold text-purple-700">{stats.attended}</p>
                      <p className="text-sm text-gray-600">الحضور الفعلي</p>
                    </div>
                  </div>
                  
                  {/* المخطط الدائري */}
                  <div className="mt-6 mb-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">توزيع حالات المدعوين</h3>
                    <StatusPieChart stats={stats} />
                  </div>
                  
                  {/* نسب مئوية */}
                  <div className="space-y-4">
                    <div className="relative pt-1">
                      <div className="flex mb-2 items-center justify-between">
                        <div>
                          <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-600 bg-green-200">
                            نسبة القبول
                          </span>
                        </div>
                        <div className="text-left">
                          <span className="text-xs font-semibold inline-block text-green-600">
                            {stats.acceptanceRate}%
                          </span>
                        </div>
                      </div>
                      <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-green-200">
                        <div
                          style={{ width: `${stats.acceptanceRate}%` }}
                          className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"
                        ></div>
                      </div>
                    </div>
                    
                    <div className="relative pt-1">
                      <div className="flex mb-2 items-center justify-between">
                        <div>
                          <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-purple-600 bg-purple-200">
                            نسبة الحضور
                          </span>
                        </div>
                        <div className="text-left">
                          <span className="text-xs font-semibold inline-block text-purple-600">
                            {stats.attendanceRate}%
                          </span>
                        </div>
                      </div>
                      <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-purple-200">
                        <div
                          style={{ width: `${stats.attendanceRate}%` }}
                          className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-purple-500"
                        ></div>
                      </div>
                    </div>
                    
                    <div className="relative pt-1">
                      <div className="flex mb-2 items-center justify-between">
                        <div>
                          <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200">
                            نسبة الاستجابة
                          </span>
                        </div>
                        <div className="text-left">
                          <span className="text-xs font-semibold inline-block text-blue-600">
                            {stats.responseRate}%
                          </span>
                        </div>
                      </div>
                      <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200">
                        <div
                          style={{ width: `${stats.responseRate}%` }}
                          className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500"
                        ></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* زر تحديث الإحصائيات */}
                  <button
                    onClick={fetchData}
                    className="mt-4 w-full bg-gray-100 hover:bg-gray-200 text-gray-800 font-bold py-2 px-4 rounded flex items-center justify-center transition-colors"
                  >
                    <FiRefreshCw className="ml-2" />
                    تحديث الإحصائيات
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* مودال عرض QR Code */}
      {showQRModal && selectedGuest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
            {/* رأس المودال */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-bold">QR Code للدخول</h3>
                  <p className="text-blue-100 text-sm mt-1">{selectedGuest.name}</p>
                </div>
                <button
                  onClick={closeQRModal}
                  className="text-white hover:text-gray-200 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* محتوى المودال */}
            <div className="p-6 text-center">
              {qrCodeImage ? (
                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-xl">
                    <img
                      src={qrCodeImage}
                      alt="QR Code"
                      className="mx-auto rounded-lg shadow-md"
                    />
                  </div>

                  <div className="text-sm text-gray-600 space-y-2">
                    <p><strong>الاسم:</strong> {selectedGuest.name}</p>
                    <p><strong>الهاتف:</strong> {selectedGuest.phone}</p>
                    <p><strong>الحالة:</strong>
                      <span className="text-green-600 font-medium mr-1">مقبول</span>
                    </p>
                  </div>

                  <div className="flex gap-3 pt-4">
                    <button
                      onClick={downloadQRCode}
                      className="flex-1 bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center justify-center font-medium"
                    >
                      <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      تحميل
                    </button>
                    <button
                      onClick={closeQRModal}
                      className="flex-1 bg-gray-500 text-white py-3 px-4 rounded-xl hover:bg-gray-600 transition-colors font-medium"
                    >
                      إغلاق
                    </button>
                  </div>
                </div>
              ) : (
                <div className="py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p className="text-gray-600">جاري إنتاج رمز QR...</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}




