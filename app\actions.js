"use server";

import { supabase } from "@/lib/supabase";
import { revalidatePath } from "next/cache";
import { v4 as uuidv4 } from "uuid";

// دالة لإنشاء دعوة جديدة
export async function createAndSendInvitation(
  userId,
  title,
  eventDate,
  location,
  message,
  phoneNumber,
  guestName,
  groomName = null,
  brideName = null
) {
  try {
    // التحقق من صحة user_id
    if (!userId) {
      throw new Error("معرف المستخدم مطلوب");
    }

    // التحقق من وجود المستخدم في قاعدة البيانات
    const { data: userExists, error: userCheckError } = await supabase
      .from("users")
      .select("id, name, is_active")
      .eq("id", userId)
      .single();

    if (userCheckError || !userExists) {
      console.error("User not found:", userCheckError);
      throw new Error(
        "المستخدم غير موجود في قاعدة البيانات. يرجى تسجيل الدخول مرة أخرى."
      );
    }

    if (!userExists.is_active) {
      throw new Error("حسابك غير مفعل. يرجى انتظار موافقة المدير.");
    }

    // إنشاء الدعوة في قاعدة البيانات
    const invitationData = {
      user_id: userId,
      title: title || "حفل زفاف",
      event_date: eventDate,
      location: location || "",
      description: message || "",
      status: "draft",
      additional_data: {
        groom_name: groomName || "",
        bride_name: brideName || "",
      },
    };

    // إضافة حقول اختيارية إذا كانت متوفرة في قاعدة البيانات
    // سنخزن هذه البيانات في حقل additional_data إذا كان موجودًا
    const additionalInfo = {
      groom_name: groomName,
      bride_name: brideName,
    };

    // إضافة البيانات الإضافية مباشرة إلى الوصف
    if (groomName || brideName) {
      invitationData.description =
        `${message || ""}\n\nمعلومات إضافية:\n` +
        (groomName ? `العريس: ${groomName}\n` : "") +
        (brideName ? `العروس: ${brideName}` : "");
    }

    const { data, error: invitationError } = await supabase
      .from("invitations")
      .insert(invitationData)
      .select()
      .single();

    if (invitationError) {
      console.error("Error creating invitation:", invitationError);
      return { success: false, error: invitationError.message };
    }

    // إنشاء رابط المشاركة
    const shareLink = generateWhatsAppShareLink({
      ...data,
      groom_name: groomName,
      bride_name: brideName,
    });

    return {
      success: true,
      data,
      shareLink,
    };
  } catch (error) {
    console.error("Error creating invitation:", error);
    return { success: false, error: error.message };
  }
}

// دالة لإنشاء رابط مشاركة واتساب
function generateWhatsAppShareLink(invitation) {
  try {
    // استخراج البيانات من الدعوة
    const { title, event_date, location, description, id } = invitation;
    const groomName = invitation.groom_name || "";
    const brideName = invitation.bride_name || "";

    // تنسيق التاريخ والوقت
    const eventDate = new Date(event_date);
    const formattedDate = eventDate.toLocaleDateString("ar-SA", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    const formattedTime = eventDate.toLocaleTimeString("ar-SA", {
      hour: "2-digit",
      minute: "2-digit",
    });

    // إنشاء نص الرسالة
    const messageText = `عزيزنا الضيف الكريم،

بكل حب ومودة، ندعوك لمشاركتنا فرحتنا في ${title}:
${groomName ? `العريس: ${groomName}` : ""}
${brideName ? `إلى كريمتنا: ${brideName}` : ""}

📅 التاريخ: ${formattedDate}
🕒 الساعة: ${formattedTime}
📍 المكان: ${location}

${description ? description.replace(/^{.*}$/m, "") : ""}
حضوركم سعدنا ويُزيد من بهجة مناسبتنا.
للتأكيد على حضوركم، يرجى الضغط على الرابط التالي:

http://localhost:3000/invitation/${id}

📞 للاستفسارات، يرجى التواصل معانا عبر المنصة.`;

    // إنشاء رابط واتساب
    const encodedMessage = encodeURIComponent(messageText);
    return `https://wa.me/?text=${encodedMessage}`;
  } catch (error) {
    console.error("Error generating WhatsApp link:", error);
    return "#";
  }
}

// دالة لتحديث حالة حضور الضيف
export async function updateGuestAttendanceStatus(
  confirmationToken,
  status,
  numberOfCompanions = 0
) {
  try {
    // استدعاء وظيفة قاعدة البيانات
    const { data, error } = await supabase.rpc("update_guest_status", {
      _confirmation_token: confirmationToken,
      _status: status,
      _number_of_companions: numberOfCompanions,
    });

    if (error) {
      console.error("Error updating guest status:", error);
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    console.error("Error updating guest status:", error);
    return { success: false, error: error.message };
  }
}

// دالة لإضافة ضيف جديد للدعوة
export async function addGuestToInvitation(
  invitationId,
  guestName,
  phoneNumber
) {
  try {
    const { data: invitation, error: invitationError } = await supabase
      .from("invitations")
      .select("user_id")
      .eq("id", invitationId)
      .single();

    if (invitationError) {
      throw new Error("لم يتم العثور على الدعوة");
    }

    const guestData = {
      invitation_id: invitationId,
      name: guestName,
      phone: phoneNumber,
      status: "pending",
      confirmation_token: uuidv4(),
    };

    const { data, error } = await supabase
      .from("guests")
      .insert(guestData)
      .select()
      .single();

    if (error) {
      console.error("Error adding guest:", error);
      return { success: false, error: error.message };
    }

    revalidatePath(`/subscriber/invitations/${invitationId}/guests`);
    return { success: true, data };
  } catch (error) {
    console.error("Error adding guest:", error);
    return { success: false, error: error.message };
  }
}

// إضافة مشترك جديد
export async function addSubscriber(name, phone, password) {
  const { data, error } = await supabase
    .from("users")
    .insert([{ name, phone, password, role: "subscriber" }]);
  revalidatePath("/admin/subscribers");
  return { data, error };
}
