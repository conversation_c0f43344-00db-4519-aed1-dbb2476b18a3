-- إضافة المستخدم المفقود بسرعة
-- تشغيل هذا السكريبت سيحل المشكلة فوراً

-- إضافة المستخدم المفقود مع ID المحدد
INSERT INTO public.users (id, name, phone, password, role, is_active) 
VALUES (
    'f2ccc0c9-c8f1-4a17-af15-8e8a93bda15f',
    'مستخدم مستعاد',
    '0500000999',
    '123456',
    'subscriber',
    true
)
ON CONFLICT (id) DO UPDATE SET
    is_active = true,
    name = 'مستخدم مستعاد',
    phone = '0500000999';

-- التحقق من نجاح العملية
SELECT 
    'تم إضافة المستخدم بنجاح:' as result,
    id,
    name,
    phone,
    role,
    is_active
FROM public.users 
WHERE id = 'f2ccc0c9-c8f1-4a17-af15-8e8a93bda15f';
