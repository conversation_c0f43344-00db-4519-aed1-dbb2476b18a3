-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    allow_new_registrations BOOLEAN DEFAULT true,
    enable_whatsapp_notifications B<PERSON><PERSON>EA<PERSON> DEFAULT true,
    enable_email_notifications B<PERSON><PERSON><PERSON><PERSON> DEFAULT true,
    require_email_verification BO<PERSON><PERSON><PERSON> DEFAULT true,
    enable_guest_tracking BOOLEAN DEFAULT true,
    enable_qr_code_scanning BOOLEAN DEFAULT true,
    max_invitations_per_subscriber INTEGER DEFAULT 100,
    enable_automatic_reminders BOOLEAN DEFAULT true,
    maintenance_mode BOOLEAN DEFAULT false,
    enable_analytics BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW())
);

-- Create trigger to update updated_at
CREATE OR REPLACE FUNCTION update_system_settings_updated_at()
<PERSON><PERSON>URNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_system_settings_updated_at
    BEFORE UPDATE ON system_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_system_settings_updated_at();

-- Insert default settings if not exists
INSERT INTO system_settings (id)
SELECT uuid_generate_v4()
WHERE NOT EXISTS (SELECT 1 FROM system_settings);

-- Set up RLS policies
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ADD COLUMN enable_analytics BOOLEAN DEFAULT true;

CREATE POLICY "Allow admins full access" ON system_settings
    USING (auth.jwt() ->> 'role' = 'admin')
    WITH CHECK (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow subscribers to view settings" ON system_settings
    FOR SELECT
    USING (auth.jwt() ->> 'role' = 'subscriber');
