import { NextResponse } from "next/server";
import { supabase } from "../../../lib/supabase";

export async function POST(request) {
  try {
    const { phone, password } = await request.json();

    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("phone", phone)
      .eq("password", password)
      .single();

    if (error || !data) {
      return NextResponse.json(
        {
          success: false,
          error: "بيانات الدخول غير صحيحة",
        },
        { status: 401 }
      );
    }

    // التحقق من تفعيل الحساب
    if (data.is_active === false) {
      return NextResponse.json(
        {
          success: false,
          error: "الحساب غير مفعل بعد. يرجى انتظار موافقة المدير.",
        },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      user: {
        id: data.id,
        name: data.name,
        role: data.role,
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "حدث خطأ أثناء تسجيل الدخول",
      },
      { status: 500 }
    );
  }
}
