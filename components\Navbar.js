'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Logo from './Logo';

export default function Navbar({ userRole = 'subscriber' }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [user, setUser] = useState(null);
  const router = useRouter();

  useEffect(() => {
    // محاولة الحصول على بيانات المستخدم من localStorage
    try {
      const userData = localStorage.getItem('user');
      if (userData) {
        setUser(JSON.parse(userData));
      }
    } catch (error) {
      console.error('Error getting user from localStorage:', error);
    }
  }, []);

  const handleLogout = () => {
    // حذف بيانات المستخدم من التخزين المحلي
    localStorage.removeItem('user');
    router.push('/login');
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className="bg-white shadow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Logo />
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8 sm:space-x-reverse">
              {userRole === 'admin' ? (
                <>
                  <Link 
                    href="/admin/dashboard" 
                    className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                  >
                    لوحة التحكم
                  </Link>
                  <Link 
                    href="/admin/subscribers" 
                    className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                  >
                    المشتركين
                  </Link>
                </>
              ) : (
                <>
                  <Link 
                    href="/subscriber/dashboard" 
                    className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                  >
                    لوحة التحكم
                  </Link>
                  <Link 
                    href="/subscriber/invitations" 
                    className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                  >
                    الدعوات
                  </Link>
                </>
              )}
            </div>
          </div>
          <div className="hidden sm:ml-6 sm:flex sm:items-center">
            <button
              onClick={handleLogout}
              className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            >
              تسجيل الخروج
            </button>
          </div>
          <div className="-mr-2 flex items-center sm:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            >
              <span className="sr-only">فتح القائمة الرئيسية</span>
              {isMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>
      
      <div className={`${isMenuOpen ? 'block' : 'hidden'} sm:hidden`}>
        <div className="pt-2 pb-3 space-y-1">
          <Link 
            href={`/${userRole}/dashboard`}
            className="block pr-3 pl-4 py-2 border-r-4 border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 text-base font-medium"
          >
            لوحة التحكم
          </Link>
          
          <Link 
            href={`/${userRole}/invitations`}
            className="block pr-3 pl-4 py-2 border-r-4 border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 text-base font-medium"
          >
            الدعوات
          </Link>
          
          {userRole === 'admin' && (
            <>
              <Link 
                href="/admin/users"
                className="block pr-3 pl-4 py-2 border-r-4 border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 text-base font-medium"
              >
                المستخدمين
              </Link>
              <Link 
                href="/admin/templates"
                className="block pr-3 pl-4 py-2 border-r-4 border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 text-base font-medium"
              >
                قوالب الدعوات
              </Link>
            </>
          )}
          
          <Link 
            href={`/${userRole}/profile`}
            className="block pr-3 pl-4 py-2 border-r-4 border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 text-base font-medium"
          >
            الملف الشخصي
          </Link>
          
          <button
            onClick={handleLogout}
            className="w-full text-right block pr-3 pl-4 py-2 border-r-4 border-transparent text-red-600 hover:text-red-800 hover:bg-red-50 hover:border-red-300 text-base font-medium"
          >
            تسجيل الخروج
          </button>
        </div>
      </div>
    </nav>
  );
}




