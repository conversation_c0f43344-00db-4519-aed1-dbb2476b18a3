import { Cairo } from 'next/font/google';
import './globals.css';

const cairo = Cairo({ 
  subsets: ['arabic'],
  weight: ['300', '400', '500', '700'],
  display: 'swap'
});

export const metadata = {
  title: 'نظام إدارة الدعوات',
  description: 'منصة متكاملة لإدارة الدعوات وتتبع الحضور',
};

export default function RootLayout({ children }) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${cairo.className} bg-gray-50 text-gray-800`}>
        {children}
      </body>
    </html>
  );
}




