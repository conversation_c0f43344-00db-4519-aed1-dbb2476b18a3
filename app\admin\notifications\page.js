"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiX } from "react-icons/fi";

export default function AdminNotificationsPage() {
  const router = useRouter();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // هنا سنقوم بجلب الإشعارات من قاعدة البيانات
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      // قم بتنفيذ طلب API لجلب الإشعارات
      const response = await fetch("/api/admin/notifications");
      const data = await response.json();

      if (data.success) {
        setNotifications(data.notifications);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleActivateUser = async (userId) => {
    try {
      const response = await fetch("/api/admin/activate-user", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId }),
      });

      const data = await response.json();

      if (data.success) {
        // تحديث قائمة الإشعارات
        setNotifications(notifications.filter((n) => n.userId !== userId));
      }
    } catch (error) {
      console.error("Error activating user:", error);
    }
  };


  if (loading) {
    return <div>جاري التحميل...</div>;
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold flex items-center">
          <FiBell className="mr-2" /> الإشعارات
        </h1>
      </div>

      <div className="bg-white rounded-lg shadow">
        {notifications.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            لا توجد إشعارات جديدة
          </div>
        ) : (
          <div className="divide-y">
            {notifications.map((notification) => (
              <div key={notification.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">طلب تفعيل حساب جديد</h3>
                    <p className="text-sm text-gray-600">
                      {notification.userName} - {notification.userEmail}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(notification.createdAt).toLocaleString("ar-SA")}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleActivateUser(notification.userId)}
                      className="bg-green-500 text-white px-3 py-1 rounded-md hover:bg-green-600 flex items-center"
                    >
                      <FiCheck className="ml-1" />
                      تفعيل
                    </button>
                    <button
                      onClick={() => handleRejectUser(notification.userId)}
                      className="bg-red-500 text-white px-3 py-1 rounded-md hover:bg-red-600 flex items-center"
                    >
                      <FiX className="ml-1" />
                      رفض
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
