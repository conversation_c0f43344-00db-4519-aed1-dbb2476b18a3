-- إعادة تعيين السياسات
DROP POLICY IF EXISTS "View guests policy" ON public.guests;
DROP POLICY IF EXISTS "Guests can view their own data" ON public.guests;
DROP POLICY IF EXISTS "Guests can update their status" ON public.guests;
DROP POLICY IF EXISTS "Manage guests policy" ON public.guests;
DROP POLICY IF EXISTS "Allow anon insert" ON public.guests;

-- تفعيل RLS
ALTER TABLE public.guests ENABLE ROW LEVEL SECURITY;

-- منح الصلاحيات
GRANT ALL ON public.guests TO authenticated;
GRANT ALL ON public.guests TO anon;

-- السماح بإضافة ضيوف جدد (للجميع)
CREATE POLICY "Allow insert" ON public.guests
    FOR INSERT
    WITH CHECK (true);

-- السماح بعرض الضيوف لصاحب الدعوة والمشرفين
CREATE POLICY "View guests policy" ON public.guests
    FOR SELECT
    USING (
        auth.role() = 'authenticated'::text
        AND (
            EXISTS (
                SELECT 1 FROM public.invitations i
                WHERE i.id = invitation_id
                AND (
                    i.user_id = auth.uid()
                    OR EXISTS (
                        SELECT 1 FROM public.users
                        WHERE users.id = auth.uid() AND users.role = 'admin'
                    )
                )
            )
        )
    );

-- السماح بتحديث الضيوف
CREATE POLICY "Update guests policy" ON public.guests
    FOR UPDATE
    USING (true)
    WITH CHECK (
        status IN ('confirmed', 'declined', 'pending')
        AND number_of_companions >= 0
        AND number_of_companions <= 10
    );
