-- تحديث نظام QR Code للصفحة العامة للدعوات
-- هذا الملف يضمن أن QR codes يتم إنشاؤها تلقائياً عند قبول الدعوة من الصفحة العامة

-- التأكد من وجود عمود qr_code في جدول guests
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'guests' AND column_name = 'qr_code'
    ) THEN
        ALTER TABLE guests ADD COLUMN qr_code TEXT;
    END IF;
END $$;

-- التأكد من وجود عمود attended_at في جدول guests
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'guests' AND column_name = 'attended_at'
    ) THEN
        ALTER TABLE guests ADD COLUMN attended_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- دالة محسنة لإنتاج QR code للضيوف
CREATE OR REPLACE FUNCTION generate_guest_qr_code(
    p_invitation_id UUID,
    p_guest_phone TEXT,
    p_guest_name TEXT DEFAULT NULL
) RETURNS TEXT AS $$
DECLARE
    qr_data JSONB;
    encoded_qr TEXT;
    unique_id UUID;
BEGIN
    -- إنشاء معرف فريد
    unique_id := gen_random_uuid();
    
    -- إنشاء بيانات QR code
    qr_data := jsonb_build_object(
        'id', unique_id,
        'invitationId', p_invitation_id,
        'phone', p_guest_phone,
        'name', COALESCE(p_guest_name, ''),
        'timestamp', extract(epoch from now()) * 1000,
        'type', 'guest_entry',
        'version', '2.0'
    );
    
    -- تشفير البيانات
    encoded_qr := encode(qr_data::text::bytea, 'base64');
    
    RETURN encoded_qr;
END;
$$ LANGUAGE plpgsql;

-- دالة لإنشاء أو تحديث QR code عند قبول الدعوة
CREATE OR REPLACE FUNCTION update_guest_qr_on_accept()
RETURNS TRIGGER AS $$
BEGIN
    -- إذا تم تغيير الحالة إلى accepted وكان QR code فارغاً
    IF NEW.status = 'accepted' AND (OLD.qr_code IS NULL OR OLD.qr_code = '') THEN
        NEW.qr_code := generate_guest_qr_code(
            NEW.invitation_id,
            NEW.phone,
            NEW.name
        );
    END IF;
    
    -- تحديث وقت التعديل
    NEW.updated_at := now();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث QR code تلقائياً
DROP TRIGGER IF EXISTS trigger_update_guest_qr_on_accept ON guests;
CREATE TRIGGER trigger_update_guest_qr_on_accept
    BEFORE UPDATE ON guests
    FOR EACH ROW
    EXECUTE FUNCTION update_guest_qr_on_accept();

-- إنشاء trigger للإدراج الجديد
CREATE OR REPLACE FUNCTION insert_guest_qr_on_accept()
RETURNS TRIGGER AS $$
BEGIN
    -- إذا كانت الحالة accepted عند الإدراج
    IF NEW.status = 'accepted' THEN
        NEW.qr_code := generate_guest_qr_code(
            NEW.invitation_id,
            NEW.phone,
            NEW.name
        );
    END IF;
    
    -- تحديث الأوقات
    NEW.created_at := COALESCE(NEW.created_at, now());
    NEW.updated_at := now();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_insert_guest_qr_on_accept ON guests;
CREATE TRIGGER trigger_insert_guest_qr_on_accept
    BEFORE INSERT ON guests
    FOR EACH ROW
    EXECUTE FUNCTION insert_guest_qr_on_accept();

-- دالة للبحث عن ضيف بواسطة QR code
CREATE OR REPLACE FUNCTION find_guest_by_qr_code(
    p_qr_code TEXT,
    p_invitation_id UUID
) RETURNS TABLE (
    guest_id UUID,
    guest_name TEXT,
    guest_phone TEXT,
    guest_status TEXT,
    guest_attended BOOLEAN,
    guest_notes TEXT,
    invitation_title TEXT,
    invitation_location TEXT,
    invitation_date TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.id,
        g.name,
        g.phone,
        g.status,
        g.attended,
        g.notes,
        i.title,
        i.location,
        i.event_date
    FROM guests g
    JOIN invitations i ON g.invitation_id = i.id
    WHERE g.qr_code = p_qr_code 
    AND g.invitation_id = p_invitation_id
    AND g.status = 'accepted';
END;
$$ LANGUAGE plpgsql;

-- دالة لتسجيل حضور الضيف
CREATE OR REPLACE FUNCTION mark_guest_attendance(
    p_qr_code TEXT,
    p_invitation_id UUID
) RETURNS JSONB AS $$
DECLARE
    guest_record RECORD;
    result JSONB;
BEGIN
    -- البحث عن الضيف
    SELECT * INTO guest_record
    FROM guests 
    WHERE qr_code = p_qr_code 
    AND invitation_id = p_invitation_id
    AND status = 'accepted';
    
    -- إذا لم يتم العثور على الضيف
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Guest not found or not accepted'
        );
    END IF;
    
    -- إذا كان الضيف قد حضر بالفعل
    IF guest_record.attended = true THEN
        RETURN jsonb_build_object(
            'success', true,
            'already_attended', true,
            'guest', row_to_json(guest_record)
        );
    END IF;
    
    -- تسجيل الحضور
    UPDATE guests 
    SET 
        attended = true,
        attended_at = now(),
        updated_at = now()
    WHERE id = guest_record.id;
    
    -- إرجاع النتيجة
    SELECT * INTO guest_record FROM guests WHERE id = guest_record.id;
    
    RETURN jsonb_build_object(
        'success', true,
        'already_attended', false,
        'guest', row_to_json(guest_record)
    );
END;
$$ LANGUAGE plpgsql;

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_guests_qr_code ON guests(qr_code);
CREATE INDEX IF NOT EXISTS idx_guests_invitation_phone ON guests(invitation_id, phone);
CREATE INDEX IF NOT EXISTS idx_guests_status_attended ON guests(status, attended);

-- تحديث QR codes للضيوف المقبولين الحاليين الذين لا يملكون QR code
UPDATE guests 
SET qr_code = generate_guest_qr_code(invitation_id, phone, name)
WHERE status = 'accepted' 
AND (qr_code IS NULL OR qr_code = '');

-- إضافة تعليقات للجدول
COMMENT ON COLUMN guests.qr_code IS 'QR code مُشفر للضيف لتسهيل دخول المناسبة';
COMMENT ON COLUMN guests.attended_at IS 'وقت تسجيل حضور الضيف';

-- رسالة نجاح
DO $$
BEGIN
    RAISE NOTICE 'تم تحديث نظام QR Code للصفحة العامة بنجاح!';
    RAISE NOTICE 'الميزات المضافة:';
    RAISE NOTICE '- إنشاء QR code تلقائياً عند قبول الدعوة';
    RAISE NOTICE '- دوال البحث والتحقق من QR codes';
    RAISE NOTICE '- تسجيل الحضور التلقائي';
    RAISE NOTICE '- فهارس محسنة للأداء';
END $$;
