'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/Header';
import { supabase } from '@/lib/supabase';
import { FiSend, FiUsers, <PERSON>Check, FiX, FiArrowRight } from 'react-icons/fi';

export default function SendInvitationPage({ params }) {
  const invitationId = params.id;
  const [invitation, setInvitation] = useState(null);
  const [guests, setGuests] = useState([]);
  const [selectedGuests, setSelectedGuests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [user, setUser] = useState(null);
  const router = useRouter();

  useEffect(() => {
    // جلب بيانات المستخدم من التخزين المحلي
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    
    fetchData();
  }, [invitationId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // جلب بيانات الدعوة
      const { data: invitationData, error: invitationError } = await supabase
        .from('invitations')
        .select('*')
        .eq('id', invitationId)
        .single();
      
      if (invitationError) throw invitationError;
      
      setInvitation(invitationData);
      
      // جلب بيانات المدعوين
      let { data: guestsData, error: guestsError } = await supabase
        .from('guests')
        .select('*')
        .eq('invitation_id', invitationId);
      
      if (guestsError) throw guestsError;
      
      setGuests(guestsData || []);
      
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('حدث خطأ أثناء جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAll = () => {
    if (selectedGuests.length === guests.length) {
      setSelectedGuests([]);
    } else {
      setSelectedGuests(guests.map(guest => guest.id));
    }
  };

  const handleSelectGuest = (guestId) => {
    if (selectedGuests.includes(guestId)) {
      setSelectedGuests(selectedGuests.filter(id => id !== guestId));
    } else {
      setSelectedGuests([...selectedGuests, guestId]);
    }
  };

  const handleSendInvitations = async () => {
    if (selectedGuests.length === 0) {
      setError('يرجى اختيار مدعو واحد على الأقل');
      return;
    }
    
    try {
      setSending(true);
      setError('');
      setSuccess('');
      
      // هنا يمكنك استدعاء API لإرسال الدعوات
      const response = await fetch('/api/send-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invitationId,
          attendeeIds: selectedGuests
        }),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ أثناء إرسال الدعوات');
      }
      
      setSuccess(`تم إرسال ${selectedGuests.length} دعوة بنجاح`);
      setSelectedGuests([]);
      
      // تحديث بيانات المدعوين
      await fetchData();
      
    } catch (error) {
      console.error('Error sending invitations:', error);
      setError(error.message || 'حدث خطأ أثناء إرسال الدعوات');
    } finally {
      setSending(false);
    }
  };

  if (loading) {
    return (
      <>
        <Header userName={user?.name} userRole="subscriber" />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header userName={user?.name} userRole="subscriber" />
      <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">إرسال الدعوة</h1>
            <Link
              href={`/subscriber/invitations/${invitationId}`}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg flex items-center"
            >
              <FiArrowRight className="ml-1" /> العودة للدعوة
            </Link>
          </div>
          
          {error && (
            <div className="bg-red-50 border-r-4 border-red-500 text-red-700 p-4 rounded-lg mb-6">
              {error}
            </div>
          )}
          
          {success && (
            <div className="bg-green-50 border-r-4 border-green-500 text-green-700 p-4 rounded-lg mb-6">
              {success}
            </div>
          )}
          
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
            <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
              <h2 className="text-xl font-bold text-gray-800">
                {invitation.title}
              </h2>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-4">
                اختر المدعوين الذين ترغب في إرسال الدعوة لهم
              </p>
              
              <div className="flex justify-between items-center mb-4">
                <button
                  onClick={handleSelectAll}
                  className="bg-indigo-100 hover:bg-indigo-200 text-indigo-700 px-4 py-2 rounded-lg flex items-center transition-colors"
                >
                  <FiCheck className="ml-1" />
                  {selectedGuests.length === guests.length ? 'إلغاء تحديد الكل' : 'تحديد الكل'}
                </button>
                
                <span className="text-gray-600">
                  تم تحديد {selectedGuests.length} من {guests.length} مدعو
                </span>
              </div>
              
              {guests.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <span className="sr-only">تحديد</span>
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الاسم
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          رقم الهاتف
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الحالة
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {guests.map((guest) => (
                        <tr key={guest.id} className={selectedGuests.includes(guest.id) ? 'bg-indigo-50' : ''}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="checkbox"
                              checked={selectedGuests.includes(guest.id)}
                              onChange={() => handleSelectGuest(guest.id)}
                              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {guest.name || "غير محدد"}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">
                              {guest.phone || "غير محدد"}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                              ${
                                guest.status === "accepted"
                                  ? "bg-green-100 text-green-800"
                                  : guest.status === "declined"
                                  ? "bg-red-100 text-red-800"
                                  : guest.status === "sent"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-yellow-100 text-yellow-800"
                              }`}
                            >
                              {guest.status === "accepted"
                                ? "قبول"
                                : guest.status === "declined"
                                ? "اعتذار"
                                : guest.status === "sent"
                                ? "تم الإرسال"
                                : "في الانتظار"}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                    />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    لا يوجد مدعوين
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    قم بإضافة مدعوين أولاً قبل إرسال الدعوات.
                  </p>
                  <div className="mt-6">
                    <Link
                      href={`/subscriber/invitations/${invitationId}/guests/add`}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                    >
                      <svg
                        className="-mr-1 ml-2 h-5 w-5"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      إضافة مدعوين
                    </Link>
                  </div>
                </div>
              )}
              
              {guests.length > 0 && (
                <div className="mt-6 flex justify-end">
                  <button
                    onClick={handleSendInvitations}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-6 rounded-lg flex items-center transition-colors"
                    disabled={sending || selectedGuests.length === 0}
                  >
                    {sending ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white ml-2"></div>
                    ) : (
                      <FiSend className="ml-2" />
                    )}
                    إرسال الدعوات ({selectedGuests.length})
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

