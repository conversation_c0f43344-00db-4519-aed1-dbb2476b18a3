// lib/twilio-templates.js

// قوالب WhatsApp المعتمدة من Twilio
export const WHATSAPP_TEMPLATES = {
  // قالب الدعوة الأساسي
  INVITATION_TEMPLATE: {
    name: "wedding_invitation_template",
    language: "ar", // اللغة العربية
    components: [
      {
        type: "header",
        format: "text",
        text: "دعوة حفل زفاف"
      },
      {
        type: "body",
        text: "عزيزنا الضيف الكريم،\n\nبكل حب ومودة، ندعوك لمشاركتنا فرحتنا في حفل زفاف:\nالعريس: {{1}}\nإلى كريمتنا: {{2}}\n\n📅 التاريخ: {{3}}\n🕒 الساعة: {{4}}\n📍 المكان: {{5}}\n\nحضوركم<|im_start|>سعدنا ويُزيد من بهجة مناسبتنا.\nللتأكيد على حضوركم، يرجى الضغط على الرد التالي:\n\n📞 للاستفسارات، يرجى التواصل معانا عبر المنصة."
      },
      {
        type: "button",
        sub_type: "quick_reply",
        index: 0,
        text: "تأكيد الحضور"
      },
      {
        type: "button",
        sub_type: "quick_reply",
        index: 1,
        text: "اعتذار"
      }
    ]
  },
  
  // قالب تأكيد الحضور
  CONFIRMATION_TEMPLATE: {
    name: "confirmation_template",
    language: "ar",
    components: [
      {
        type: "header",
        format: "text",
        text: "تأكيد الحضور"
      },
      {
        type: "body",
        text: "شكراً لتأكيد حضوركم لحفل زفاف {{1}} و {{2}}.\n\nنتطلع لرؤيتكم يوم {{3}} الساعة {{4}}.\n\nمع خالص تقديرنا وامتناننا لمشاركتكم فرحتنا."
      }
    ]
  },
  
  // قالب الاعتذار عن الحضور
  DECLINE_TEMPLATE: {
    name: "decline_template",
    language: "ar",
    components: [
      {
        type: "header",
        format: "text",
        text: "تأكيد الاعتذار"
      },
      {
        type: "body",
        text: "شكراً لإعلامنا باعتذاركم عن حضور حفل زفاف {{1}} و {{2}}.\n\nنتفهم ظروفكم ونأمل أن نراكم في مناسبة سعيدة أخرى قريباً.\n\nمع خالص تقديرنا."
      }
    ]
  }
};

// دالة لإرسال رسالة باستخدام قالب
export async function sendTemplateMessage(client, to, templateName, variables) {
  try {
    const result = await client.messages.create({
      contentSid: templateName,
      from: 'whatsapp:+14155238886',
      to: `whatsapp:${to}`,
      contentVariables: JSON.stringify(variables)
    });
    
    return { success: true, sid: result.sid };
  } catch (error) {
    console.error('Error sending template message:', error);
    return { success: false, error: error.message };
  }
}