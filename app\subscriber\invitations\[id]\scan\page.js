"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  FiCamera,
  <PERSON>Check,
  FiX,
  FiUser,
  FiRefreshCw,
  FiSearch,
} from "react-icons/fi";
import { Html5Qrcode } from "html5-qrcode";
import { supabase } from "@/lib/supabase";
import Header from "@/components/Header";

export default function ScanPage({ params }) {
  const invitationId = params.id;
  const router = useRouter();
  const scannerRef = useRef(null);

  const [user, setUser] = useState(null);
  const [invitation, setInvitation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [scanning, setScanning] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [scanResult, setScanResult] = useState(null);
  const [guestInfo, setGuestInfo] = useState(null);
  const [manualQrCode, setManualQrCode] = useState("");
  const [manualMode, setManualMode] = useState(false);

  useEffect(() => {
    const fetchUserAndInvitation = async () => {
      try {
        // التحقق من وجود المستخدم في localStorage
        const userData = localStorage.getItem("user");
        if (!userData) {
          console.log("No user found in localStorage, redirecting to login");
          router.push("/login");
          return;
        }

        const parsedUser = JSON.parse(userData);
        console.log("User found in localStorage:", parsedUser);

        if (!parsedUser || parsedUser.role !== "subscriber") {
          console.log("Invalid user role, redirecting to login");
          router.push("/login");
          return;
        }

        setUser(parsedUser);

        // الحصول على بيانات الدعوة
        const { data, error } = await supabase
          .from("invitations")
          .select("*")
          .eq("id", invitationId)
          .single();

        if (error) throw error;
        setInvitation(data);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("حدث خطأ أثناء جلب البيانات");
      } finally {
        setLoading(false);
      }
    };

    fetchUserAndInvitation();

    // تنظيف الماسح عند مغادرة الصفحة
    return () => {
      if (scannerRef.current) {
        scannerRef.current.stop();
        scannerRef.current.clear();
      }
    };
  }, [invitationId, router]);

  const startScanner = async () => {
    if (scannerRef.current) {
      stopScanner();
    }

    setScanning(true);
    setError(null);
    setSuccess(null);
    setScanResult(null);
    setGuestInfo(null);

    try {
      // التحقق من دعم واجهة برمجة التطبيقات MediaDevices
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.log("MediaDevices API not supported");
        setManualMode(true);
        setScanning(false);
        setError(
          "متصفحك لا يدعم الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي أدناه."
        );
        return;
      }

      // طلب إذن الوصول إلى الكاميرا
      try {
        await navigator.mediaDevices.getUserMedia({ video: true });
      } catch (err) {
        console.error("Camera permission error:", err);
        setManualMode(true);
        setScanning(false);

        if (err.name === "NotAllowedError") {
          setError(
            "لم يتم منح إذن الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي أدناه."
          );
        } else if (err.name === "NotFoundError") {
          setError(
            "لم يتم العثور على كاميرا في جهازك. يرجى استخدام الإدخال اليدوي أدناه."
          );
        } else {
          setError(
            "حدث خطأ أثناء الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي أدناه."
          );
        }
        return;
      }

      // تأخير بدء الماسح للتأكد من وجود العنصر في DOM
      setTimeout(() => {
        try {
          const readerElement = document.getElementById("reader");

          if (!readerElement) {
            throw new Error("لم يتم العثور على عنصر الماسح في الصفحة");
          }

          scannerRef.current = new Html5Qrcode("reader");

          // استخدام إعدادات أكثر توافقًا
          scannerRef.current
            .start(
              { facingMode: "environment" }, // استخدام الكاميرا الخلفية
              {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                formatsToSupport: [Html5Qrcode.FORMATS.QR_CODE],
              },
              (decodedText) => {
                handleScanResult(decodedText);
              },
              (errorMessage) => {
                console.log(errorMessage);
              }
            )
            .catch((err) => {
              console.error("Error starting scanner:", err);
              setManualMode(true);
              setScanning(false);
              setError("تعذر تشغيل الماسح. يرجى استخدام الإدخال اليدوي أدناه.");
            });
        } catch (error) {
          console.error("Error initializing scanner:", error);
          setManualMode(true);
          setScanning(false);
          setError(
            "حدث خطأ أثناء تهيئة الماسح. يرجى استخدام الإدخال اليدوي أدناه."
          );
        }
      }, 500);
    } catch (error) {
      console.error("Unexpected error:", error);
      setManualMode(true);
      setScanning(false);
      setError("حدث خطأ غير متوقع. يرجى استخدام الإدخال اليدوي أدناه.");
    }
  };

  const stopScanner = () => {
    if (scannerRef.current) {
      scannerRef.current
        .stop()
        .then(() => {
          scannerRef.current.clear();
          setScanning(false);
        })
        .catch((err) => {
          console.error("Error stopping scanner:", err);
        });
    }
  };

  const handleScanResult = async (qrCode) => {
    try {
      stopScanner();
      setScanResult(qrCode);

      // البحث عن المدعو باستخدام رمز QR
      const { data: guestData, error: guestError } = await supabase
        .from("guests")
        .select("*")
        .eq("qr_code", qrCode)
        .eq("invitation_id", invitationId)
        .single();

      if (guestError || !guestData) {
        // إذا فشل البحث باستخدام qr_code، نحاول البحث باستخدام الهاتف
        // افتراض أن الرمز قد يكون رقم هاتف
        const { data: phoneGuestData, error: phoneGuestError } = await supabase
          .from("guests")
          .select("*")
          .eq("phone", qrCode)
          .eq("invitation_id", invitationId)
          .single();

        if (phoneGuestError || !phoneGuestData) {
          throw new Error("رمز QR غير صالح أو لا ينتمي لهذه الدعوة");
        }

        setGuestInfo(phoneGuestData);

        // إذا كان المدعو قد حضر بالفعل
        if (phoneGuestData.attended) {
          setSuccess(
            `تم تسجيل حضور هذا المدعو مسبقًا (${phoneGuestData.phone})`
          );
          return;
        }

        // تحديث حالة الحضور
        const { error: updateError } = await supabase
          .from("guests")
          .update({ attended: true, attended_at: new Date().toISOString() })
          .eq("id", phoneGuestData.id);

        if (updateError) throw updateError;

        setSuccess(`تم تسجيل حضور المدعو بنجاح (${phoneGuestData.phone})`);

        // تحديث بيانات المدعو
        setGuestInfo({ ...phoneGuestData, attended: true });
      } else {
        setGuestInfo(guestData);

        // إذا كان المدعو قد حضر بالفعل
        if (guestData.attended) {
          setSuccess(`تم تسجيل حضور هذا المدعو مسبقًا (${guestData.phone})`);
          return;
        }

        // تحديث حالة الحضور
        const { error: updateError } = await supabase
          .from("guests")
          .update({ attended: true, attended_at: new Date().toISOString() })
          .eq("id", guestData.id);

        if (updateError) throw updateError;

        setSuccess(`تم تسجيل حضور المدعو بنجاح (${guestData.phone})`);

        // تحديث بيانات المدعو
        setGuestInfo({ ...guestData, attended: true });
      }
    } catch (error) {
      console.error("Error processing scan result:", error);
      setError(error.message);
    }
  };

  const handleManualSubmit = async (e) => {
    e.preventDefault();

    if (!manualQrCode.trim()) {
      setError("الرجاء إدخال رمز QR أو رقم الهاتف");
      return;
    }

    try {
      setScanResult(manualQrCode);
      setError(null);
      setSuccess(null);
      setGuestInfo(null);

      // البحث عن المدعو باستخدام رمز QR
      const { data: guestData, error: guestError } = await supabase
        .from("guests")
        .select("*")
        .eq("qr_code", manualQrCode)
        .eq("invitation_id", invitationId)
        .single();

      if (guestError || !guestData) {
        // إذا فشل البحث باستخدام qr_code، نحاول البحث باستخدام الهاتف
        // افتراض أن الرمز قد يكون رقم هاتف
        const { data: phoneGuestData, error: phoneGuestError } = await supabase
          .from("guests")
          .select("*")
          .eq("phone", manualQrCode)
          .eq("invitation_id", invitationId)
          .single();

        if (phoneGuestError || !phoneGuestData) {
          throw new Error(
            "رمز QR أو رقم الهاتف غير صالح أو لا ينتمي لهذه الدعوة"
          );
        }

        setGuestInfo(phoneGuestData);

        // إذا كان المدعو قد حضر بالفعل
        if (phoneGuestData.attended) {
          setSuccess(
            `تم تسجيل حضور هذا المدعو مسبقًا (${phoneGuestData.phone})`
          );
          return;
        }

        // تحديث حالة الحضور
        const { error: updateError } = await supabase
          .from("guests")
          .update({ attended: true, attended_at: new Date().toISOString() })
          .eq("id", phoneGuestData.id);

        if (updateError) throw updateError;

        setSuccess(`تم تسجيل حضور المدعو بنجاح (${phoneGuestData.phone})`);

        // تحديث بيانات المدعو
        setGuestInfo({ ...phoneGuestData, attended: true });
      } else {
        setGuestInfo(guestData);

        // إذا كان المدعو قد حضر بالفعل
        if (guestData.attended) {
          setSuccess(`تم تسجيل حضور هذا المدعو مسبقًا (${guestData.phone})`);
          return;
        }

        // تحديث حالة الحضور
        const { error: updateError } = await supabase
          .from("guests")
          .update({ attended: true, attended_at: new Date().toISOString() })
          .eq("id", guestData.id);

        if (updateError) throw updateError;

        setSuccess(`تم تسجيل حضور المدعو بنجاح (${guestData.phone})`);

        // تحديث بيانات المدعو
        setGuestInfo({ ...guestData, attended: true });
      }

      // إعادة تعيين حقل الإدخال اليدوي
      setManualQrCode("");
    } catch (error) {
      console.error("Error processing manual input:", error);
      setError(error.message);
    }
  };

  if (loading) {
    return (
      <>
        <Header userName={user?.name} userRole="subscriber" />
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
          <div className="bg-white shadow-md rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header userName={user?.name} userRole="subscriber" />
      <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <div className="p-4 sm:p-6 bg-gradient-to-r from-purple-600 to-indigo-600 text-white">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold mb-2">مسح QR Code للحضور</h1>
                <p className="text-purple-100">للدعوة: {invitation?.title}</p>
              </div>
              <Link
                href={`/subscriber/invitations/${invitationId}`}
                className="bg-white text-purple-700 hover:bg-purple-100 font-bold py-2 px-4 rounded transition-colors duration-200"
              >
                العودة للدعوة
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-4 sm:p-6">
            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              <h2 className="text-xl font-bold mb-4 text-purple-700 flex items-center">
                <FiCamera className="ml-2" />
                ماسح QR Code
              </h2>

              {error && (
                <div className="bg-red-100 border-r-4 border-red-500 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
                  <FiX className="ml-2 text-red-500" />
                  {error}
                </div>
              )}

              {success && (
                <div className="bg-green-100 border-r-4 border-green-500 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
                  <FiCheck className="ml-2 text-green-500" />
                  {success}
                </div>
              )}

              <div className="mb-6">
                {scanning ? (
                  <div className="relative">
                    <div
                      id="reader"
                      className="w-full h-64 rounded-lg overflow-hidden border-2 border-purple-300"
                    ></div>
                    <button
                      onClick={stopScanner}
                      className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-full shadow-md transition-colors duration-200 flex items-center"
                    >
                      <FiX className="ml-1" />
                      إيقاف
                    </button>
                  </div>
                ) : (
                  <div className="text-center p-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <FiCamera className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="mb-4 text-gray-600">
                      اضغط على زر "بدء المسح" لتشغيل الكاميرا ومسح رمز QR
                    </p>
                    <button
                      onClick={startScanner}
                      className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition-colors duration-200 w-full flex items-center justify-center"
                    >
                      <FiCamera className="ml-2" />
                      بدء المسح
                    </button>
                  </div>
                )}
              </div>

              {/* إدخال يدوي للرمز */}
              <div className="mt-6">
                <h3 className="text-lg font-bold mb-3 text-gray-700">
                  أو أدخل رمز QR / رقم الهاتف يدويًا
                </h3>
                <form
                  onSubmit={handleManualSubmit}
                  className="flex flex-col space-y-4"
                >
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <FiSearch className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={manualQrCode}
                      onChange={(e) => setManualQrCode(e.target.value)}
                      placeholder="أدخل رمز QR أو رقم الهاتف"
                      className="block w-full pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                  <button
                    type="submit"
                    className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition-colors duration-200 w-full flex items-center justify-center"
                  >
                    <FiSearch className="ml-2" />
                    بحث
                  </button>
                </form>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              <h2 className="text-xl font-bold mb-4 text-purple-700 flex items-center">
                <FiUser className="ml-2" />
                معلومات المدعو
              </h2>

              {guestInfo ? (
                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-500 mb-1">الاسم</p>
                    <p className="font-medium text-lg">
                      {guestInfo.name || "غير محدد"}
                    </p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-500 mb-1">رقم الهاتف</p>
                    <p className="font-medium text-lg">{guestInfo.phone}</p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-500 mb-1">حالة الحضور</p>
                    <p className="font-medium text-lg">
                      {guestInfo.attended ? (
                        <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                          حضر
                        </span>
                      ) : (
                        <span className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">
                          لم يحضر
                        </span>
                      )}
                    </p>
                  </div>

                  {scanResult && (
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <p className="text-gray-500 text-sm mb-1">رمز QR</p>
                      <p className="font-mono text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                        {scanResult}
                      </p>
                    </div>
                  )}

                  <div className="mt-6">
                    <button
                      onClick={() => {
                        setScanResult(null);
                        setGuestInfo(null);
                        setError("");
                        setSuccess("");
                      }}
                      className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition-colors duration-200 w-full flex items-center justify-center"
                    >
                      <FiRefreshCw className="ml-2" />
                      مسح بيانات المدعو
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center p-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <FiUser className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">
                    قم بمسح رمز QR لعرض معلومات المدعو
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
