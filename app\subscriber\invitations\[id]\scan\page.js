"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  FiCamera,
  FiCheck,
  FiX,
  FiUser,
  FiRefreshCw,
  FiSearch,
  FiUsers,
  FiUserCheck,
  FiClock,
  FiPhone,
  FiHash,
  FiArrowRight,
} from "react-icons/fi";
import { Html5Qrcode } from "html5-qrcode";
import { supabase } from "@/lib/supabase";
import Header from "@/components/Header";

export default function ScanPage({ params }) {
  const invitationId = params.id;
  const router = useRouter();
  const scannerRef = useRef(null);

  const [user, setUser] = useState(null);
  const [invitation, setInvitation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [scanning, setScanning] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [scanResult, setScanResult] = useState(null);
  const [guestInfo, setGuestInfo] = useState(null);
  const [manualQrCode, setManualQrCode] = useState("");
  const [manualMode, setManualMode] = useState(false);

  useEffect(() => {
    const fetchUserAndInvitation = async () => {
      try {
        // التحقق من وجود المستخدم في localStorage
        const userData = localStorage.getItem("user");
        if (!userData) {
          console.log("No user found in localStorage, redirecting to login");
          router.push("/login");
          return;
        }

        const parsedUser = JSON.parse(userData);
        console.log("User found in localStorage:", parsedUser);

        if (!parsedUser || parsedUser.role !== "subscriber") {
          console.log("Invalid user role, redirecting to login");
          router.push("/login");
          return;
        }

        setUser(parsedUser);

        // الحصول على بيانات الدعوة
        const { data, error } = await supabase
          .from("invitations")
          .select("*")
          .eq("id", invitationId)
          .single();

        if (error) throw error;
        setInvitation(data);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("حدث خطأ أثناء جلب البيانات");
      } finally {
        setLoading(false);
      }
    };

    fetchUserAndInvitation();

    // تنظيف الماسح عند مغادرة الصفحة
    return () => {
      if (scannerRef.current) {
        scannerRef.current.stop();
        scannerRef.current.clear();
      }
    };
  }, [invitationId, router]);

  const startScanner = async () => {
    if (scannerRef.current) {
      stopScanner();
    }

    setScanning(true);
    setError(null);
    setSuccess(null);
    setScanResult(null);
    setGuestInfo(null);

    try {
      // التحقق من دعم واجهة برمجة التطبيقات MediaDevices
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.log("MediaDevices API not supported");
        setManualMode(true);
        setScanning(false);
        setError(
          "متصفحك لا يدعم الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي أدناه."
        );
        return;
      }

      // طلب إذن الوصول إلى الكاميرا
      try {
        await navigator.mediaDevices.getUserMedia({ video: true });
      } catch (err) {
        console.error("Camera permission error:", err);
        setManualMode(true);
        setScanning(false);

        if (err.name === "NotAllowedError") {
          setError(
            "لم يتم منح إذن الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي أدناه."
          );
        } else if (err.name === "NotFoundError") {
          setError(
            "لم يتم العثور على كاميرا في جهازك. يرجى استخدام الإدخال اليدوي أدناه."
          );
        } else {
          setError(
            "حدث خطأ أثناء الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي أدناه."
          );
        }
        return;
      }

      // تأخير بدء الماسح للتأكد من وجود العنصر في DOM
      setTimeout(() => {
        try {
          const readerElement = document.getElementById("reader");

          if (!readerElement) {
            throw new Error("لم يتم العثور على عنصر الماسح في الصفحة");
          }

          scannerRef.current = new Html5Qrcode("reader");

          // استخدام إعدادات أكثر توافقًا
          scannerRef.current
            .start(
              { facingMode: "environment" }, // استخدام الكاميرا الخلفية
              {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                formatsToSupport: [Html5Qrcode.FORMATS.QR_CODE],
              },
              (decodedText) => {
                handleScanResult(decodedText);
              },
              (errorMessage) => {
                console.log(errorMessage);
              }
            )
            .catch((err) => {
              console.error("Error starting scanner:", err);
              setManualMode(true);
              setScanning(false);
              setError("تعذر تشغيل الماسح. يرجى استخدام الإدخال اليدوي أدناه.");
            });
        } catch (error) {
          console.error("Error initializing scanner:", error);
          setManualMode(true);
          setScanning(false);
          setError(
            "حدث خطأ أثناء تهيئة الماسح. يرجى استخدام الإدخال اليدوي أدناه."
          );
        }
      }, 500);
    } catch (error) {
      console.error("Unexpected error:", error);
      setManualMode(true);
      setScanning(false);
      setError("حدث خطأ غير متوقع. يرجى استخدام الإدخال اليدوي أدناه.");
    }
  };

  const stopScanner = () => {
    if (scannerRef.current) {
      scannerRef.current
        .stop()
        .then(() => {
          scannerRef.current.clear();
          setScanning(false);
        })
        .catch((err) => {
          console.error("Error stopping scanner:", err);
        });
    }
  };

  const handleScanResult = async (qrCode) => {
    try {
      stopScanner();
      setScanResult(qrCode);

      // البحث عن المدعو باستخدام رمز QR
      const { data: guestData, error: guestError } = await supabase
        .from("guests")
        .select("*")
        .eq("qr_code", qrCode)
        .eq("invitation_id", invitationId)
        .single();

      if (guestError || !guestData) {
        // إذا فشل البحث باستخدام qr_code، نحاول البحث باستخدام الهاتف
        // افتراض أن الرمز قد يكون رقم هاتف
        const { data: phoneGuestData, error: phoneGuestError } = await supabase
          .from("guests")
          .select("*")
          .eq("phone", qrCode)
          .eq("invitation_id", invitationId)
          .single();

        if (phoneGuestError || !phoneGuestData) {
          throw new Error("رمز QR غير صالح أو لا ينتمي لهذه الدعوة");
        }

        setGuestInfo(phoneGuestData);

        // إذا كان المدعو قد حضر بالفعل
        if (phoneGuestData.attended) {
          setSuccess(
            `تم تسجيل حضور هذا المدعو مسبقًا (${phoneGuestData.phone})`
          );
          return;
        }

        // تحديث حالة الحضور
        const { error: updateError } = await supabase
          .from("guests")
          .update({ attended: true, attended_at: new Date().toISOString() })
          .eq("id", phoneGuestData.id);

        if (updateError) throw updateError;

        setSuccess(`تم تسجيل حضور المدعو بنجاح (${phoneGuestData.phone})`);

        // تحديث بيانات المدعو
        setGuestInfo({ ...phoneGuestData, attended: true });
      } else {
        setGuestInfo(guestData);

        // إذا كان المدعو قد حضر بالفعل
        if (guestData.attended) {
          setSuccess(`تم تسجيل حضور هذا المدعو مسبقًا (${guestData.phone})`);
          return;
        }

        // تحديث حالة الحضور
        const { error: updateError } = await supabase
          .from("guests")
          .update({ attended: true, attended_at: new Date().toISOString() })
          .eq("id", guestData.id);

        if (updateError) throw updateError;

        setSuccess(`تم تسجيل حضور المدعو بنجاح (${guestData.phone})`);

        // تحديث بيانات المدعو
        setGuestInfo({ ...guestData, attended: true });
      }
    } catch (error) {
      console.error("Error processing scan result:", error);
      setError(error.message);
    }
  };

  const handleManualSubmit = async (e) => {
    e.preventDefault();

    if (!manualQrCode.trim()) {
      setError("الرجاء إدخال رمز QR أو رقم الهاتف");
      return;
    }

    try {
      setScanResult(manualQrCode);
      setError(null);
      setSuccess(null);
      setGuestInfo(null);

      // البحث عن المدعو باستخدام رمز QR
      const { data: guestData, error: guestError } = await supabase
        .from("guests")
        .select("*")
        .eq("qr_code", manualQrCode)
        .eq("invitation_id", invitationId)
        .single();

      if (guestError || !guestData) {
        // إذا فشل البحث باستخدام qr_code، نحاول البحث باستخدام الهاتف
        // افتراض أن الرمز قد يكون رقم هاتف
        const { data: phoneGuestData, error: phoneGuestError } = await supabase
          .from("guests")
          .select("*")
          .eq("phone", manualQrCode)
          .eq("invitation_id", invitationId)
          .single();

        if (phoneGuestError || !phoneGuestData) {
          throw new Error(
            "رمز QR أو رقم الهاتف غير صالح أو لا ينتمي لهذه الدعوة"
          );
        }

        setGuestInfo(phoneGuestData);

        // إذا كان المدعو قد حضر بالفعل
        if (phoneGuestData.attended) {
          setSuccess(
            `تم تسجيل حضور هذا المدعو مسبقًا (${phoneGuestData.phone})`
          );
          return;
        }

        // تحديث حالة الحضور
        const { error: updateError } = await supabase
          .from("guests")
          .update({ attended: true, attended_at: new Date().toISOString() })
          .eq("id", phoneGuestData.id);

        if (updateError) throw updateError;

        setSuccess(`تم تسجيل حضور المدعو بنجاح (${phoneGuestData.phone})`);

        // تحديث بيانات المدعو
        setGuestInfo({ ...phoneGuestData, attended: true });
      } else {
        setGuestInfo(guestData);

        // إذا كان المدعو قد حضر بالفعل
        if (guestData.attended) {
          setSuccess(`تم تسجيل حضور هذا المدعو مسبقًا (${guestData.phone})`);
          return;
        }

        // تحديث حالة الحضور
        const { error: updateError } = await supabase
          .from("guests")
          .update({ attended: true, attended_at: new Date().toISOString() })
          .eq("id", guestData.id);

        if (updateError) throw updateError;

        setSuccess(`تم تسجيل حضور المدعو بنجاح (${guestData.phone})`);

        // تحديث بيانات المدعو
        setGuestInfo({ ...guestData, attended: true });
      }

      // إعادة تعيين حقل الإدخال اليدوي
      setManualQrCode("");
    } catch (error) {
      console.error("Error processing manual input:", error);
      setError(error.message);
    }
  };

  if (loading) {
    return (
      <>
        <Header userName={user?.name} userRole="subscriber" />
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
          <div className="bg-white shadow-md rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header userName={user?.name} userRole="subscriber" />
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
          {/* Header Section */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden mb-8">
            <div className="p-6 bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 text-white relative overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="relative z-10">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h1 className="text-3xl font-bold mb-2 flex items-center">
                      <div className="bg-white/20 p-2 rounded-lg ml-3">
                        <FiCamera className="text-white text-2xl" />
                      </div>
                      مسح QR Code للحضور
                    </h1>
                    <p className="text-purple-100 text-lg">
                      للدعوة: {invitation?.title}
                    </p>
                  </div>
                  <Link
                    href={`/subscriber/invitations/${invitationId}`}
                    className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200 flex items-center"
                  >
                    <FiArrowRight className="ml-2" />
                    العودة للدعوة
                  </Link>
                </div>

                {/* إحصائيات سريعة */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                  {[
                    {
                      icon: FiUsers,
                      value: stats.totalGuests,
                      label: "إجمالي المدعوين",
                      delay: 0.1,
                    },
                    {
                      icon: FiCheck,
                      value: stats.acceptedGuests,
                      label: "قبلوا الدعوة",
                      delay: 0.2,
                    },
                    {
                      icon: FiUserCheck,
                      value: stats.attendedGuests,
                      label: "حضروا الفعلية",
                      delay: 0.3,
                    },
                    {
                      icon: FiClock,
                      value: stats.pendingGuests,
                      label: "في الانتظار",
                      delay: 0.4,
                    },
                  ].map((stat, index) => (
                    <div
                      key={index}
                      className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center hover:bg-white/30 transition-all duration-200"
                    >
                      <stat.icon className="mx-auto text-2xl mb-2" />
                      <div className="text-2xl font-bold">{stat.value}</div>
                      <div className="text-sm text-purple-100">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* قسم المسح */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                <h2 className="text-2xl font-bold mb-6 text-gray-800 flex items-center">
                  <div className="bg-gradient-to-r from-purple-500 to-indigo-500 p-2 rounded-lg ml-2">
                    <FiCamera className="text-white text-xl" />
                  </div>
                  ماسح QR Code
                </h2>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl mb-4 flex items-center">
                  <FiX className="ml-2 text-red-500 flex-shrink-0" />
                  <span>{error}</span>
                </div>
              )}

              {success && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl mb-4 flex items-center">
                  <div className="bg-green-100 p-1 rounded-full ml-2">
                    <FiCheck className="text-green-500 flex-shrink-0" />
                  </div>
                  <span>{success}</span>
                </div>
              )}

              <div className="mb-6">
                {scanning ? (
                  <div className="relative">
                    <div
                      id="reader"
                      className="w-full h-80 rounded-2xl overflow-hidden border-4 border-purple-300 shadow-lg"
                    ></div>
                    <button
                      onClick={stopScanner}
                      className="absolute top-4 right-4 bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-full shadow-lg transition-colors duration-200 flex items-center backdrop-blur-sm"
                    >
                      <FiX className="ml-1" />
                      إيقاف
                    </button>
                    {isProcessing && (
                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-2xl">
                        <div className="bg-white rounded-xl p-4 flex items-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 ml-3"></div>
                          <span className="text-gray-700">
                            جاري المعالجة...
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center p-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border-2 border-dashed border-gray-300">
                    <FiCamera className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                    <h3 className="text-xl font-bold text-gray-700 mb-2">
                      ابدأ مسح QR Code
                    </h3>
                    <p className="mb-6 text-gray-600">
                      اضغط على زر "بدء المسح" لتشغيل الكاميرا ومسح رمز QR
                      للضيوف
                    </p>
                    <button
                      onClick={startScanner}
                      className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-8 rounded-xl shadow-lg transition-all duration-200 w-full flex items-center justify-center"
                    >
                      <FiCamera className="ml-2 text-xl" />
                      بدء المسح
                    </button>
                  </div>
                )}
              </div>

              {/* إدخال يدوي للرمز */}
              <div className="mt-8 p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-200">
                <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
                  <div className="bg-gradient-to-r from-blue-500 to-indigo-500 p-2 rounded-lg ml-2">
                    <FiSearch className="text-white" />
                  </div>
                  أو أدخل رمز QR / رقم الهاتف يدويًا
                </h3>
                <form
                  onSubmit={handleManualSubmit}
                  className="flex flex-col space-y-4"
                >
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <FiSearch className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={manualQrCode}
                      onChange={(e) => setManualQrCode(e.target.value)}
                      placeholder="أدخل رمز QR أو رقم الهاتف"
                      className="block w-full pr-10 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-lg"
                      disabled={isProcessing}
                    />
                  </div>
                  <button
                    type="submit"
                    disabled={isProcessing}
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 px-6 rounded-xl shadow-lg transition-all duration-200 w-full flex items-center justify-center"
                  >
                    {isProcessing ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                        جاري البحث...
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <FiSearch className="ml-2" />
                        بحث
                      </div>
                    )}
                  </button>
                </form>
              </div>
            </div>

            {/* قسم المعلومات والإحصائيات */}
            <div className="space-y-6">
              {/* معلومات المدعو */}
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                <h2 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
                  <div className="bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg ml-2">
                    <FiUser className="text-white" />
                  </div>
                  معلومات المدعو
                </h2>

              {guestInfo ? (
                <div className="space-y-4">
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-green-600 mb-1 font-medium">الاسم</p>
                        <p className="font-bold text-xl text-gray-800">
                          {guestInfo.name || "غير محدد"}
                        </p>
                      </div>
                      <div className="bg-green-100 p-2 rounded-full">
                        <FiUser className="text-green-600 text-xl" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-blue-600 mb-1 font-medium">رقم الهاتف</p>
                        <p className="font-bold text-xl text-gray-800">{guestInfo.phone}</p>
                      </div>
                      <div className="bg-blue-100 p-2 rounded-full">
                        <FiPhone className="text-blue-600 text-xl" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-purple-600 mb-1 font-medium">حالة الحضور</p>
                        <span className={`px-4 py-2 rounded-full text-sm font-bold ${
                          guestInfo.attended
                            ? "bg-green-100 text-green-800"
                            : "bg-orange-100 text-orange-800"
                        }`}>
                          {guestInfo.attended ? "✅ حضر" : "⏳ لم يحضر بعد"}
                        </span>
                      </div>
                      <div className={`p-2 rounded-full ${
                        guestInfo.attended ? "bg-green-100" : "bg-orange-100"
                      }`}>
                        {guestInfo.attended ? (
                          <FiCheck className="text-green-600 text-xl" />
                        ) : (
                          <FiClock className="text-orange-600 text-xl" />
                        )}
                      </div>
                    </div>
                  </div>

                  {scanResult && (
                    <div className="mt-4 p-4 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl border border-gray-200">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-gray-600 text-sm font-medium">رمز QR المستخدم</p>
                        <div className="bg-gray-100 p-1 rounded-full">
                          <FiHash className="text-gray-600 text-sm" />
                        </div>
                      </div>
                      <p className="font-mono text-sm bg-white p-3 rounded-lg border overflow-x-auto text-gray-700">
                        {scanResult}
                      </p>
                    </div>
                  )}

                  <div className="mt-6">
                    <button
                      onClick={() => {
                        setScanResult(null);
                        setGuestInfo(null);
                        setError("");
                        setSuccess("");
                      }}
                      className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-xl shadow-lg transition-all duration-200 w-full flex items-center justify-center"
                    >
                      <FiRefreshCw className="ml-2" />
                      مسح بيانات المدعو
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FiUser className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                  <p>لم يتم مسح أي رمز بعد</p>
                  <p className="text-sm">
                    ابدأ بمسح QR Code لعرض معلومات المدعو
                  </p>
                </div>
              )}

              {/* المسح الأخير */}
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                <h2 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
                  <div className="bg-gradient-to-r from-orange-500 to-red-500 p-2 rounded-lg ml-2">
                    <FiClock className="text-white" />
                  </div>
                  آخر المسح
                </h2>

                {recentScans.length > 0 ? (
                  <div className="space-y-3">
                    {recentScans.map((guest, index) => (
                      <div
                        key={guest.id}
                        className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200 flex items-center justify-between"
                      >
                        <div>
                          <p className="font-bold text-gray-800">{guest.name}</p>
                          <p className="text-sm text-gray-600">{guest.phone}</p>
                        </div>
                        <div className="bg-green-100 p-2 rounded-full">
                          <FiCheck className="text-green-500 text-xl" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <FiClock className="mx-auto h-8 w-8 text-gray-300 mb-2" />
                    <p className="text-sm">لا توجد عمليات مسح حديثة</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
