"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  FiCamera,
  FiCheck,
  FiX,
  FiUser,
  FiRefreshCw,
  FiSearch,
  FiUsers,
  FiClock,
  FiScan,
  FiUserCheck,
  FiBarChart3,
} from "react-icons/fi";
import { supabase } from "@/lib/supabase";
import Header from "@/components/Header";

export default function ScanPage({ params }) {
  const invitationId = params.id;
  const router = useRouter();
  const scannerRef = useRef(null);

  const [user, setUser] = useState(null);
  const [invitation, setInvitation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [scanning, setScanning] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [guestInfo, setGuestInfo] = useState(null);
  const [manualQrCode, setManualQrCode] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [recentScans, setRecentScans] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    accepted: 0,
    attended: 0,
  });

  // جلب بيانات المستخدم والدعوة
  useEffect(() => {
    const fetchData = async () => {
      try {
        // جلب بيانات المستخدم
        const userData = localStorage.getItem("user");
        if (userData) {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);

          // جلب بيانات الدعوة
          const { data: invitationData, error: invitationError } =
            await supabase
              .from("invitations")
              .select("*")
              .eq("id", invitationId)
              .eq("user_id", parsedUser.id)
              .single();

          if (invitationError) {
            console.error("Error fetching invitation:", invitationError);
            setError("خطأ في جلب بيانات الدعوة");
            return;
          }

          setInvitation(invitationData);
          await fetchStats();
        } else {
          router.push("/login");
        }
      } catch (error) {
        console.error("Error in fetchData:", error);
        setError("خطأ في جلب البيانات");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [invitationId, router]);

  // جلب الإحصائيات
  const fetchStats = async () => {
    try {
      const { data: guests, error } = await supabase
        .from("guests")
        .select("status, attended")
        .eq("invitation_id", invitationId);

      if (error) {
        console.error("Error fetching stats:", error);
        return;
      }

      const total = guests.length;
      const accepted = guests.filter((g) => g.status === "accepted").length;
      const attended = guests.filter((g) => g.attended === true).length;

      setStats({ total, accepted, attended });
    } catch (error) {
      console.error("Error in fetchStats:", error);
    }
  };

  // بدء المسح
  const startScanner = async () => {
    try {
      setScanning(true);
      setError("");

      // استيراد Html5Qrcode ديناميكياً لتجنب مشاكل SSR
      const { Html5Qrcode } = await import("html5-qrcode");

      const html5QrCode = new Html5Qrcode("reader");
      scannerRef.current = html5QrCode;

      const config = {
        fps: 10,
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1.0,
      };

      await html5QrCode.start(
        { facingMode: "environment" },
        config,
        async (decodedText) => {
          console.log("QR Code detected:", decodedText);
          await handleScanResult(decodedText);
        },
        (errorMessage) => {
          // تجاهل أخطاء المسح العادية
        }
      );
    } catch (error) {
      console.error("Error starting scanner:", error);
      setError("خطأ في تشغيل الماسح. تأكد من السماح بالوصول للكاميرا.");
      setScanning(false);
    }
  };

  // إيقاف المسح
  const stopScanner = async () => {
    try {
      if (scannerRef.current) {
        await scannerRef.current.stop();
        scannerRef.current = null;
      }
    } catch (error) {
      console.error("Error stopping scanner:", error);
    } finally {
      setScanning(false);
    }
  };

  // معالجة نتيجة المسح
  const handleScanResult = async (qrCode) => {
    if (isProcessing) return;

    setIsProcessing(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch("/api/scan-qr", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          qrCode: qrCode,
          invitationId: invitationId,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(`تم تسجيل حضور ${data.guest.name} بنجاح! 🎉`);
        setGuestInfo(data.guest);

        // إضافة للمسح الأخير
        setRecentScans((prev) => [data.guest, ...prev.slice(0, 4)]);

        // تحديث الإحصائيات
        await fetchStats();

        // تأثيرات النجاح
        if (typeof window !== "undefined") {
          const confetti = (await import("canvas-confetti")).default;
          confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 },
            colors: ["#10B981", "#059669", "#047857"],
          });
        }

        // إيقاف المسح بعد النجاح
        await stopScanner();
      } else {
        setError(data.error || "خطأ في معالجة الرمز");
      }
    } catch (error) {
      console.error("Error processing QR code:", error);
      setError("خطأ في الاتصال بالخادم");
    } finally {
      setIsProcessing(false);
    }
  };

  // إرسال الرمز يدوياً
  const handleManualSubmit = async (e) => {
    e.preventDefault();
    if (!manualQrCode.trim()) return;

    await handleScanResult(manualQrCode.trim());
    setManualQrCode("");
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Header userName={user?.name} userRole="subscriber" />
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
          {/* Header Section */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden mb-8">
            <div className="p-6 bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 text-white relative overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="relative z-10">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h1 className="text-3xl font-bold mb-2 flex items-center">
                      <FiScan className="ml-3 text-4xl" />
                      مسح QR Code للحضور
                    </h1>
                    <p className="text-purple-100 text-lg">
                      للدعوة: {invitation?.title}
                    </p>
                  </div>
                  <Link
                    href={`/subscriber/invitations/${invitationId}`}
                    className="bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 font-bold py-3 px-6 rounded-xl transition-all duration-200 flex items-center"
                  >
                    <FiRefreshCw className="ml-2" />
                    العودة للدعوة
                  </Link>
                </div>

                {/* إحصائيات سريعة */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                  <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center">
                    <FiUsers className="mx-auto text-2xl mb-2" />
                    <div className="text-2xl font-bold">{stats.total}</div>
                    <div className="text-sm text-purple-100">
                      إجمالي المدعوين
                    </div>
                  </div>
                  <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center">
                    <FiUserCheck className="mx-auto text-2xl mb-2" />
                    <div className="text-2xl font-bold">{stats.accepted}</div>
                    <div className="text-sm text-purple-100">قبلوا الدعوة</div>
                  </div>
                  <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center">
                    <FiCheck className="mx-auto text-2xl mb-2" />
                    <div className="text-2xl font-bold">{stats.attended}</div>
                    <div className="text-sm text-purple-100">حضروا الفعلية</div>
                  </div>
                  <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center">
                    <FiBarChart3 className="mx-auto text-2xl mb-2" />
                    <div className="text-2xl font-bold">
                      {stats.accepted > 0
                        ? Math.round((stats.attended / stats.accepted) * 100)
                        : 0}
                      %
                    </div>
                    <div className="text-sm text-purple-100">معدل الحضور</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* قسم المسح */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                <h2 className="text-2xl font-bold mb-6 text-gray-800 flex items-center">
                  <div className="bg-gradient-to-r from-purple-500 to-indigo-500 p-3 rounded-xl ml-3">
                    <FiCamera className="text-white text-xl" />
                  </div>
                  ماسح QR Code
                </h2>

                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl mb-4 flex items-center">
                    <FiX className="ml-2 text-red-500 flex-shrink-0" />
                    <span>{error}</span>
                  </div>
                )}

                {success && (
                  <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl mb-4 flex items-center">
                    <FiCheck className="ml-2 text-green-500 flex-shrink-0" />
                    <span>{success}</span>
                  </div>
                )}

                <div className="mb-6">
                  {scanning ? (
                    <div className="relative">
                      <div
                        id="reader"
                        className="w-full h-80 rounded-2xl overflow-hidden border-4 border-purple-300 shadow-lg"
                      ></div>
                      <button
                        onClick={stopScanner}
                        className="absolute top-4 right-4 bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-full shadow-lg transition-colors duration-200 flex items-center backdrop-blur-sm"
                      >
                        <FiX className="ml-1" />
                        إيقاف
                      </button>
                      {isProcessing && (
                        <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-2xl">
                          <div className="bg-white rounded-xl p-4 flex items-center">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 ml-3"></div>
                            <span className="text-gray-700">
                              جاري المعالجة...
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center p-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border-2 border-dashed border-gray-300">
                      <FiCamera className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                      <h3 className="text-xl font-bold text-gray-700 mb-2">
                        ابدأ مسح QR Code
                      </h3>
                      <p className="mb-6 text-gray-600">
                        اضغط على زر "بدء المسح" لتشغيل الكاميرا ومسح رمز QR
                        للضيوف
                      </p>
                      <button
                        onClick={startScanner}
                        className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-8 rounded-xl shadow-lg transition-all duration-200 w-full flex items-center justify-center"
                      >
                        <FiCamera className="ml-2 text-xl" />
                        بدء المسح
                      </button>
                    </div>
                  )}
                </div>

                {/* إدخال يدوي للرمز */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-bold mb-4 text-gray-700 flex items-center">
                    <FiSearch className="ml-2 text-purple-600" />
                    أو أدخل رمز QR / رقم الهاتف يدويًا
                  </h3>
                  <form onSubmit={handleManualSubmit} className="space-y-4">
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <FiSearch className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        value={manualQrCode}
                        onChange={(e) => setManualQrCode(e.target.value)}
                        placeholder="أدخل رمز QR أو رقم الهاتف"
                        className="block w-full pr-10 py-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-lg"
                        disabled={isProcessing}
                      />
                    </div>
                    <button
                      type="submit"
                      disabled={isProcessing}
                      className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 disabled:opacity-50 text-white font-bold py-4 px-6 rounded-xl shadow-lg transition-all duration-200 w-full flex items-center justify-center"
                    >
                      {isProcessing ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                          جاري البحث...
                        </>
                      ) : (
                        <>
                          <FiSearch className="ml-2" />
                          بحث
                        </>
                      )}
                    </button>
                  </form>
                </div>
              </div>
            </div>

            {/* قسم المعلومات والإحصائيات */}
            <div className="space-y-6">
              {/* معلومات المدعو */}
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                <h2 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
                  <div className="bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg ml-2">
                    <FiUser className="text-white" />
                  </div>
                  معلومات المدعو
                </h2>

                {guestInfo ? (
                  <div className="space-y-4">
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-bold text-green-800 text-lg">
                          {guestInfo.name}
                        </h3>
                        <span
                          className={`px-3 py-1 rounded-full text-sm font-medium ${
                            guestInfo.attended
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {guestInfo.attended ? "✅ حضر" : "⏳ لم يحضر بعد"}
                        </span>
                      </div>
                      <div className="space-y-2 text-green-700">
                        <p className="flex items-center">
                          <FiUser className="ml-2 flex-shrink-0" />
                          <span className="font-medium">الهاتف:</span>
                          <span className="mr-2">{guestInfo.phone}</span>
                        </p>
                        <p className="flex items-center">
                          <FiCheck className="ml-2 flex-shrink-0" />
                          <span className="font-medium">الحالة:</span>
                          <span className="mr-2">
                            {guestInfo.status === "accepted"
                              ? "قبل الدعوة"
                              : "في الانتظار"}
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <FiUser className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                    <p>لم يتم مسح أي رمز بعد</p>
                    <p className="text-sm">
                      ابدأ بمسح QR Code لعرض معلومات المدعو
                    </p>
                  </div>
                )}
              </div>

              {/* المسح الأخير */}
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                <h2 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
                  <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-2 rounded-lg ml-2">
                    <FiClock className="text-white" />
                  </div>
                  آخر المسح
                </h2>

                {recentScans.length > 0 ? (
                  <div className="space-y-3">
                    {recentScans.map((guest, index) => (
                      <div
                        key={guest.id}
                        className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200"
                      >
                        <div>
                          <p className="font-medium text-blue-800">
                            {guest.name}
                          </p>
                          <p className="text-sm text-blue-600">{guest.phone}</p>
                        </div>
                        <FiCheck className="text-green-500 text-xl" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <FiClock className="mx-auto h-8 w-8 text-gray-300 mb-2" />
                    <p className="text-sm">لا توجد عمليات مسح حديثة</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
