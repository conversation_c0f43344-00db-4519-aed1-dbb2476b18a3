"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import {
  FiCamera,
  FiCheck,
  FiX,
  FiUser,
  FiRefreshCw,
  FiSearch,
  FiUsers,
  FiClock,
  FiScan,
  FiUserCheck,
  FiBarChart3,
  FiCalendar,
  FiMapPin,
  FiUserX,
} from "react-icons/fi";
import { supabase } from "@/lib/supabase";
import Header from "@/components/Header";

export default function ScanPage({ params }) {
  const invitationId = params.id;
  const router = useRouter();
  const scannerRef = useRef(null);

  const [user, setUser] = useState(null);
  const [invitation, setInvitation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [scanning, setScanning] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [guestInfo, setGuestInfo] = useState(null);
  const [scanResult, setScanResult] = useState("");
  const [manualQrCode, setManualQrCode] = useState("");
  const [manualMode, setManualMode] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [recentScans, setRecentScans] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    accepted: 0,
    attended: 0,
  });

  // جلب بيانات المستخدم والدعوة
  useEffect(() => {
    const fetchData = async () => {
      try {
        // جلب بيانات المستخدم
        const userData = localStorage.getItem("user");
        if (userData) {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);

          // جلب بيانات الدعوة
          const { data: invitationData, error: invitationError } =
            await supabase
              .from("invitations")
              .select("*")
              .eq("id", invitationId)
              .eq("user_id", parsedUser.id)
              .single();

          if (invitationError) {
            console.error("Error fetching invitation:", invitationError);
            setError("خطأ في جلب بيانات الدعوة");
            return;
          }

          setInvitation(invitationData);
          await fetchStats();
        } else {
          router.push("/login");
        }
      } catch (error) {
        console.error("Error in fetchData:", error);
        setError("خطأ في جلب البيانات");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [invitationId, router]);

  // جلب الإحصائيات
  const fetchStats = async () => {
    try {
      const { data: guests, error } = await supabase
        .from("guests")
        .select("status, attended")
        .eq("invitation_id", invitationId);

      if (error) {
        console.error("Error fetching stats:", error);
        return;
      }

      const total = guests.length;
      const accepted = guests.filter((g) => g.status === "accepted").length;
      const attended = guests.filter((g) => g.attended === true).length;

      setStats({ total, accepted, attended });
    } catch (error) {
      console.error("Error in fetchStats:", error);
    }
  };

  // بدء المسح
  const startScanner = async () => {
    try {
      setScanning(true);
      setError("");

      // استيراد Html5Qrcode ديناميكياً لتجنب مشاكل SSR
      const { Html5Qrcode } = await import("html5-qrcode");

      const html5QrCode = new Html5Qrcode("reader");
      scannerRef.current = html5QrCode;

      const config = {
        fps: 10,
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1.0,
      };

      await html5QrCode.start(
        { facingMode: "environment" },
        config,
        async (decodedText) => {
          console.log("QR Code detected:", decodedText);
          await handleScanResult(decodedText);
        },
        (errorMessage) => {
          // تجاهل أخطاء المسح العادية
        }
      );
    } catch (error) {
      console.error("Error starting scanner:", error);
      setError("خطأ في تشغيل الماسح. تأكد من السماح بالوصول للكاميرا.");
      setScanning(false);
    }
  };

  // إيقاف المسح
  const stopScanner = async () => {
    try {
      if (scannerRef.current) {
        await scannerRef.current.stop();
        scannerRef.current = null;
      }
    } catch (error) {
      console.error("Error stopping scanner:", error);
    } finally {
      setScanning(false);
    }
  };

  // معالجة نتيجة المسح
  const handleScanResult = async (qrCode) => {
    if (isProcessing) return;

    setIsProcessing(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch("/api/scan-qr", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          qrCode: qrCode,
          invitationId: invitationId,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(`تم تسجيل حضور ${data.guest.name} بنجاح! 🎉`);
        setGuestInfo(data.guest);

        // إضافة للمسح الأخير
        setRecentScans((prev) => [data.guest, ...prev.slice(0, 4)]);

        // تحديث الإحصائيات
        await fetchStats();

        // تأثيرات النجاح
        if (typeof window !== "undefined") {
          const confetti = (await import("canvas-confetti")).default;
          confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 },
            colors: ["#10B981", "#059669", "#047857"],
          });
        }

        // إيقاف المسح بعد النجاح
        await stopScanner();
      } else {
        setError(data.error || "خطأ في معالجة الرمز");
      }
    } catch (error) {
      console.error("Error processing QR code:", error);
      setError("خطأ في الاتصال بالخادم");
    } finally {
      setIsProcessing(false);
    }
  };

  // إرسال الرمز يدوياً
  const handleManualSubmit = async (e) => {
    e.preventDefault();
    if (!manualQrCode.trim()) return;

    await handleScanResult(manualQrCode.trim());
    setManualQrCode("");
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Header userName={user?.name} userRole="subscriber" />
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
          {/* Header Section */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white shadow-xl rounded-2xl overflow-hidden mb-8"
          >
            <div className="p-6 bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 text-white relative overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="relative z-10">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <motion.h1
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                      className="text-3xl font-bold mb-2 flex items-center"
                    >
                      <FiScan className="ml-3 text-4xl" />
                      مسح QR Code للحضور
                    </motion.h1>
                    <motion.p
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="text-purple-100 text-lg"
                    >
                      للدعوة: {invitation?.title}
                    </motion.p>
                  </div>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.4 }}
                  >
                    <Link
                      href={`/subscriber/invitations/${invitationId}`}
                      className="bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 font-bold py-3 px-6 rounded-xl transition-all duration-200 flex items-center"
                    >
                      <FiRefreshCw className="ml-2" />
                      العودة للدعوة
                    </Link>
                  </motion.div>
                </div>

                {/* إحصائيات سريعة */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6"
                >
                  {[
                    {
                      icon: FiUsers,
                      value: stats.total,
                      label: "إجمالي المدعوين",
                      delay: 0.6,
                    },
                    {
                      icon: FiUserCheck,
                      value: stats.accepted,
                      label: "قبلوا الدعوة",
                      delay: 0.7,
                    },
                    {
                      icon: FiCheck,
                      value: stats.attended,
                      label: "حضروا الفعلية",
                      delay: 0.8,
                    },
                    {
                      icon: FiBarChart3,
                      value: `${
                        stats.accepted > 0
                          ? Math.round((stats.attended / stats.accepted) * 100)
                          : 0
                      }%`,
                      label: "معدل الحضور",
                      delay: 0.9,
                    },
                  ].map((stat, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: stat.delay }}
                      whileHover={{ scale: 1.05 }}
                      className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center cursor-pointer"
                    >
                      <stat.icon className="mx-auto text-2xl mb-2" />
                      <div className="text-2xl font-bold">{stat.value}</div>
                      <div className="text-sm text-purple-100">
                        {stat.label}
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </div>
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* قسم المسح */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.0 }}
                className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6"
              >
                <motion.h2
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.1 }}
                  className="text-2xl font-bold mb-6 text-gray-800 flex items-center"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 1.2, type: "spring" }}
                    className="bg-gradient-to-r from-purple-500 to-indigo-500 p-3 rounded-xl ml-3"
                  >
                    <FiCamera className="text-white text-xl" />
                  </motion.div>
                  ماسح QR Code
                </motion.h2>

                <AnimatePresence>
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl mb-4 flex items-center"
                    >
                      <FiX className="ml-2 text-red-500 flex-shrink-0" />
                      <span>{error}</span>
                    </motion.div>
                  )}
                </AnimatePresence>

                <AnimatePresence>
                  {success && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl mb-4 flex items-center"
                    >
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2, type: "spring" }}
                      >
                        <FiCheck className="ml-2 text-green-500 flex-shrink-0" />
                      </motion.div>
                      <span>{success}</span>
                    </motion.div>
                  )}
                </AnimatePresence>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.3 }}
                  className="mb-6"
                >
                  <AnimatePresence mode="wait">
                    {scanning ? (
                      <motion.div
                        key="scanning"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        className="relative"
                      >
                        <div
                          id="reader"
                          className="w-full h-80 rounded-2xl overflow-hidden border-4 border-purple-300 shadow-lg"
                        ></div>
                        <motion.button
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={stopScanner}
                          className="absolute top-4 right-4 bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-full shadow-lg transition-colors duration-200 flex items-center backdrop-blur-sm"
                        >
                          <FiX className="ml-1" />
                          إيقاف
                        </motion.button>
                        <AnimatePresence>
                          {isProcessing && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              exit={{ opacity: 0 }}
                              className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-2xl"
                            >
                              <motion.div
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                className="bg-white rounded-xl p-4 flex items-center"
                              >
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 ml-3"></div>
                                <span className="text-gray-700">
                                  جاري المعالجة...
                                </span>
                              </motion.div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    ) : (
                      <motion.div
                        key="not-scanning"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="text-center p-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border-2 border-dashed border-gray-300"
                      >
                        <motion.div
                          animate={{ y: [0, -10, 0] }}
                          transition={{ repeat: Infinity, duration: 2 }}
                        >
                          <FiCamera className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                        </motion.div>
                        <h3 className="text-xl font-bold text-gray-700 mb-2">
                          ابدأ مسح QR Code
                        </h3>
                        <p className="mb-6 text-gray-600">
                          اضغط على زر "بدء المسح" لتشغيل الكاميرا ومسح رمز QR
                          للضيوف
                        </p>
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={startScanner}
                          className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-8 rounded-xl shadow-lg transition-all duration-200 w-full flex items-center justify-center"
                        >
                          <FiCamera className="ml-2 text-xl" />
                          بدء المسح
                        </motion.button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>

                {/* إدخال يدوي للرمز */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.4 }}
                  className="border-t border-gray-200 pt-6"
                >
                  <motion.h3
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.5 }}
                    className="text-lg font-bold mb-4 text-gray-700 flex items-center"
                  >
                    <FiSearch className="ml-2 text-purple-600" />
                    أو أدخل رمز QR / رقم الهاتف يدويًا
                  </motion.h3>
                  <motion.form
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.6 }}
                    onSubmit={handleManualSubmit}
                    className="space-y-4"
                  >
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <FiSearch className="h-5 w-5 text-gray-400" />
                      </div>
                      <motion.input
                        whileFocus={{ scale: 1.02 }}
                        type="text"
                        value={manualQrCode}
                        onChange={(e) => setManualQrCode(e.target.value)}
                        placeholder="أدخل رمز QR أو رقم الهاتف"
                        className="block w-full pr-10 py-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-lg transition-all duration-200"
                        disabled={isProcessing}
                      />
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      type="submit"
                      disabled={isProcessing}
                      className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 disabled:opacity-50 text-white font-bold py-4 px-6 rounded-xl shadow-lg transition-all duration-200 w-full flex items-center justify-center"
                    >
                      <AnimatePresence mode="wait">
                        {isProcessing ? (
                          <motion.div
                            key="processing"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            className="flex items-center"
                          >
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                            جاري البحث...
                          </motion.div>
                        ) : (
                          <motion.div
                            key="search"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            className="flex items-center"
                          >
                            <FiSearch className="ml-2" />
                            بحث
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.button>
                  </motion.form>
                </motion.div>
              </motion.div>
            </div>

            {/* قسم المعلومات والإحصائيات */}
            <div className="space-y-6">
              {/* معلومات المدعو */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.7 }}
                className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6"
              >
                <motion.h2
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.8 }}
                  className="text-xl font-bold mb-4 text-gray-800 flex items-center"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 1.9, type: "spring" }}
                    className="bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg ml-2"
                  >
                    <FiUser className="text-white" />
                  </motion.div>
                  معلومات المدعو
                </motion.h2>

                <AnimatePresence mode="wait">
                  {guestInfo ? (
                    <motion.div
                      key="guest-info"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="space-y-4"
                    >
                      <motion.div
                        initial={{ scale: 0.9 }}
                        animate={{ scale: 1 }}
                        className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <motion.h3
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="font-bold text-green-800 text-lg"
                          >
                            {guestInfo.name}
                          </motion.h3>
                          <motion.span
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.2 }}
                            className={`px-3 py-1 rounded-full text-sm font-medium ${
                              guestInfo.attended
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                            }`}
                          >
                            {guestInfo.attended ? "✅ حضر" : "⏳ لم يحضر بعد"}
                          </motion.span>
                        </div>
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.3 }}
                          className="space-y-2 text-green-700"
                        >
                          <p className="flex items-center">
                            <FiUser className="ml-2 flex-shrink-0" />
                            <span className="font-medium">الهاتف:</span>
                            <span className="mr-2">{guestInfo.phone}</span>
                          </p>
                          <p className="flex items-center">
                            <FiCheck className="ml-2 flex-shrink-0" />
                            <span className="font-medium">الحالة:</span>
                            <span className="mr-2">
                              {guestInfo.status === "accepted"
                                ? "قبل الدعوة"
                                : "في الانتظار"}
                            </span>
                          </p>
                        </motion.div>
                      </motion.div>
                    </motion.div>
                  ) : (
                    <motion.div
                      key="no-guest"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="text-center py-8 text-gray-500"
                    >
                      <motion.div
                        animate={{ y: [0, -5, 0] }}
                        transition={{ repeat: Infinity, duration: 2 }}
                      >
                        <FiUser className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                      </motion.div>
                      <p>لم يتم مسح أي رمز بعد</p>
                      <p className="text-sm">
                        ابدأ بمسح QR Code لعرض معلومات المدعو
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>

              {/* المسح الأخير */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 2.0 }}
                className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6"
              >
                <motion.h2
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 2.1 }}
                  className="text-xl font-bold mb-4 text-gray-800 flex items-center"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 2.2, type: "spring" }}
                    className="bg-gradient-to-r from-blue-500 to-cyan-500 p-2 rounded-lg ml-2"
                  >
                    <FiClock className="text-white" />
                  </motion.div>
                  آخر المسح
                </motion.h2>

                <AnimatePresence mode="wait">
                  {recentScans.length > 0 ? (
                    <motion.div
                      key="recent-scans"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="space-y-3"
                    >
                      {recentScans.map((guest, index) => (
                        <motion.div
                          key={guest.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          whileHover={{ scale: 1.02 }}
                          className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200 cursor-pointer"
                        >
                          <div>
                            <p className="font-medium text-blue-800">
                              {guest.name}
                            </p>
                            <p className="text-sm text-blue-600">
                              {guest.phone}
                            </p>
                          </div>
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.3 + index * 0.1 }}
                          >
                            <FiCheck className="text-green-500 text-xl" />
                          </motion.div>
                        </motion.div>
                      ))}
                    </motion.div>
                  ) : (
                    <motion.div
                      key="no-scans"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="text-center py-6 text-gray-500"
                    >
                      <motion.div
                        animate={{ rotate: [0, 10, -10, 0] }}
                        transition={{ repeat: Infinity, duration: 3 }}
                      >
                        <FiClock className="mx-auto h-8 w-8 text-gray-300 mb-2" />
                      </motion.div>
                      <p className="text-sm">لا توجد عمليات مسح حديثة</p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
