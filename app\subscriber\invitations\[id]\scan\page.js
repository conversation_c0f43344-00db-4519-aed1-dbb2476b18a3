"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  FiCamera,
  FiCheck,
  FiX,
  FiUser,
  FiRefreshCw,
  FiSearch,
  FiPhone,
} from "react-icons/fi";
import { BsQrCodeScan } from "react-icons/bs";
import { Html5Qrcode } from "html5-qrcode";
import { supabase } from "@/lib/supabase";
import Header from "@/components/Header";

export default function ScanPage({ params }) {
  const invitationId = params.id;
  const router = useRouter();
  const scannerRef = useRef(null);

  const [user, setUser] = useState(null);
  const [invitation, setInvitation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [scanning, setScanning] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [scanResult, setScanResult] = useState(null);
  const [guestInfo, setGuestInfo] = useState(null);
  const [manualQrCode, setManualQrCode] = useState("");
  const [manualMode, setManualMode] = useState(false);

  useEffect(() => {
    const fetchUserAndInvitation = async () => {
      try {
        // التحقق من وجود المستخدم في localStorage
        const userData = localStorage.getItem("user");
        if (!userData) {
          console.log("No user found in localStorage, redirecting to login");
          router.push("/login");
          return;
        }

        const parsedUser = JSON.parse(userData);
        console.log("User found in localStorage:", parsedUser);

        if (!parsedUser || parsedUser.role !== "subscriber") {
          console.log("Invalid user role, redirecting to login");
          router.push("/login");
          return;
        }

        setUser(parsedUser);

        // الحصول على بيانات الدعوة
        const { data, error } = await supabase
          .from("invitations")
          .select("*")
          .eq("id", invitationId)
          .single();

        if (error) throw error;
        setInvitation(data);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("حدث خطأ أثناء جلب البيانات");
      } finally {
        setLoading(false);
      }
    };

    fetchUserAndInvitation();

    // تنظيف الماسح عند مغادرة الصفحة
    return () => {
      if (scannerRef.current) {
        scannerRef.current.stop();
        scannerRef.current.clear();
      }
    };
  }, [invitationId, router]);

  const startScanner = async () => {
    if (scannerRef.current) {
      stopScanner();
    }

    setScanning(true);
    setError(null);
    setSuccess(null);
    setScanResult(null);
    setGuestInfo(null);

    try {
      // التحقق من دعم واجهة برمجة التطبيقات MediaDevices
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.log("MediaDevices API not supported");
        setManualMode(true);
        setScanning(false);
        setError(
          "متصفحك لا يدعم الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي أدناه."
        );
        return;
      }

      // طلب إذن الوصول إلى الكاميرا
      try {
        await navigator.mediaDevices.getUserMedia({ video: true });
      } catch (err) {
        console.error("Camera permission error:", err);
        setManualMode(true);
        setScanning(false);

        if (err.name === "NotAllowedError") {
          setError(
            "لم يتم منح إذن الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي أدناه."
          );
        } else if (err.name === "NotFoundError") {
          setError(
            "لم يتم العثور على كاميرا في جهازك. يرجى استخدام الإدخال اليدوي أدناه."
          );
        } else {
          setError(
            "حدث خطأ أثناء الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي أدناه."
          );
        }
        return;
      }

      // تأخير بدء الماسح للتأكد من وجود العنصر في DOM
      setTimeout(() => {
        try {
          const readerElement = document.getElementById("reader");

          if (!readerElement) {
            throw new Error("لم يتم العثور على عنصر الماسح في الصفحة");
          }

          scannerRef.current = new Html5Qrcode("reader");

          // استخدام إعدادات أكثر توافقًا
          scannerRef.current
            .start(
              { facingMode: "environment" }, // استخدام الكاميرا الخلفية
              {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                formatsToSupport: [Html5Qrcode.FORMATS.QR_CODE],
              },
              (decodedText) => {
                handleScanResult(decodedText);
              },
              (errorMessage) => {
                console.log(errorMessage);
              }
            )
            .catch((err) => {
              console.error("Error starting scanner:", err);
              setManualMode(true);
              setScanning(false);
              setError("تعذر تشغيل الماسح. يرجى استخدام الإدخال اليدوي أدناه.");
            });
        } catch (error) {
          console.error("Error initializing scanner:", error);
          setManualMode(true);
          setScanning(false);
          setError(
            "حدث خطأ أثناء تهيئة الماسح. يرجى استخدام الإدخال اليدوي أدناه."
          );
        }
      }, 500);
    } catch (error) {
      console.error("Unexpected error:", error);
      setManualMode(true);
      setScanning(false);
      setError("حدث خطأ غير متوقع. يرجى استخدام الإدخال اليدوي أدناه.");
    }
  };

  const stopScanner = () => {
    if (scannerRef.current) {
      scannerRef.current
        .stop()
        .then(() => {
          scannerRef.current.clear();
          setScanning(false);
        })
        .catch((err) => {
          console.error("Error stopping scanner:", err);
        });
    }
  };

  const handleScanResult = async (qrCode) => {
    try {
      setError(null);
      setSuccess(null);
      setScanResult(qrCode);

      console.log("QR Code scanned:", qrCode);

      // استخدام API الجديد لمعالجة QR code
      const response = await fetch("/api/scan-qr", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          qrCode: qrCode,
          invitationId: invitationId,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "فشل في معالجة رمز QR");
      }

      if (result.success) {
        setGuestInfo(result.guest);

        if (result.alreadyAttended) {
          setSuccess(
            `تم تسجيل حضور هذا المدعو مسبقًا (${
              result.guest.name || result.guest.phone
            })`
          );
        } else {
          setSuccess(
            `تم تسجيل حضور المدعو بنجاح (${
              result.guest.name || result.guest.phone
            })`
          );
        }
      }

      stopScanner();
    } catch (error) {
      console.error("Error processing scan result:", error);
      setError(error.message);
    }
  };

  const handleManualSubmit = async (e) => {
    e.preventDefault();

    if (!manualQrCode.trim()) {
      setError("الرجاء إدخال رمز QR أو رقم الهاتف");
      return;
    }

    try {
      setError(null);
      setSuccess(null);
      setScanResult(manualQrCode);

      console.log("Manual QR/Phone input:", manualQrCode);

      // استخدام API الجديد لمعالجة QR code أو رقم الهاتف
      const response = await fetch("/api/scan-qr", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          qrCode: manualQrCode.trim(),
          invitationId: invitationId,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "فشل في معالجة البيانات المدخلة");
      }

      if (result.success) {
        setGuestInfo(result.guest);

        if (result.alreadyAttended) {
          setSuccess(
            `تم تسجيل حضور هذا المدعو مسبقًا (${
              result.guest.name || result.guest.phone
            })`
          );
        } else {
          setSuccess(
            `تم تسجيل حضور المدعو بنجاح (${
              result.guest.name || result.guest.phone
            })`
          );
        }

        // مسح الحقل بعد النجاح
        setManualQrCode("");
      }
    } catch (error) {
      console.error("Error processing manual input:", error);
      setError(error.message);
    }
  };

  if (loading) {
    return (
      <>
        <Header userName={user?.name} userRole="subscriber" />
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
          <div className="bg-white shadow-md rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header userName={user?.name} userRole="subscriber" />
      <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <div className="p-6 sm:p-8 bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600 text-white relative overflow-hidden">
            {/* خلفية ديكورية */}
            <div className="absolute inset-0 bg-black bg-opacity-10"></div>
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-white bg-opacity-10 rounded-full"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-white bg-opacity-5 rounded-full"></div>

            <div className="relative z-10">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div className="flex items-center mb-4 lg:mb-0">
                  <div className="bg-white bg-opacity-20 p-3 rounded-full ml-4">
                    <BsQrCodeScan className="h-6 w-6" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold mb-2">
                      مسح QR Code للحضور
                    </h1>
                    <p className="text-purple-100 font-medium">
                      للدعوة: {invitation?.title}
                    </p>
                  </div>
                </div>
                <Link
                  href={`/subscriber/invitations/${invitationId}`}
                  className="bg-white bg-opacity-20 backdrop-blur-sm text-white hover:bg-opacity-30 font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  العودة للدعوة
                </Link>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-4 sm:p-6">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
              <div className="text-center mb-6">
                <div className="bg-gradient-to-r from-purple-500 to-indigo-600 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <FiCamera className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">
                  ماسح QR Code
                </h2>
                <p className="text-gray-600">امسح رمز QR للضيف لتسجيل الحضور</p>
              </div>

              {error && (
                <div className="bg-red-100 border-r-4 border-red-500 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
                  <FiX className="ml-2 text-red-500" />
                  {error}
                </div>
              )}

              {success && (
                <div className="bg-green-100 border-r-4 border-green-500 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
                  <FiCheck className="ml-2 text-green-500" />
                  {success}
                </div>
              )}

              <div className="mb-6">
                {scanning ? (
                  <div className="relative">
                    <div
                      id="reader"
                      className="w-full h-64 rounded-lg overflow-hidden border-2 border-purple-300"
                    ></div>
                    <button
                      onClick={stopScanner}
                      className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-full shadow-md transition-colors duration-200 flex items-center"
                    >
                      <FiX className="ml-1" />
                      إيقاف
                    </button>
                  </div>
                ) : (
                  <div className="text-center p-8 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border-2 border-dashed border-gray-300">
                    <div className="bg-white p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-md">
                      <FiCamera className="h-10 w-10 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">
                      جاهز للمسح
                    </h3>
                    <p className="mb-6 text-gray-600">
                      اضغط على زر "بدء المسح" لتشغيل الكاميرا ومسح رمز QR
                    </p>
                    <button
                      onClick={startScanner}
                      className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-8 rounded-xl shadow-lg transition-all duration-300 transform hover:scale-105 w-full flex items-center justify-center"
                    >
                      <FiCamera className="ml-2 h-5 w-5" />
                      بدء المسح
                    </button>
                  </div>
                )}
              </div>

              {/* إدخال يدوي للرمز */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="text-center mb-4">
                  <div className="bg-gradient-to-r from-indigo-500 to-blue-600 p-3 rounded-full w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                    <FiSearch className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-800 mb-1">
                    البحث اليدوي
                  </h3>
                  <p className="text-sm text-gray-600">
                    أدخل رمز QR أو رقم الهاتف
                  </p>
                </div>
                <form onSubmit={handleManualSubmit} className="space-y-4">
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                      <FiSearch className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={manualQrCode}
                      onChange={(e) => setManualQrCode(e.target.value)}
                      placeholder="أدخل رمز QR أو رقم الهاتف"
                      className="block w-full pr-12 py-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-right shadow-sm"
                    />
                  </div>
                  <button
                    type="submit"
                    className="bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-xl shadow-lg transition-all duration-300 transform hover:scale-105 w-full flex items-center justify-center"
                  >
                    <FiSearch className="ml-2 h-5 w-5" />
                    بحث وتسجيل الحضور
                  </button>
                </form>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
              <div className="text-center mb-6">
                <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <FiUser className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">
                  معلومات المدعو
                </h2>
                <p className="text-gray-600">تفاصيل الضيف المسجل</p>
              </div>

              {guestInfo ? (
                <div className="space-y-6">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
                    <div className="flex items-center mb-2">
                      <FiUser className="h-5 w-5 text-blue-600 ml-2" />
                      <p className="text-sm font-medium text-blue-600">الاسم</p>
                    </div>
                    <p className="font-bold text-xl text-gray-800">
                      {guestInfo.name || "غير محدد"}
                    </p>
                  </div>

                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-100">
                    <div className="flex items-center mb-2">
                      <FiPhone className="h-5 w-5 text-green-600 ml-2" />
                      <p className="text-sm font-medium text-green-600">
                        رقم الهاتف
                      </p>
                    </div>
                    <p className="font-bold text-xl text-gray-800">
                      {guestInfo.phone}
                    </p>
                  </div>

                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-100">
                    <div className="flex items-center mb-2">
                      <FiCheck className="h-5 w-5 text-purple-600 ml-2" />
                      <p className="text-sm font-medium text-purple-600">
                        حالة الحضور
                      </p>
                    </div>
                    <div className="mt-2">
                      {guestInfo.attended ? (
                        <span className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                          <FiCheck className="h-4 w-4 ml-1" />
                          حضر
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">
                          <FiX className="h-4 w-4 ml-1" />
                          لم يحضر
                        </span>
                      )}
                    </div>
                  </div>

                  {scanResult && (
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <p className="text-gray-500 text-sm mb-1">رمز QR</p>
                      <p className="font-mono text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                        {scanResult}
                      </p>
                    </div>
                  )}

                  <div className="mt-6">
                    <button
                      onClick={() => {
                        setScanResult(null);
                        setGuestInfo(null);
                        setError("");
                        setSuccess("");
                      }}
                      className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition-colors duration-200 w-full flex items-center justify-center"
                    >
                      <FiRefreshCw className="ml-2" />
                      مسح بيانات المدعو
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center p-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <FiUser className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">
                    قم بمسح رمز QR لعرض معلومات المدعو
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* إحصائيات المسح */}
          <div className="mt-8 bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
            <div className="text-center mb-6">
              <div className="bg-gradient-to-r from-orange-500 to-red-600 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <FiRefreshCw className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                إحصائيات الحضور
              </h2>
              <p className="text-gray-600">معلومات سريعة عن حالة الحضور</p>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100 text-center">
                <div className="text-2xl font-bold text-blue-600 mb-1">
                  {invitation?.guests?.length || 0}
                </div>
                <div className="text-sm text-blue-600 font-medium">
                  إجمالي المدعوين
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-xl border border-green-100 text-center">
                <div className="text-2xl font-bold text-green-600 mb-1">
                  {invitation?.guests?.filter((g) => g.status === "accepted")
                    .length || 0}
                </div>
                <div className="text-sm text-green-600 font-medium">مقبول</div>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-100 text-center">
                <div className="text-2xl font-bold text-purple-600 mb-1">
                  {invitation?.guests?.filter((g) => g.attended).length || 0}
                </div>
                <div className="text-sm text-purple-600 font-medium">
                  حضر فعلياً
                </div>
              </div>

              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-xl border border-yellow-100 text-center">
                <div className="text-2xl font-bold text-yellow-600 mb-1">
                  {invitation?.guests?.filter((g) => g.qr_scanned_at).length ||
                    0}
                </div>
                <div className="text-sm text-yellow-600 font-medium">
                  تم مسح QR
                </div>
              </div>
            </div>

            {guestInfo && (
              <div className="mt-6 p-4 bg-gradient-to-r from-green-100 to-emerald-100 rounded-xl border border-green-200">
                <div className="flex items-center justify-center">
                  <FiCheck className="h-6 w-6 text-green-600 ml-2" />
                  <span className="text-green-800 font-medium">
                    آخر عملية مسح: {guestInfo.name || guestInfo.phone}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
