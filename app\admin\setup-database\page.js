'use client';

import { useState } from 'react';
import Navbar from '../../../components/Navbar';
import Link from 'next/link';

export default function SetupDatabase() {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [showManualInstructions, setShowManualInstructions] = useState(true);

  const checkDatabase = async () => {
    setLoading(true);
    setMessage('');
    setError('');

    try {
      // التحقق من وجود جدول templates
      const response = await fetch('/api/check-tables');
      const result = await response.json();
      
      console.log('API response:', result);

      if (result.error) {
        throw new Error(result.error);
      }

      // التأكد من وجود البيانات في الاستجابة
      if (result.data && typeof result.data.templatesExists !== 'undefined') {
        if (result.data.templatesExists) {
          setMessage('تم التحقق بنجاح. جدول templates موجود في قاعدة البيانات.');
        } else {
          setError('جدول templates غير موجود. يرجى اتباع التعليمات اليدوية لإنشاء الجدول.');
          setShowManualInstructions(true);
        }
      } else {
        throw new Error('استجابة API غير صالحة: البيانات المطلوبة غير موجودة');
      }
    } catch (error) {
      console.error('Error checking database:', error);
      setError(`حدث خطأ أثناء التحقق من قاعدة البيانات: ${error.message}`);
      setShowManualInstructions(true);
    } finally {
      setLoading(false);
    }
  };

  // تعليمات إعداد قاعدة البيانات يدوين
  const manualInstructions = () => {
    return (
      <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
        <h3 className="font-bold mb-2">تعليمات إعداد قاعدة البيانات يدوين:</h3>
        <ol className="list-decimal pl-5">
          <li className="mb-1">قم بتسجيل الدخول إلى لوحة تحكم Supabase</li>
          <li className="mb-1">اختر مشروعك</li>
          <li className="mb-1">انتقل إلى قسم "SQL Editor"</li>
          <li className="mb-1">اضغط على "New Query"</li>
          <li className="mb-1">انسخ والصق الاستعلام التالي:</li>
          <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto my-2">
{`-- إنشاء جدول templates
CREATE TABLE IF NOT EXISTS templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  image_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة علاقة بين جدول invitations وجدول templates
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invitations') THEN
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'invitations' AND column_name = 'template_id'
    ) THEN
      ALTER TABLE invitations 
      ADD COLUMN template_id INTEGER REFERENCES templates(id);
    END IF;
  END IF;
END $$;

-- إضافة عمود للبيانات الإضافية في جدول invitations
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invitations') THEN
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'invitations' AND column_name = 'additional_data'
    ) THEN
      ALTER TABLE invitations 
      ADD COLUMN additional_data JSONB DEFAULT '{}'::jsonb;
    END IF;
  END IF;
END $$;

-- إضافة بعض القوالب الافتراضية للاختبار
INSERT INTO templates (name, image_url)
VALUES 
  ('قالب زفاف', 'https://example.com/wedding.jpg'),
  ('قالب تخرج', 'https://example.com/graduation.jpg'),
  ('قالب عيد ميلاد', 'https://example.com/birthday.jpg');`}
          </pre>
          <li className="mb-1">اضغط على "Run" لتنفيذ الاستعلام</li>
          <li className="mb-1">بعد التنفيذ، اضغط على زر "التحقق من قاعدة البيانات" أدناه للتأكد من وجود الجدول</li>
        </ol>
      </div>
    );
  };

  return (
    <>
      <Navbar userRole="admin" />
      <div className="p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">إعداد قاعدة البيانات</h1>
          <Link 
            href="/admin/dashboard" 
            className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
          >
            العودة للوحة التحكم
          </Link>
        </div>
        
        {message && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {message}
          </div>
        )}
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        {showManualInstructions && manualInstructions()}
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">التحقق من قاعدة البيانات</h2>
          <p className="mb-4">
            انقر على الزر أدناه للتحقق من وجود جدول templates في قاعدة البيانات.
          </p>
          
          <button
            onClick={checkDatabase}
            disabled={loading}
            className={`${
              loading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
            } text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            {loading ? 'جاري التحقق...' : 'التحقق من قاعدة البيانات'}
          </button>
        </div>
      </div>
    </>
  );
}

