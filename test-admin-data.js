/**
 * ملف اختبار لجلب بيانات المدير
 */

import { supabase } from "./lib/supabase.js";

async function testAdminData() {
  console.log("🔍 اختبار جلب بيانات المدير...");
  
  try {
    // 1. اختبار جلب المستخدمين
    console.log("\n📊 جلب بيانات المستخدمين...");
    const { data: users, error: usersError } = await supabase
      .from("users")
      .select("id, role, is_active, created_at");
    
    if (usersError) {
      console.error("❌ خطأ في جلب المستخدمين:", usersError);
    } else {
      console.log("✅ تم جلب المستخدمين بنجاح:", users?.length || 0, "مستخدم");
      console.log("المستخدمون:", users);
    }

    // 2. اختبار جلب الدعوات
    console.log("\n📧 جلب بيانات الدعوات...");
    const { data: invitations, error: invitationsError } = await supabase
      .from("invitations")
      .select("id, status, created_at, event_date, user_id");
    
    if (invitationsError) {
      console.error("❌ خطأ في جلب الدعوات:", invitationsError);
    } else {
      console.log("✅ تم جلب الدعوات بنجاح:", invitations?.length || 0, "دعوة");
      console.log("الدعوات:", invitations);
    }

    // 3. اختبار جلب الضيوف
    console.log("\n👥 جلب بيانات الضيوف...");
    const { data: guests, error: guestsError } = await supabase
      .from("guests")
      .select("id, status, attended, invitation_id, created_at");
    
    if (guestsError) {
      console.error("❌ خطأ في جلب الضيوف:", guestsError);
    } else {
      console.log("✅ تم جلب الضيوف بنجاح:", guests?.length || 0, "ضيف");
      console.log("الضيوف:", guests);
    }

    // 4. اختبار الاتصال بقاعدة البيانات
    console.log("\n🔗 اختبار الاتصال بقاعدة البيانات...");
    const { data: testData, error: testError } = await supabase
      .from("users")
      .select("count", { count: "exact", head: true });
    
    if (testError) {
      console.error("❌ خطأ في الاتصال:", testError);
    } else {
      console.log("✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح");
    }

  } catch (error) {
    console.error("❌ خطأ عام في الاختبار:", error);
  }
}

// تشغيل الاختبار
testAdminData();
