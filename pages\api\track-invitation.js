// pages/api/track-invitation.js
import { supabase } from '@/lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { invitationId } = req.query;

    if (!invitationId) {
      return res.status(400).json({ error: 'Invitation ID is required' });
    }

    // الحصول على بيانات الدعوة
    const { data: invitation, error: invitationError } = await supabase
      .from('invitations')
      .select('*')
      .eq('id', invitationId)
      .single();
    
    if (invitationError) {
      return res.status(404).json({ error: 'Invitation not found' });
    }

    // الحصول على إحصائيات المدعوين
    const { data: attendees, error: