// pages/api/track-invitation.js
import { supabase } from "@/lib/supabase";

export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { invitationId } = req.query;

    if (!invitationId) {
      return res.status(400).json({ error: "Invitation ID is required" });
    }

    // الحصول على بيانات الدعوة
    const { data: invitation, error: invitationError } = await supabase
      .from("invitations")
      .select("*")
      .eq("id", invitationId)
      .single();

    if (invitationError) {
      return res.status(404).json({ error: "Invitation not found" });
    }

    // الحصول على إحصائيات المدعوين
    const { data: attendees, error: attendeesError } = await supabase
      .from("guests")
      .select("*")
      .eq("invitation_id", invitationId);

    if (attendeesError) {
      return res.status(500).json({ error: "Error fetching attendees" });
    }

    // إرجاع البيانات
    res.status(200).json({
      invitation: invitationData,
      attendees: attendees || [],
      stats: {
        total: attendees?.length || 0,
        accepted: attendees?.filter((a) => a.status === "accepted").length || 0,
        declined: attendees?.filter((a) => a.status === "declined").length || 0,
        attended: attendees?.filter((a) => a.attended === true).length || 0,
      },
    });
  } catch (error) {
    console.error("Error in track-invitation API:", error);
    res.status(500).json({ error: "Internal server error" });
  }
}
