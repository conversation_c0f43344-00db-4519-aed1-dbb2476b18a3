"use client";

import { useState } from "react";
import Link from "next/link";

export default function SetupPage() {
  const [status, setStatus] = useState("لم يبدأ");
  const [message, setMessage] = useState("");
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showManualOption, setShowManualOption] = useState(false);

  async function setupDatabase() {
    setIsLoading(true);
    setStatus("جاري الإعداد...");
    setError(null);
    setShowManualOption(false);

    try {
      const response = await fetch("/api/setup-db");
      const data = await response.json();

      if (data.success) {
        setStatus("تم الإعداد بنجاح");
        setMessage(data.message);

        // عرض النتائج التفصيلية
        if (data.results && data.results.length > 0) {
          const resultMessages = data.results
            .map((r) => `${r.table}: ${r.message}`)
            .join("\n");
          setMessage(data.message + "\n\nتفاصيل:\n" + resultMessages);
        }
      } else {
        setStatus("فشل الإعداد");
        setError(data.error || data.message);
        setShowManualOption(true);
      }
    } catch (err) {
      setStatus("حدث خطأ");
      setError(err.message);
      setShowManualOption(true);
    } finally {
      setIsLoading(false);
    }
  }

  async function createTestUser() {
    setIsLoading(true);
    setStatus("جاري إنشاء المستخدم التجريبي...");
    setError(null);

    try {
      const response = await fetch("/api/auth/quick-login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ role: "subscriber" }),
      });

      const data = await response.json();

      if (data.success) {
        setStatus("تم إنشاء المستخدم التجريبي بنجاح");
        setMessage("يمكنك الآن تسجيل الدخول باستخدام البيانات المعروضة أدناه");
      } else {
        setStatus("فشل في إنشاء المستخدم");
        setError(data.error || "حدث خطأ غير معروف");
      }
    } catch (err) {
      setStatus("حدث خطأ");
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6 text-center">
        إعداد قاعدة البيانات
      </h1>

      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <p className="mb-4">
          هذه الصفحة ستقوم بإعداد قاعدة البيانات وإنشاء الجداول اللازمة وإضافة
          بيانات المستخدمين الافتراضية.
        </p>

        <div className="mb-4">
          <p className="font-bold mb-2">حالة الإعداد:</p>
          <p
            className={`
            ${status === "تم الإعداد بنجاح" ? "text-green-600" : ""}
            ${
              status === "فشل الإعداد" || status === "حدث خطأ"
                ? "text-red-600"
                : ""
            }
            ${status === "جاري الإعداد..." ? "text-blue-600" : ""}
          `}
          >
            {status}
          </p>

          {message && <p className="mt-2 text-green-600">{message}</p>}

          {error && (
            <div className="mt-4 p-4 bg-red-100 text-red-700 rounded">
              <p className="font-bold">رسالة الخطأ:</p>
              <p>{error}</p>
            </div>
          )}

          {showManualOption && (
            <div className="mt-4 p-4 bg-yellow-100 text-yellow-800 rounded">
              <p>
                يبدو أن هناك مشكلة في الإعداد التلقائي. يمكنك محاولة الإعداد
                اليدوي بدلاً من ذلك.
              </p>
              <Link
                href="/setup/manual"
                className="block mt-2 text-blue-600 hover:underline"
              >
                الانتقال إلى صفحة الإعداد اليدوي
              </Link>
            </div>
          )}
        </div>

        <div className="space-y-3">
          <button
            onClick={setupDatabase}
            disabled={isLoading}
            className={`
              w-full py-2 px-4 rounded
              ${isLoading ? "bg-gray-400" : "bg-blue-600 hover:bg-blue-700"}
              text-white font-bold transition-colors
            `}
          >
            {isLoading ? "جاري الإعداد..." : "فحص وإعداد قاعدة البيانات"}
          </button>

          <button
            onClick={createTestUser}
            disabled={isLoading}
            className={`
              w-full py-2 px-4 rounded
              ${isLoading ? "bg-gray-400" : "bg-green-600 hover:bg-green-700"}
              text-white font-bold transition-colors
            `}
          >
            إنشاء مستخدم تجريبي
          </button>

          <Link
            href="/login"
            className="block w-full py-2 px-4 rounded bg-purple-600 hover:bg-purple-700 text-white font-bold text-center transition-colors"
          >
            الذهاب لتسجيل الدخول
          </Link>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-xl font-bold mb-4">بيانات المستخدمين الافتراضية</h2>

        <div className="mb-4">
          <h3 className="font-bold">حساب المدير:</h3>
          <p>
            رقم الهاتف:{" "}
            <code className="bg-gray-100 px-2 py-1 rounded">0500000001</code>
          </p>
          <p>
            كلمة المرور:{" "}
            <code className="bg-gray-100 px-2 py-1 rounded">admin123</code>
          </p>
        </div>

        <div>
          <h3 className="font-bold">حساب المستخدم العادي:</h3>
          <p>
            رقم الهاتف:{" "}
            <code className="bg-gray-100 px-2 py-1 rounded">0500000002</code>
          </p>
          <p>
            كلمة المرور:{" "}
            <code className="bg-gray-100 px-2 py-1 rounded">password123</code>
          </p>
        </div>
      </div>

      <div className="text-center">
        <Link href="/" className="text-blue-500 hover:underline">
          العودة إلى الصفحة الرئيسية
        </Link>
      </div>
    </div>
  );
}
