'use client';

import { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

export default function TestPage() {
  const [status, setStatus] = useState('جاري التحقق...');
  const [error, setError] = useState(null);
  const [envVars, setEnvVars] = useState({});

  useEffect(() => {
    async function checkConnection() {
      try {
        // التحقق من متغيرات البيئة
        setEnvVars({
          supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'موجود' : 'غير موجود',
          supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'موجود' : 'غير موجود'
        });

        // التحقق من الاتصال بـ Supabase
        const { data, error } = await supabase
          .from('users')
          .select('count(*)', { count: 'exact', head: true });

        if (error) {
          setStatus('فشل الاتصال');
          setError(error.message);
          console.error('Supabase connection error:', error);
        } else {
          setStatus('تم الاتصال بنجاح');
          console.log('Connection successful, count:', data);
        }
      } catch (err) {
        setStatus('حدث خطأ');
        setError(err.message);
        console.error('Error checking connection:', err);
      }
    }

    checkConnection();
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">اختبار الاتصال بـ Supabase</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-xl font-bold mb-4">حالة الاتصال</h2>
        <p className={`text-lg ${status === 'تم الاتصال بنجاح' ? 'text-green-600' : 'text-red-600'}`}>
          {status}
        </p>
        {error && (
          <div className="mt-4 p-4 bg-red-100 text-red-700 rounded">
            <p className="font-bold">رسالة الخطأ:</p>
            <p>{error}</p>
          </div>
        )}
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-bold mb-4">متغيرات البيئة</h2>
        <ul>
          <li className="mb-2">
            <span className="font-bold">NEXT_PUBLIC_SUPABASE_URL:</span>{' '}
            <span className={envVars.supabaseUrl === 'موجود' ? 'text-green-600' : 'text-red-600'}>
              {envVars.supabaseUrl}
            </span>
          </li>
          <li>
            <span className="font-bold">NEXT_PUBLIC_SUPABASE_ANON_KEY:</span>{' '}
            <span className={envVars.supabaseKey === 'موجود' ? 'text-green-600' : 'text-red-600'}>
              {envVars.supabaseKey}
            </span>
          </li>
        </ul>
      </div>
      
      <div className="mt-6">
        <a href="/" className="text-blue-500 hover:underline">العودة إلى الصفحة الرئيسية</a>
      </div>
    </div>
  );
}