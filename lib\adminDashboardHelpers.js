/**
 * دوال مساعدة لداشبورد الأدمن
 */

import { supabase } from "./supabase";
import { validateUserId, normalizeGuestStatus } from "./dashboardHelpers";

/**
 * جلب إحصائيات شاملة للأدمن
 */
export async function fetchAdminStats() {
  console.log("fetchAdminStats called");
  
  try {
    // 1. إحصائيات المستخدمين
    console.log("Fetching user statistics...");
    const { data: usersData, error: usersError } = await supabase
      .from("users")
      .select("id, role, is_active, created_at");

    if (usersError) {
      console.error("Error fetching users:", usersError);
      throw new Error(`فشل في جلب بيانات المستخدمين: ${usersError.message}`);
    }

    const totalUsers = usersData?.length || 0;
    const activeUsers = usersData?.filter(user => user.is_active)?.length || 0;
    const subscribers = usersData?.filter(user => user.role === 'subscriber')?.length || 0;
    const admins = usersData?.filter(user => user.role === 'admin')?.length || 0;

    // 2. إحصائيات الدعوات
    console.log("Fetching invitations statistics...");
    const { data: invitationsData, error: invitationsError } = await supabase
      .from("invitations")
      .select("id, status, created_at, event_date, user_id");

    if (invitationsError) {
      console.error("Error fetching invitations:", invitationsError);
      throw new Error(`فشل في جلب بيانات الدعوات: ${invitationsError.message}`);
    }

    const totalInvitations = invitationsData?.length || 0;
    const activeInvitations = invitationsData?.filter(inv => 
      inv.status === 'active' || inv.status === 'published'
    )?.length || 0;
    
    // الأحداث القادمة (خلال الشهر القادم)
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    const upcomingEvents = invitationsData?.filter(inv => 
      inv.event_date && new Date(inv.event_date) > new Date() && new Date(inv.event_date) <= nextMonth
    )?.length || 0;

    // 3. إحصائيات الضيوف
    console.log("Fetching guests statistics...");
    const { data: guestsData, error: guestsError } = await supabase
      .from("guests")
      .select("id, status, attended, invitation_id, created_at");

    if (guestsError) {
      console.error("Error fetching guests:", guestsError);
      throw new Error(`فشل في جلب بيانات الضيوف: ${guestsError.message}`);
    }

    const totalGuests = guestsData?.length || 0;
    let acceptedGuests = 0;
    let declinedGuests = 0;
    let pendingGuests = 0;
    let attendedGuests = 0;

    guestsData?.forEach(guest => {
      const status = normalizeGuestStatus(guest.status);
      if (status === 'accepted') acceptedGuests++;
      else if (status === 'declined') declinedGuests++;
      else pendingGuests++;

      if (guest.attended === true) attendedGuests++;
    });

    // 4. إحصائيات النمو (آخر 6 أشهر)
    console.log("Calculating growth statistics...");
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const newUsersThisMonth = usersData?.filter(user => {
      const userDate = new Date(user.created_at);
      const thisMonth = new Date();
      return userDate.getMonth() === thisMonth.getMonth() && 
             userDate.getFullYear() === thisMonth.getFullYear();
    })?.length || 0;

    const newInvitationsThisMonth = invitationsData?.filter(inv => {
      const invDate = new Date(inv.created_at);
      const thisMonth = new Date();
      return invDate.getMonth() === thisMonth.getMonth() && 
             invDate.getFullYear() === thisMonth.getFullYear();
    })?.length || 0;

    // 5. معدلات الأداء
    const acceptanceRate = totalGuests > 0 ? Math.round((acceptedGuests / totalGuests) * 100) : 0;
    const attendanceRate = acceptedGuests > 0 ? Math.round((attendedGuests / acceptedGuests) * 100) : 0;
    const avgGuestsPerInvitation = totalInvitations > 0 ? Math.round(totalGuests / totalInvitations) : 0;

    const result = {
      users: {
        total: totalUsers,
        active: activeUsers,
        subscribers: subscribers,
        admins: admins,
        newThisMonth: newUsersThisMonth,
        growthRate: totalUsers > 0 ? Math.round((newUsersThisMonth / totalUsers) * 100) : 0
      },
      invitations: {
        total: totalInvitations,
        active: activeInvitations,
        upcoming: upcomingEvents,
        newThisMonth: newInvitationsThisMonth,
        growthRate: totalInvitations > 0 ? Math.round((newInvitationsThisMonth / totalInvitations) * 100) : 0
      },
      guests: {
        total: totalGuests,
        accepted: acceptedGuests,
        declined: declinedGuests,
        pending: pendingGuests,
        attended: attendedGuests,
        acceptanceRate: acceptanceRate,
        attendanceRate: attendanceRate,
        avgPerInvitation: avgGuestsPerInvitation
      },
      performance: {
        acceptanceRate: acceptanceRate,
        attendanceRate: attendanceRate,
        avgGuestsPerInvitation: avgGuestsPerInvitation,
        userEngagement: activeUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0
      }
    };

    console.log("Admin stats calculated successfully:", result);
    return result;

  } catch (error) {
    console.error("Error in fetchAdminStats:", error);
    
    // إرجاع بيانات افتراضية في حالة الخطأ
    const defaultResult = {
      users: {
        total: 0, active: 0, subscribers: 0, admins: 0, 
        newThisMonth: 0, growthRate: 0
      },
      invitations: {
        total: 0, active: 0, upcoming: 0, 
        newThisMonth: 0, growthRate: 0
      },
      guests: {
        total: 0, accepted: 0, declined: 0, pending: 0, 
        attended: 0, acceptanceRate: 0, attendanceRate: 0, avgPerInvitation: 0
      },
      performance: {
        acceptanceRate: 0, attendanceRate: 0, 
        avgGuestsPerInvitation: 0, userEngagement: 0
      }
    };
    
    console.log("Returning default admin stats due to error:", defaultResult);
    return defaultResult;
  }
}

/**
 * جلب آخر النشاطات للأدمن
 */
export async function fetchRecentActivities() {
  console.log("fetchRecentActivities called");
  
  try {
    // آخر المستخدمين المسجلين
    const { data: recentUsers, error: usersError } = await supabase
      .from("users")
      .select("id, name, phone, role, is_active, created_at")
      .order("created_at", { ascending: false })
      .limit(5);

    if (usersError) {
      console.error("Error fetching recent users:", usersError);
    }

    // آخر الدعوات المنشأة
    const { data: recentInvitations, error: invitationsError } = await supabase
      .from("invitations")
      .select(`
        id, title, status, created_at, event_date, 
        users!invitations_user_id_fkey(name)
      `)
      .order("created_at", { ascending: false })
      .limit(5);

    if (invitationsError) {
      console.error("Error fetching recent invitations:", invitationsError);
    }

    // آخر الضيوف المضافين
    const { data: recentGuests, error: guestsError } = await supabase
      .from("guests")
      .select(`
        id, name, status, attended, created_at,
        invitations!guests_invitation_id_fkey(title)
      `)
      .order("created_at", { ascending: false })
      .limit(5);

    if (guestsError) {
      console.error("Error fetching recent guests:", guestsError);
    }

    const result = {
      users: recentUsers || [],
      invitations: recentInvitations || [],
      guests: recentGuests || []
    };

    console.log("Recent activities fetched successfully:", result);
    return result;

  } catch (error) {
    console.error("Error in fetchRecentActivities:", error);
    return {
      users: [],
      invitations: [],
      guests: []
    };
  }
}

/**
 * معالجة أخطاء داشبورد الأدمن
 */
export function handleAdminDashboardError(error, operation) {
  console.error(`Admin dashboard error in ${operation}:`, error);
  
  if (error.message?.includes('JWT')) {
    return 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.';
  }
  
  if (error.message?.includes('network')) {
    return 'مشكلة في الاتصال بالإنترنت. يرجى المحاولة مرة أخرى.';
  }
  
  if (error.message?.includes('permission')) {
    return 'ليس لديك صلاحية للوصول إلى هذه البيانات.';
  }
  
  return `حدث خطأ أثناء ${operation}. يرجى المحاولة مرة أخرى.`;
}
