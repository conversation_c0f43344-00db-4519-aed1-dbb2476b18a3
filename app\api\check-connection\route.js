import { NextResponse } from 'next/server';
import { supabase } from '../../../lib/supabase';

export async function GET() {
  if (!supabase) {
    return NextResponse.json({
      success: false,
      error: 'Supabase client not initialized',
    }, { status: 500 });
  }

  try {
    const { data, error } = await supabase.from('users').select('id').limit(1);
    if (error) {
      return NextResponse.json({
        success: false,
        error: error.message,
      }, { status: 500 });
    }
    return NextResponse.json({
      success: true,
      message: 'Database connection is healthy',
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message,
    }, { status: 500 });
  }
}
