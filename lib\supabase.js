import { createClient } from "@supabase/supabase-js";

// التحقق من وجود متغيرات البيئة
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// طباعة معلومات التصحيح (للتطوير فقط)
if (process.env.NODE_ENV === "development") {
  console.log(
    "Supabase URL:",
    supabaseUrl ? supabaseUrl : "Not set or invalid"
  );
  console.log("Supabase Key:", supabaseKey ? "Set" : "Not set or invalid");
}

if (!supabaseUrl || !supabaseKey) {
  throw new Error(
    "Supabase credentials are missing. Please check your .env.local file."
  );
}

// إنشاء عميل Supabase مع خيارات إضافية
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
  db: {
    schema: "public",
  },
});

// وظيفة للتحقق من الاتصال
export async function checkSupabaseConnection() {
  try {
    // التحقق من الاتصال عن طريق استعلام بسيط
    const { data, error } = await supabase
      .from("users")
      .select("count(*)", { count: "exact", head: true });

    if (error) {
      console.error("Supabase connection error:", error);
      return {
        success: false,
        error: error.message || "خطأ في الاتصال بقاعدة البيانات",
      };
    }

    return {
      success: true,
      message: "تم الاتصال بنجاح",
    };
  } catch (error) {
    console.error("Unexpected error:", error);
    return {
      success: false,
      error: error.message || "خطأ غير متوقع",
    };
  }
}
