'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { supabase } from '@/lib/supabase';
import { addGuests } from '@/app/actions';
import Navbar from '@/components/Navbar';

export default function AddGuests({ params }) {
  const invitationId = params.id;
  const [invitation, setInvitation] = useState(null);
  const [guestsPhones, setGuestsPhones] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const router = useRouter();

  useEffect(() => {
    const fetchInvitation = async () => {
      try {
        const { data, error } = await supabase
          .from('invitations')
          .select('*')
          .eq('id', invitationId)
          .single();
        
        if (error) throw error;
        setInvitation(data);
      } catch (error) {
        console.error('Error fetching invitation:', error);
        setError('حدث خطأ أثناء جلب بيانات الدعوة');
      } finally {
        setLoading(false);
      }
    };
    
    fetchInvitation();
  }, [invitationId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!guestsPhones.trim()) {
      setError('الرجاء إدخال رقم هاتف واحد على الأقل');
      return;
    }
    
    setSubmitting(true);
    setError(null);
    setSuccess(null);
    
    try {
      // تحويل النص إلى مصفوفة من أرقام الهواتف
      const phones = guestsPhones
        .split('\n')
        .map(phone => phone.trim())
        .filter(phone => phone.length > 0);
      
      if (phones.length === 0) {
        setError('الرجاء إدخال رقم هاتف واحد على الأقل');
        setSubmitting(false);
        return;
      }
      
      // إضافة المدعوين
      const result = await addGuests(invitationId, phones);
      
      if (!result.success) {
        throw new Error(result.error || 'حدث خطأ أثناء إضافة المدعوين');
      }
      
      setSuccess(`تم إضافة ${phones.length} مدعو بنجاح`);
      setGuestsPhones('');
    } catch (error) {
      console.error('Error adding guests:', error);
      setError(error.message);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <>
        <Navbar userRole="subscriber" />
        <div className="p-8 text-center">جاري تحميل البيانات...</div>
      </>
    );
  }

  return (
    <>
      <Navbar userRole="subscriber" />
      <div className="p-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold mb-2">إضافة مدعوين</h1>
            <p className="text-gray-600">للدعوة: {invitation?.title}</p>
          </div>
          <Link 
            href={`/subscriber/invitations/${invitationId}/guests`}
            className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
          >
            العودة لقائمة المدعوين
          </Link>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-bold mb-4">أدخل أرقام هواتف المدعوين</h2>
              
              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                  {error}
                </div>
              )}
              
              {success && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                  {success}
                </div>
              )}
              
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label className="block text-gray-700 text-sm font-bold mb-2">
                    أرقام الهواتف (كل رقم في سطر)
                  </label>
                  <textarea
                    value={guestsPhones}
                    onChange={(e) => setGuestsPhones(e.target.value)}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    rows={10}
                    placeholder="05xxxxxxxx&#10;05xxxxxxxx&#10;05xxxxxxxx"
                    dir="ltr"
                  ></textarea>
                </div>
                
                <div className="flex items-center justify-between mt-6">
                  <button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={submitting}
                  >
                    {submitting ? 'جاري الإضافة...' : 'إضافة المدعوين'}
                  </button>
                  
                  <button
                    type="button"
                    onClick={() => router.push(`/subscriber/invitations/${invitationId}`)}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

