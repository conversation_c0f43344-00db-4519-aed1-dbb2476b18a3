export default function Card({ title, children, footer, className = '' }) {
  return (
    <div className={`card ${className}`}>
      {title && (
        <div className="mb-4 pb-4 border-b border-neutral-200 dark:border-neutral-700">
          <h3 className="text-lg font-semibold text-neutral-900 dark:text-white">{title}</h3>
        </div>
      )}
      <div>{children}</div>
      {footer && (
        <div className="mt-4 pt-4 border-t border-neutral-200 dark:border-neutral-700">
          {footer}
        </div>
      )}
    </div>
  );
}