'use client';

import { useState } from 'react';
import { supabase } from '../../lib/supabase';
import Navbar from '../../components/Navbar';

export default function SetupStorage() {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleSetup = async () => {
    setLoading(true);
    setMessage('');
    setError('');

    try {
      // إنشاء bucket لتخزين صور القوالب
      const { data, error: bucketError } = await supabase.storage.createBucket('templates', {
        public: true,
        fileSizeLimit: 10485760, // 10MB
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/jpg', 'image/gif']
      });

      if (bucketError) {
        throw bucketError;
      }

      setMessage('تم إنشاء مساحة التخزين بنجاح!');
    } catch (error) {
      console.error('Error setting up storage:', error);
      setError(`حدث خطأ أثناء الإعداد: ${error.message || error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Navbar userRole="admin" />
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-6">إعداد مساحة التخزين</h1>
        
        {message && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {message}
          </div>
        )}
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        <div className="bg-white rounded-lg shadow p-6">
          <p className="mb-4">
            انقر على الزر أدناه لإنشاء مساحة تخزين لصور قوالب الدعوات.
          </p>
          
          <button
            onClick={handleSetup}
            disabled={loading}
            className={`${
              loading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
            } text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            {loading ? 'جاري الإعداد...' : 'إنشاء مساحة التخزين'}
          </button>
        </div>
      </div>
    </>
  );
}