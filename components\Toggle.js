export default function Toggle({
  label,
  id,
  checked,
  onChange,
  disabled = false,
  className = '',
}) {
  return (
    <div className={`flex items-center ${className}`}>
      <button
        type="button"
        id={id}
        role="switch"
        aria-checked={checked}
        disabled={disabled}
        onClick={() => !disabled && onChange(!checked)}
        className={`
          relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer 
          transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500
          ${checked ? 'bg-primary-600' : 'bg-neutral-200 dark:bg-neutral-700'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <span
          className={`
            pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 
            transition ease-in-out duration-200
            ${checked ? 'translate-x-5 rtl:-translate-x-5' : 'translate-x-0'}
          `}
        />
      </button>
      {label && (
        <label htmlFor={id} className="mr-3 text-sm font-medium text-neutral-700 dark:text-neutral-300 cursor-pointer">
          {label}
        </label>
      )}
    </div>
  );
}