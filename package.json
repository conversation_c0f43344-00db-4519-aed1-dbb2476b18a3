{"name": "invitation-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "apexcharts": "^4.7.0", "bcryptjs": "^3.0.2", "canvas-confetti": "^1.9.3", "dotenv": "^16.5.0", "framer-motion": "^12.16.0", "html-to-image": "^1.11.13", "html5-qrcode": "^2.3.8", "jsqr": "^1.4.0", "next": "14.1.0", "qr-scanner": "^1.4.2", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "twilio": "^5.6.1", "uuid": "^11.1.0"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17"}}