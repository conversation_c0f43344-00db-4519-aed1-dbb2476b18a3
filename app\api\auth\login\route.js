import { NextResponse } from 'next/server';
import { supabase } from '../../../../lib/supabase';

export async function POST(request) {
  try {
    // استخراج بيانات الاعتماد
    const credentials = await request.json();
    console.log('Login request received:', { phone: credentials.phone, passwordLength: credentials.password?.length });

    if (!credentials || !credentials.phone || !credentials.password) {
      console.error('Missing credentials');
      return NextResponse.json({ 
        success: false, 
        error: 'بيانات الاعتماد غير مكتملة' 
      }, { status: 400 });
    }

    const { phone, password } = credentials;
    
    console.log('Attempting to find user with phone:', phone);
    // التحقق من وجود المستخدم أولاً (للتشخيص)
    const { data: userCheck, error: userCheckError } = await supabase
      .from('users')
      .select('*')
      .eq('phone', phone);
    
    console.log('User check result:', { 
      found: !!userCheck?.length, 
      count: userCheck?.length || 0,
      error: userCheckError ? userCheckError.message : null,
      rawData: userCheck
    });
    
    if (userCheckError) {
      console.error('Error checking user:', userCheckError);
      return NextResponse.json({ 
        success: false, 
        error: 'خطأ في قاعدة البيانات',
        details: userCheckError.message
      }, { status: 500 });
    }
    
    if (!userCheck || userCheck.length === 0) {
      console.log('No user found with phone:', phone);
      return NextResponse.json({ 
        success: false, 
        error: 'بيانات الدخول غير صحيحة' 
      }, { status: 401 });
    }
    
    // التحقق من كلمة المرور
    const user = userCheck.find(u => u.password === password);
    
    if (!user) {
      console.log('Password mismatch for user with phone:', phone);
      return NextResponse.json({ 
        success: false, 
        error: 'بيانات الدخول غير صحيحة' 
      }, { status: 401 });
    }
    
    // إرجاع بيانات المستخدم بدون كلمة المرور
    const { password: _, ...userWithoutPassword } = user;
    
    console.log('User authenticated successfully:', { id: user.id, role: user.role });
    
    return NextResponse.json({ 
      success: true, 
      user: userWithoutPassword
    });
  } catch (error) {
    console.error('Unexpected error in login API:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'حدث خطأ غير متوقع',
      details: error.message
    }, { status: 500 });
  }
}





