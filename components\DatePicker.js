'use client';

import { useState } from 'react';

export default function DatePicker({
  label,
  id,
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  className = '',
  minDate,
  maxDate,
  ...props
}) {
  const [focused, setFocused] = useState(false);

  // تنسيق التاريخ للعرض
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label htmlFor={id} className="label">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}
      <div className="relative">
        <input
          id={id}
          name={id}
          type={focused ? 'datetime-local' : 'text'}
          value={focused ? value : formatDate(value)}
          onChange={(e) => onChange(e.target.value)}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          disabled={disabled}
          min={minDate}
          max={maxDate}
          className={`input ${error ? 'border-red-500 focus:ring-red-500' : ''} ${disabled ? 'bg-neutral-100 cursor-not-allowed' : ''}`}
          required={required}
          {...props}
        />
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-neutral-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
      </div>
      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
}