import { supabase } from '@/lib/supabase';
import Link from 'next/link';

export default async function AdminDashboard() {
  // استخدام Server Component للحصول على البيانات
  const { data: subscribers } = await supabase
    .from('users')
    .select('*')
    .eq('role', 'subscriber');
  
  const { data: invitations } = await supabase
    .from('invitations')
    .select('*');
  
  const { data: guests } = await supabase
    .from('guests')
    .select('*');
  
  const acceptedGuests = guests?.filter(guest => guest.status === 'accepted') || [];
  const attendedGuests = guests?.filter(guest => guest.attended) || [];

  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold mb-8">لوحة تحكم المدير</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-2">المشتركين</h2>
          <p className="text-3xl font-bold">{subscribers?.length || 0}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-2">الدعوات</h2>
          <p className="text-3xl font-bold">{invitations?.length || 0}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-2">الحضور المؤكد</h2>
          <p className="text-3xl font-bold">{attendedGuests?.length || 0} / {acceptedGuests?.length || 0}</p>
        </div>
      </div>
      
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">المشتركين</h2>
          <Link 
            href="/admin/subscribers/add" 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            إضافة مشترك جديد
          </Link>
        </div>
        
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الهاتف</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {subscribers?.map((subscriber) => (
                <tr key={subscriber.id}>
                  <td className="px-6 py-4 whitespace-nowrap">{subscriber.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap">{subscriber.phone}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {new Date(subscriber.created_at).toLocaleDateString('ar-EG')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Link 
                      href={`/admin/subscribers/${subscriber.id}`}
                      className="text-blue-600 hover:text-blue-900 ml-4"
                    >
                      عرض
                    </Link>
                    <Link 
                      href={`/admin/subscribers/${subscriber.id}/edit`}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      تعديل
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}