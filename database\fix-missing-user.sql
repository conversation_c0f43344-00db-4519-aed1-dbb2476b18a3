-- إصلاح مشكلة المستخدم المفقود
-- هذا السكريبت سيتحقق من وجود المستخدم وإنشاؤه إذا لم يكن موجوداً

-- 1. التحقق من وجود المستخدم المحدد
SELECT 
    'Checking for user:' as check_type,
    id,
    name,
    phone,
    role,
    is_active
FROM public.users 
WHERE id = 'f2ccc0c9-c8f1-4a17-af15-8e8a93bda15f';

-- 2. إذا لم يكن المستخدم موجوداً، إنشاء مستخدم بديل
-- (يجب تحديث localStorage بعد ذلك)
INSERT INTO public.users (id, name, phone, password, role, is_active) 
VALUES (
    'f2ccc0c9-c8f1-4a17-af15-8e8a93bda15f',
    'مستخدم مستعاد',
    '0500000999',
    '123456',
    'subscriber',
    true
)
ON CONFLICT (id) DO UPDATE SET
    is_active = true,
    name = COALESCE(EXCLUDED.name, users.name),
    phone = COALESCE(EXCLUDED.phone, users.phone);

-- 3. التحقق من نجاح العملية
SELECT 
    'User restoration result:' as result_type,
    id,
    name,
    phone,
    role,
    is_active,
    created_at
FROM public.users 
WHERE id = 'f2ccc0c9-c8f1-4a17-af15-8e8a93bda15f';

-- 4. إنشاء مستخدمين تجريبيين إضافيين للاختبار
INSERT INTO public.users (name, phone, password, role, is_active) 
VALUES 
    ('مستخدم تجريبي 1', '0500000001', '123456', 'subscriber', true),
    ('مستخدم تجريبي 2', '0500000002', '123456', 'subscriber', true),
    ('مدير النظام', '0500000000', '123456', 'admin', true)
ON CONFLICT (phone) DO UPDATE SET
    is_active = true,
    role = EXCLUDED.role;

-- 5. عرض جميع المستخدمين النشطين
SELECT 
    'All active users:' as list_type,
    id,
    name,
    phone,
    role,
    is_active
FROM public.users 
WHERE is_active = true
ORDER BY created_at DESC;
