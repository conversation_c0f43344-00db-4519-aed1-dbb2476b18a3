-- حل سريع لإضافة العم<PERSON><PERSON> attended بدون قيود معقدة
-- هذا الحل يركز على حل المشكلة الأساسية فقط

-- 1. إض<PERSON><PERSON><PERSON> العمود attended إذا لم يكن موجوداً
ALTER TABLE public.guests ADD COLUMN IF NOT EXISTS attended BOOLEAN DEFAULT false;

-- 2. إض<PERSON><PERSON><PERSON> العمود attended_at إذا لم يكن موجوداً  
ALTER TABLE public.guests ADD COLUMN IF NOT EXISTS attended_at TIMESTAMP WITH TIME ZONE;

-- 3. تحديث أي قيم status فارغة أو غير صحيحة
UPDATE public.guests 
SET status = 'pending' 
WHERE status IS NULL OR status = '' OR status NOT IN ('pending', 'accepted', 'declined');

-- 4. إعادة تحميل schema cache لـ Supabase
NOTIFY pgrst, 'reload schema';

-- 5. التحقق من نجاح العملية
SELECT 
    'Success! Columns added:' as message,
    COUNT(*) as total_guests,
    COUNT(CA<PERSON> WH<PERSON> attended IS NOT NULL THEN 1 END) as guests_with_attended_column
FROM public.guests;
