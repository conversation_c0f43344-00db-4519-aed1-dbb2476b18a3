<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار QR Code - تشخيص المشكلة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .debug-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #dc3545;
        }
        .debug-section h3 {
            color: #dc3545;
            margin-top: 0;
        }
        .solution {
            background: #d4edda;
            border-left: 5px solid #28a745;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .solution h4 {
            color: #28a745;
            margin-top: 0;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .step {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .step h4 {
            color: #856404;
            margin-top: 0;
        }
        .btn {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
            margin: 10px 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="text-align: center; margin-bottom: 30px;">
            <h1>🔍 تشخيص مشكلة QR Code</h1>
            <p>المشكلة: QR Code لا يظهر بعد قبول الدعوة</p>
        </div>

        <div class="debug-section">
            <h3>🐛 تحليل المشكلة</h3>
            <p><strong>المشكلة المحتملة:</strong></p>
            <ul>
                <li>الـ triggers في قاعدة البيانات قد لا تعمل بشكل صحيح</li>
                <li>التوقيت بين إدراج البيانات وجلب QR Code</li>
                <li>مشكلة في مكتبة QR Code helpers</li>
                <li>خطأ في JavaScript console</li>
            </ul>
        </div>

        <div class="solution">
            <h4>✅ الحلول المطبقة</h4>
            <ol>
                <li><strong>إنشاء Database Triggers:</strong> تم إنشاء functions و triggers لإنتاج QR Code تلقائياً</li>
                <li><strong>Fallback Mechanism:</strong> إذا لم يجد QR Code في قاعدة البيانات، ينشئه محلياً</li>
                <li><strong>تحسين التوقيت:</strong> إضافة انتظار 1 ثانية للسماح للـ trigger بالعمل</li>
                <li><strong>Console Logging:</strong> إضافة logs للتشخيص</li>
            </ol>
        </div>

        <div class="step">
            <h4>📋 خطوات الاختبار</h4>
            <ol>
                <li>افتح Developer Tools (F12)</li>
                <li>اذهب إلى Console tab</li>
                <li>افتح صفحة دعوة: <code>/invitation/[id]</code></li>
                <li>اقبل الدعوة وراقب الـ console</li>
                <li>ابحث عن هذه الرسائل:</li>
            </ol>
            
            <div class="code-block">
Updated guest data: {...}
QR code not found in database, generating locally...
QR Code generated successfully!
            </div>
        </div>

        <div class="debug-section">
            <h3>🔧 إذا لم يعمل QR Code</h3>
            <p><strong>تحقق من:</strong></p>
            <ul>
                <li>هل يظهر خطأ في Console؟</li>
                <li>هل البيانات تُحفظ في قاعدة البيانات؟</li>
                <li>هل الـ state يتحدث بشكل صحيح؟</li>
                <li>هل مكتبة qrcode مثبتة؟</li>
            </ul>
        </div>

        <div class="solution">
            <h4>🛠️ حلول إضافية</h4>
            
            <div class="step">
                <h4>1. تحقق من قاعدة البيانات</h4>
                <div class="code-block">
SELECT id, name, phone, status, qr_code 
FROM guests 
WHERE invitation_id = 'YOUR_INVITATION_ID' 
ORDER BY created_at DESC;
                </div>
            </div>

            <div class="step">
                <h4>2. اختبار QR Code محلياً</h4>
                <div class="code-block">
// في Console المتصفح
import('@/lib/qrCodeHelpers').then(helpers => {
  const qr = helpers.generateGuestQRCode('test-id', '0501234567', 'اسم تجريبي');
  console.log('QR Code:', qr);
});
                </div>
            </div>

            <div class="step">
                <h4>3. فرض إعادة إنشاء QR Code</h4>
                <p>إذا كان الضيف موجود بالفعل بدون QR Code:</p>
                <div class="code-block">
UPDATE guests 
SET qr_code = NULL 
WHERE invitation_id = 'YOUR_INVITATION_ID' 
AND phone = 'GUEST_PHONE';
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn btn-success" onclick="window.location.reload()">
                🔄 إعادة تحميل الصفحة
            </button>
            <button class="btn" onclick="window.close()">
                ❌ إغلاق
            </button>
        </div>

        <div style="background: #e9ecef; padding: 20px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h4>📞 للمساعدة</h4>
            <p>إذا استمرت المشكلة، تحقق من:</p>
            <ul style="text-align: right; display: inline-block;">
                <li>Console errors في المتصفح</li>
                <li>Network tab للتأكد من API calls</li>
                <li>Database logs في Supabase</li>
                <li>State management في React</li>
            </ul>
        </div>
    </div>

    <script>
        // إضافة بعض التفاعل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 صفحة تشخيص QR Code جاهزة');
            console.log('تأكد من فتح Developer Tools لمراقبة الأخطاء');
        });
    </script>
</body>
</html>
