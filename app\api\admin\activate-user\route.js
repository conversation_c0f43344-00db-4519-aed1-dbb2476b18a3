import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function POST(request) {
  try {
    const { userId } = await request.json();
    const supabase = createRouteHandlerClient({ cookies });

    // تحديث حالة المستخدم إلى "مفعل"
    const { data, error } = await supabase
      .from("users")
      .update({ is_active: true, activated_at: new Date().toISOString() })
      .eq("id", userId);

    if (error) throw error;

    // حذف الإشعار
    await supabase
      .from("notifications")
      .delete()
      .eq("user_id", userId)
      .eq("type", "activation_request");

    return NextResponse.json({
      success: true,
      message: "تم تفعيل حساب المستخدم بنجاح",
    });
  } catch (error) {
    console.error("Error activating user:", error);
    return NextResponse.json(
      {
        success: false,
        error: "حدث خطأ أثناء تفعيل حساب المستخدم",
      },
      { status: 500 }
    );
  }
}
