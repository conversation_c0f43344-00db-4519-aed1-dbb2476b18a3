-- إصلا<PERSON> خطأ قيد التحقق من status في جدول guests
-- المشكلة: هناك بيانات موجودة لا تتطابق مع القيود الجديدة

-- 1. أولاً، دعنا نرى ما هي القيم الموجودة حالياً
SELECT 
    'Current status values in guests table:' as info,
    status,
    COUNT(*) as count
FROM public.guests 
GROUP BY status
ORDER BY count DESC;

-- 2. تحديث القيم غير الصحيحة إلى قيم صحيحة
-- تحديث أي قيم فارغة أو NULL إلى 'pending'
UPDATE public.guests 
SET status = 'pending' 
WHERE status IS NULL OR status = '';

-- تحديث أي قيم أخرى غير صحيحة
-- إذا كانت هناك قيم مثل 'confirmed' نحولها إلى 'accepted'
UPDATE public.guests 
SET status = 'accepted' 
WHERE status NOT IN ('pending', 'accepted', 'declined') 
    AND status IS NOT NULL 
    AND status != '';

-- 3. التحقق من القيم بعد التحديث
SELECT 
    'Status values after cleanup:' as info,
    status,
    COUNT(*) as count
FROM public.guests 
GROUP BY status
ORDER BY count DESC;

-- 4. التأكد من أن جميع القيم صحيحة الآن
SELECT 
    'Invalid status values (should be empty):' as info,
    status,
    COUNT(*) as count
FROM public.guests 
WHERE status NOT IN ('pending', 'accepted', 'declined')
GROUP BY status;

-- 5. الآن يمكننا إضافة قيد التحقق بأمان
-- أولاً نحذف القيد إذا كان موجوداً
ALTER TABLE public.guests DROP CONSTRAINT IF EXISTS guests_status_check;

-- ثم نضيف القيد الجديد
ALTER TABLE public.guests 
ADD CONSTRAINT guests_status_check 
CHECK (status IN ('pending', 'accepted', 'declined'));

-- 6. إضافة العمود attended إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'guests' 
        AND column_name = 'attended'
    ) THEN
        ALTER TABLE public.guests ADD COLUMN attended BOOLEAN DEFAULT false;
        RAISE NOTICE 'تم إضافة العمود attended';
    ELSE
        RAISE NOTICE 'العمود attended موجود بالفعل';
    END IF;
END $$;

-- 7. إضافة العمود attended_at إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'guests' 
        AND column_name = 'attended_at'
    ) THEN
        ALTER TABLE public.guests ADD COLUMN attended_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'تم إضافة العمود attended_at';
    ELSE
        RAISE NOTICE 'العمود attended_at موجود بالفعل';
    END IF;
END $$;

-- 8. تحديث البيانات الموجودة للتوافق
-- إذا كان attended_at موجود، نحدث attended إلى true
UPDATE public.guests 
SET attended = true 
WHERE attended_at IS NOT NULL AND (attended IS NULL OR attended = false);

-- 9. إضافة تعليقات توضيحية
COMMENT ON COLUMN public.guests.status IS 'حالة الرد على الدعوة (يحددها الضيف): pending = لم يرد بعد، accepted = قبل الدعوة، declined = اعتذر عن الحضور';
COMMENT ON COLUMN public.guests.attended IS 'حالة الحضور الفعلي (يحددها المستخدم/صاحب الدعوة): true = حضر فعلاً، false = لم يحضر';
COMMENT ON COLUMN public.guests.attended_at IS 'وقت تسجيل الحضور الفعلي';

-- 10. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_guests_status ON public.guests(status);
CREATE INDEX IF NOT EXISTS idx_guests_attended ON public.guests(attended);
CREATE INDEX IF NOT EXISTS idx_guests_attended_at ON public.guests(attended_at);

-- 11. عرض الهيكل النهائي
SELECT 
    'Final guests table structure:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'guests'
ORDER BY ordinal_position;

-- 12. عرض إحصائيات نهائية
SELECT 
    'Final statistics:' as info,
    COUNT(*) as total_guests,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_responses,
    COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_invitations,
    COUNT(CASE WHEN status = 'declined' THEN 1 END) as declined_invitations,
    COUNT(CASE WHEN attended = true THEN 1 END) as actually_attended,
    COUNT(CASE WHEN attended_at IS NOT NULL THEN 1 END) as attended_with_timestamp
FROM public.guests;

-- 13. إعادة تحميل schema cache
NOTIFY pgrst, 'reload schema';
