import { Cairo } from 'next/font/google';
import './globals.css';

const cairo = Cairo({ 
  subsets: ['arabic'],
  weight: ['300', '400', '500', '700'],
  display: 'swap'
});

export default function RootLayout({ children }) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>نظام إدارة الدعوات</title>
      </head>
      <body className={`${cairo.className} bg-neutral-50 dark:bg-neutral-900 text-neutral-900 dark:text-white`}>
        {children}
      </body>
    </html>
  );
}
