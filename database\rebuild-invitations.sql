-- إعادة بناء جدول الدعوات
DROP TABLE IF EXISTS public.invitations CASCADE;

-- إن<PERSON>اء جدول الدعوات من جديد
CREATE TABLE public.invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_date TIMESTAMP WITH TIME ZONE,
    location TEXT,
    status VARCHAR(50) DEFAULT 'draft',
    template_id UUID,
    additional_data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- تمكين RLS
ALTER TABLE public.invitations ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جميع السياسات القديمة
DROP POLICY IF EXISTS "Enable read for users" ON public.invitations;
DROP POLICY IF EXISTS "Enable insert for users" ON public.invitations;
DROP POLICY IF EXISTS "Enable update for users" ON public.invitations;
DROP POLICY IF EXISTS "Enable delete for users" ON public.invitations;
DROP POLICY IF EXISTS "Invitations Policy" ON public.invitations;
DROP POLICY IF EXISTS "Users can view their own invitations" ON public.invitations;
DROP POLICY IF EXISTS "Users can create their own invitations" ON public.invitations;
DROP POLICY IF EXISTS "Users can update their own invitations" ON public.invitations;
DROP POLICY IF EXISTS "Users can delete their own invitations" ON public.invitations;
DROP POLICY IF EXISTS "Admin can manage all invitations" ON public.invitations;

-- إنشاء سياسات جديدة
CREATE POLICY "Allow all operations for authenticated users" ON public.invitations FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- منح صلاحيات للمستخدمين المصادق عليهم
GRANT ALL ON public.invitations TO authenticated;
GRANT USAGE ON SEQUENCE invitations_id_seq TO authenticated;
