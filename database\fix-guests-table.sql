-- حذف السياسات الحالية
DROP POLICY IF EXISTS "Allow insert guests" ON public.guests;
DROP POLICY IF EXISTS "Allow update guests" ON public.guests;
DROP POLICY IF EXISTS "Allow select guests" ON public.guests;

-- حذ<PERSON> الجدول وإعادة إنشائه
DROP TABLE IF EXISTS public.guests CASCADE;

CREATE TABLE IF NOT EXISTS public.guests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invitation_id UUID NOT NULL REFERENCES public.invitations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    notes TEXT DEFAULT '',
    number_of_companions INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_guest_per_invitation UNIQUE (invitation_id, phone)
);

-- م<PERSON><PERSON> الصلاحيات
GRANT ALL ON public.guests TO authenticated;
GRANT ALL ON public.guests TO anon;
GRANT USAGE ON SEQUENCE guests_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE guests_id_seq TO anon;

-- تمكين RLS
ALTER TABLE public.guests ENABLE ROW LEVEL SECURITY;

-- إضافة السياسات الجديدة
CREATE POLICY "Enable read access for all users" ON public.guests
    FOR SELECT TO public
    USING (true);

CREATE POLICY "Enable insert access for all users" ON public.guests
    FOR INSERT TO public
    WITH CHECK (true);

CREATE POLICY "Enable update access for all users" ON public.guests
    FOR UPDATE TO public
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow public insert" ON public.users
    FOR INSERT TO authenticated
    WITH CHECK (
        -- المشرفون يمكنهم إضافة مستخدمين بأي دور
        (auth.jwt() ->> 'role' = 'admin')
        OR
        -- المستخدمون المسجلون الآخرون يمكنهم إضافة مشتركين فقط إذا كان التسجيل العام مغلقاً
        (
            NOT EXISTS (SELECT 1 FROM system_settings WHERE allow_new_registrations = true)
            AND auth.uid() IS NOT NULL -- تأكيد أن المستخدم مسجل
            AND role = 'subscriber'
        )
    );
