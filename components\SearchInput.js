export default function SearchInput({
  value,
  onChange,
  placeholder = 'بحث...',
  className = '',
  onSubmit,
}) {
  return (
    <div className={`relative ${className}`}>
      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <svg className="w-5 h-5 text-neutral-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
        </svg>
      </div>
      <form onSubmit={onSubmit ? (e) => { e.preventDefault(); onSubmit(); } : undefined}>
        <input
          type="text"
          value={value}
          onChange={onChange}
          className="input pr-10"
          placeholder={placeholder}
        />
      </form>
    </div>
  );
}