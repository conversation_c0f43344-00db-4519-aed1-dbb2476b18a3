// lib/twilio.js
import twilio from 'twilio';

// إعداد Twilio API
const accountSid = process.env.TWILIO_ACCOUNT_SID || '**********************************';
const authToken = process.env.TWILIO_AUTH_TOKEN || 'ae33e59158b8c750c13be6569cb492ef';
const client = twilio(accountSid, authToken);

// دالة لإرسال دعوة عبر واتساب بدون استخدام القالب
export async function sendWhatsAppInvitation(to, invitationData) {
  try {
    // تنسيق رقم الهاتف
    const formattedPhone = formatPhoneNumber(to);
    
    // إنشاء رابط الدعوة
    const invitationLink = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/invitation/${invitationData.id}`;
    
    // تنسيق التاريخ والوقت
    const eventDate = new Date(invitationData.event_date);
    const formattedDate = eventDate.toLocaleDateString('ar-SA');
    const formattedTime = eventDate.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    
    // إنشاء نص الرسالة
    const messageBody = `
دعوة: ${invitationData.title}
التاريخ: ${formattedDate}
الوقت: ${formattedTime}
المكان: ${invitationData.location}
${invitationData.description ? '\n' + invitationData.description : ''}

للرد على الدعوة، يرجى الضغط على الرابط:
${invitationLink}
    `;
    
    // إرسال الرسالة
    const result = await client.messages.create({
      body: messageBody,
      from: 'whatsapp:+14155238886',
      to: formattedPhone
    });
    
    return { success: true, sid: result.sid };
  } catch (error) {
    console.error('Error sending WhatsApp message:', error);
    return { success: false, error: error.message };
  }
}

// دالة لإرسال دعوة زفاف عبر واتساب باستخدام القالب المعتمد
export async function sendWeddingInvitationWithTemplate(to, invitationData, templateSid = null) {
  try {
    // تنسيق رقم الهاتف
    let formattedPhone = formatPhoneNumber(to);
    formattedPhone = formattedPhone.replace('whatsapp:', '');
    
    // تنسيق التاريخ والوقت
    const eventDate = new Date(invitationData.event_date);
    const formattedDate = eventDate.toLocaleDateString('ar-SA', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
    const formattedTime = eventDate.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    
    // استخراج بيانات العريس والعروس من الوصف إذا كانت متاحة
    let groomName = invitationData.groom_name || "العريس";
    let brideName = invitationData.bride_name || "العروسة";
    
    // إعداد متغيرات القالب
    const variables = {
      "1": groomName,
      "2": brideName,
      "3": formattedDate,
      "4": formattedTime,
      "5": invitationData.location || "قاعة الاحتفالات"
    };
    
    // استخدام معرف القالب المعتمد من Twilio
    const templateName = templateSid || process.env.TWILIO_WEDDING_TEMPLATE_SID || 'HX85b0700d7dbee5e0d805e311ac02330d';
    
    // إرسال الرسالة باستخدام القالب
    const result = await sendTemplateMessage(client, formattedPhone, templateName, variables);
    
    // تسجيل معلومات الدعوة في قاعدة البيانات
    if (result.success) {
      console.log('Wedding invitation template message sent successfully with SID:', result.sid);
    }
    
    return result;
  } catch (error) {
    console.error('Error sending wedding invitation template message:', error);
    return { success: false, error: error.message };
  }
}

// دالة لإرسال دعوة زفاف عبر واتساب باستخدام رسالة تفاعلية
export async function sendWhatsAppInvitationWithInteractiveMessage(to, invitationData) {
  try {
    // تنسيق رقم الهاتف
    let formattedPhone = formatPhoneNumber(to);
    
    // التأكد من أن الرقم يبدأ بـ whatsapp:
    if (!formattedPhone.startsWith('whatsapp:')) {
      formattedPhone = 'whatsapp:' + formattedPhone;
    }
    
    // تنسيق التاريخ والوقت
    const eventDate = new Date(invitationData.event_date);
    const formattedDate = eventDate.toLocaleDateString('ar-SA', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
    const formattedTime = eventDate.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    
    // استخراج بيانات العريس والعروس
    let groomName = invitationData.groom_name || "أحمد محمد عبد الرحمن";
    let brideName = invitationData.bride_name || "سارة مصطفى حسن";
    
    // إنشاء رابط الدعوة
    const invitationLink = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/invitation/${invitationData.id}?phone=${to.replace('+', '')}`;
    
    // إنشاء نص الرسالة
    const messageBody = `
عزيزنا الضيف الكريم،

بكل حب ومودة، ندعوك لمشاركتنا فرحتنا في حفل زفاف:
العريس: ${groomName}
إلى كريمتنا: ${brideName}

📅 التاريخ: ${formattedDate}
🕒 الساعة: ${formattedTime}
📍 المكان: ${invitationData.location || "قاعة الاحتفالات"}

حضوركم سعدنا ويُزيد من بهجة مناسبتنا.
للتأكيد على حضوركم، يرجى الضغط على الرد التالي:

📞 للاستفسارات، يرجى التواصل معانا عبر المنصة.

${invitationLink}
    `;
    
    // إرسال الرسالة مع أزرار تفاعلية
    const result = await client.messages.create({
      body: messageBody,
      from: 'whatsapp:+14155238886',
      to: formattedPhone,
      persistentAction: [
        `action=reply&suggested_response=تأكيد الحضور&postback_data=accept_${invitationData.id}`,
        `action=reply&suggested_response=اعتذار&postback_data=decline_${invitationData.id}`
      ]
    });
    
    console.log('Interactive message sent with SID:', result.sid);
    return { success: true, sid: result.sid };
  } catch (error) {
    console.error('Error sending WhatsApp interactive message:', error);
    return { success: false, error: error.message };
  }
}

// دالة لتنسيق رقم الهاتف
function formatPhoneNumber(phone) {
  // إزالة أي مسافات أو رموز
  let formattedPhone = phone.replace(/\s+/g, '');
  
  // التأكد من أن الرقم يبدأ بـ +
  if (!formattedPhone.startsWith('+')) {
    // إذا كان الرقم يبدأ بـ 0، نستبدله بـ +966 (للسعودية)
    if (formattedPhone.startsWith('0')) {
      formattedPhone = '+966' + formattedPhone.substring(1);
    } else {
      // وإلا نضيف + في البداية
      formattedPhone = '+' + formattedPhone;
    }
  }
  
  // إذا كان الرقم يبدأ بـ +20، فهو رقم مصري
  if (formattedPhone.startsWith('+20')) {
    // لا نقوم بأي تغيير
  } 
  // إذا كان الرقم يبدأ بـ +966، فهو رقم سعودي
  else if (formattedPhone.startsWith('+966')) {
    // لا نقوم بأي تغيير
  }
  
  return `whatsapp:${formattedPhone}`;
}

export default client;








