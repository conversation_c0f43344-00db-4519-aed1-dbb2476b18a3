"use client";

import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import Image from "next/image";
import {
  FiCalendar,
  FiMapPin,
  FiClock,
  FiCheck,
  FiX,
  FiMessageSquare,
  FiDownload,
  FiShare2,
  FiHeart,
  FiGift,
} from "react-icons/fi";
import { BsQrCodeScan } from "react-icons/bs";
import confetti from "canvas-confetti";
import dynamic from "next/dynamic";

export default function InvitationPage({ params }) {
  const [invitation, setInvitation] = useState(null);
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  const [notes, setNotes] = useState("");
  const [responseStatus, setResponseStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [additionalInfo, setAdditionalInfo] = useState(null);
  const [guestData, setGuestData] = useState(null);
  const [qrCodeImage, setQrCodeImage] = useState(null);
  const [showQRModal, setShowQRModal] = useState(false);

  useEffect(() => {
    const fetchInvitation = async () => {
      try {
        const { data, error } = await supabase
          .from("invitations")
          .select("*")
          .eq("id", params.id)
          .single();

        if (error) throw error;

        if (!data) {
          setError("لا يمكن العثور على الدعوة المطلوبة");
          return;
        }

        setInvitation(data);

        // استخراج المعلومات الإضافية من الوصف إذا كانت موجودة
        try {
          if (data.additional_data) {
            setAdditionalInfo(data.additional_data);
          } else if (data.description && data.description.startsWith("{")) {
            const jsonEndIndex = data.description.indexOf("}") + 1;
            const jsonStr = data.description.substring(0, jsonEndIndex);
            const additionalData = JSON.parse(jsonStr);
            setAdditionalInfo(additionalData);
          }
        } catch (err) {
          console.error("Error parsing additional info:", err);
        }
      } catch (err) {
        console.error("Error fetching invitation:", err);
        setError("لا يمكن العثور على الدعوة المطلوبة");
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [params.id]);

  const handleResponse = async (status) => {
    try {
      setSubmitting(true);
      setError(null);

      // التحقق من المدخلات
      if (!phone) {
        setError("يرجى إدخال رقم الهاتف");
        return;
      }

      if (!name) {
        setError("يرجى إدخال الاسم");
        return;
      }

      if (!params.id) {
        setError("معرف الدعوة غير صالح");
        return;
      }

      // تنظيف البيانات
      const guestData = {
        invitation_id: params.id,
        name: name.trim(),
        phone: phone.replace(/\s+/g, "").replace(/^0/, "+966"),
        status: status,
        notes: notes?.trim() || "",
        number_of_companions: 0,
      };

      // محاولة إضافة الضيف
      const { data, error } = await supabase
        .from("guests")
        .insert([guestData])
        .select()
        .single();

      if (error) {
        // إذا كان الضيف موجود، قم بالتحديث
        if (error.code === "23505") {
          const { data: updateData, error: updateError } = await supabase
            .from("guests")
            .update({
              name: guestData.name,
              status: guestData.status,
              notes: guestData.notes,
              updated_at: new Date().toISOString(),
            })
            .eq("invitation_id", params.id)
            .eq("phone", guestData.phone)
            .select()
            .single();

          if (updateError) throw updateError;
        } else {
          throw error;
        }
      }

      // إظهار رسالة النجاح
      setSuccess(
        status === "accepted" ? "شكراً لتأكيد حضورك!" : "شكراً لإبلاغنا"
      );
      setResponseStatus(status);

      // إذا تم قبول الدعوة، إنشاء QR Code
      if (status === "accepted") {
        try {
          // جلب بيانات الضيف المحدثة مع QR Code
          const { data: updatedGuest, error: fetchError } = await supabase
            .from("guests")
            .select("*")
            .eq("invitation_id", params.id)
            .eq("phone", phone)
            .single();

          if (!fetchError && updatedGuest && updatedGuest.qr_code) {
            setGuestData(updatedGuest);

            // إنشاء صورة QR Code
            const { generateQRCodeImage } = await import("@/lib/qrCodeHelpers");
            const qrImage = await generateQRCodeImage(updatedGuest.qr_code, {
              width: 300,
              color: {
                dark: "#1a365d",
                light: "#ffffff",
              },
            });
            setQrCodeImage(qrImage);
          }
        } catch (qrError) {
          console.error("Error generating QR code:", qrError);
        }

        // تأثيرات النجاح
        confetti({
          particleCount: 100,
          spread: 70,
          origin: { y: 0.6 },
        });
      }
    } catch (error) {
      console.error("Error:", error);
      setError(error.message || "عذراً، حدث خطأ أثناء تحديث حالة الحضور");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <div className="text-red-500 text-4xl mb-4">
            <FiX className="inline-block" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">عذراً</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  // دوال QR Code
  const showQRCode = () => {
    setShowQRModal(true);
  };

  const closeQRModal = () => {
    setShowQRModal(false);
  };

  const downloadQRCode = () => {
    if (qrCodeImage && guestData) {
      const link = document.createElement("a");
      link.download = `qr-code-${guestData.name || guestData.phone}.png`;
      link.href = qrCodeImage;
      link.click();
    }
  };

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        {/* Header محسن */}
        <div className="relative overflow-hidden">
          {/* خلفية متدرجة مع تأثيرات */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"></div>
          <div className="absolute inset-0 bg-black opacity-20"></div>

          {/* عناصر ديكورية */}
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
            <div className="absolute top-10 left-10 w-20 h-20 bg-white opacity-10 rounded-full"></div>
            <div className="absolute top-32 right-16 w-16 h-16 bg-white opacity-10 rounded-full"></div>
            <div className="absolute bottom-20 left-20 w-12 h-12 bg-white opacity-10 rounded-full"></div>
            <div className="absolute bottom-10 right-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>
          </div>

          <div className="relative px-4 py-16 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center">
              <div className="mb-8">
                <FiHeart className="mx-auto h-16 w-16 text-white mb-4" />
                <h1 className="text-4xl md:text-6xl font-bold text-white mb-4 leading-tight">
                  {invitation.title}
                </h1>
                {additionalInfo && (
                  <div className="text-xl md:text-2xl text-blue-100">
                    {additionalInfo.groom_name && additionalInfo.bride_name && (
                      <p className="font-medium">
                        {additionalInfo.groom_name} &{" "}
                        {additionalInfo.bride_name}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* محتوى الصفحة الرئيسي */}
        <div className="max-w-4xl mx-auto px-4 py-12 sm:px-6 lg:px-8">
          {/* بطاقة تفاصيل الدعوة */}
          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden mb-8">
            <div className="p-8 lg:p-12">
              {/* تفاصيل الحدث */}
              <div className="grid md:grid-cols-2 gap-8 mb-8">
                {invitation.event_date && (
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6">
                    <div className="flex items-center mb-4">
                      <div className="bg-blue-500 rounded-full p-3 ml-4">
                        <FiCalendar className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-800">
                          التاريخ والوقت
                        </h3>
                      </div>
                    </div>
                    <div className="text-gray-700">
                      <div className="text-xl font-semibold mb-2">
                        {new Date(invitation.event_date).toLocaleDateString(
                          "ar-SA",
                          {
                            weekday: "long",
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          }
                        )}
                      </div>
                      <div className="text-lg text-blue-600 font-medium">
                        {new Date(invitation.event_date).toLocaleTimeString(
                          "ar-SA",
                          {
                            hour: "2-digit",
                            minute: "2-digit",
                          }
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {invitation.location && (
                  <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6">
                    <div className="flex items-center mb-4">
                      <div className="bg-green-500 rounded-full p-3 ml-4">
                        <FiMapPin className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-800">
                          المكان
                        </h3>
                      </div>
                    </div>
                    <div className="text-gray-700">
                      <div className="text-xl font-semibold">
                        {invitation.location}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {invitation.description && (
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 mb-8">
                  <div className="flex items-center mb-4">
                    <div className="bg-purple-500 rounded-full p-3 ml-4">
                      <FiMessageSquare className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-800">
                        تفاصيل إضافية
                      </h3>
                    </div>
                  </div>
                  <div className="text-gray-700 text-lg leading-relaxed whitespace-pre-wrap">
                    {invitation.description}
                  </div>
                </div>
              )}

              {/* نموذج تأكيد الحضور */}
              {!responseStatus && (
                <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8">
                  <div className="text-center mb-8">
                    <FiGift className="mx-auto h-12 w-12 text-blue-500 mb-4" />
                    <h2 className="text-3xl font-bold text-gray-800 mb-2">
                      تأكيد الحضور
                    </h2>
                    <p className="text-gray-600 text-lg">
                      يسعدنا حضورك معنا في هذه المناسبة المميزة
                    </p>
                  </div>

                  {error && (
                    <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
                      <div className="flex items-center">
                        <FiX className="h-5 w-5 text-red-500 ml-3" />
                        <p className="text-red-700 font-medium">{error}</p>
                      </div>
                    </div>
                  )}

                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">
                        الاسم الكامل
                      </label>
                      <input
                        type="text"
                        placeholder="أدخل اسمك الكامل"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="block w-full px-4 py-4 border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-lg"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">
                        رقم الجوال
                      </label>
                      <input
                        type="tel"
                        placeholder="05xxxxxxxx"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        className="block w-full px-4 py-4 border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-lg"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">
                        ملاحظات إضافية (اختياري)
                      </label>
                      <textarea
                        placeholder="أي ملاحظات أو طلبات خاصة..."
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        className="block w-full px-4 py-4 border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-lg h-32 resize-none"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <button
                        onClick={() => handleResponse("accepted")}
                        disabled={submitting || !name.trim() || !phone.trim()}
                        className="group relative w-full flex items-center justify-center px-8 py-4 border-2 border-transparent text-lg font-bold rounded-2xl text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-4 focus:ring-green-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        {submitting ? (
                          <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent"></div>
                        ) : (
                          <>
                            <FiCheck className="ml-3 h-6 w-6" />
                            سأحضر بإذن الله
                          </>
                        )}
                      </button>

                      <button
                        onClick={() => handleResponse("declined")}
                        disabled={submitting || !name.trim() || !phone.trim()}
                        className="group relative w-full flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-lg font-bold rounded-2xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-4 focus:ring-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        {submitting ? (
                          <div className="animate-spin rounded-full h-6 w-6 border-2 border-gray-400 border-t-transparent"></div>
                        ) : (
                          <>
                            <FiX className="ml-3 h-6 w-6" />
                            أعتذر عن الحضور
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* رسالة النجاح مع QR Code */}
              {success && (
                <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8 text-center">
                  <div className="mb-6">
                    {responseStatus === "accepted" ? (
                      <div className="bg-green-500 rounded-full p-4 w-20 h-20 mx-auto mb-4">
                        <FiCheck className="h-12 w-12 text-white mx-auto" />
                      </div>
                    ) : (
                      <div className="bg-gray-500 rounded-full p-4 w-20 h-20 mx-auto mb-4">
                        <FiX className="h-12 w-12 text-white mx-auto" />
                      </div>
                    )}

                    <h3 className="text-3xl font-bold text-gray-800 mb-4">
                      {success}
                    </h3>

                    <p className="text-xl text-gray-600 mb-6">
                      {responseStatus === "accepted"
                        ? "نتطلع للقائك في المناسبة المميزة"
                        : "شكراً لإبلاغنا. نتمنى رؤيتك في مناسبة قادمة"}
                    </p>
                  </div>

                  {/* عرض QR Code للضيوف المقبولين */}
                  {responseStatus === "accepted" &&
                    guestData &&
                    guestData.qr_code && (
                      <div className="bg-white rounded-2xl p-6 shadow-lg">
                        <div className="flex items-center justify-center mb-4">
                          <BsQrCodeScan className="h-8 w-8 text-blue-500 ml-3" />
                          <h4 className="text-2xl font-bold text-gray-800">
                            رمز الدخول الخاص بك
                          </h4>
                        </div>

                        <p className="text-gray-600 mb-6 text-lg">
                          احتفظ بهذا الرمز لتسهيل دخولك للمناسبة
                        </p>

                        <button
                          onClick={showQRCode}
                          className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-blue-600 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center mx-auto"
                        >
                          <BsQrCodeScan className="ml-3 h-6 w-6" />
                          عرض رمز الدخول
                        </button>
                      </div>
                    )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* مودال عرض QR Code */}
        {showQRModal && guestData && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-3xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
              {/* رأس المودال */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-2xl font-bold">رمز الدخول</h3>
                    <p className="text-blue-100 text-sm mt-1">
                      {guestData.name}
                    </p>
                  </div>
                  <button
                    onClick={closeQRModal}
                    className="text-white hover:text-gray-200 transition-colors p-2 rounded-full hover:bg-white hover:bg-opacity-20"
                  >
                    <FiX className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* محتوى المودال */}
              <div className="p-8 text-center">
                {qrCodeImage ? (
                  <div className="space-y-6">
                    <div className="bg-gray-50 p-6 rounded-2xl">
                      <img
                        src={qrCodeImage}
                        alt="QR Code للدخول"
                        className="mx-auto rounded-xl shadow-lg"
                      />
                    </div>

                    <div className="text-sm text-gray-600 space-y-3 bg-blue-50 p-4 rounded-xl">
                      <div className="flex justify-between">
                        <span className="font-bold">الاسم:</span>
                        <span>{guestData.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-bold">الهاتف:</span>
                        <span>{guestData.phone}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-bold">الحالة:</span>
                        <span className="text-green-600 font-medium">
                          مقبول
                        </span>
                      </div>
                    </div>

                    <div className="flex gap-3">
                      <button
                        onClick={downloadQRCode}
                        className="flex-1 bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center justify-center font-bold shadow-lg hover:shadow-xl transform hover:scale-105"
                      >
                        <FiDownload className="ml-2 h-5 w-5" />
                        تحميل
                      </button>
                      <button
                        onClick={closeQRModal}
                        className="flex-1 bg-gray-500 text-white py-3 px-4 rounded-xl hover:bg-gray-600 transition-colors font-bold"
                      >
                        إغلاق
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="py-12">
                    <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent mx-auto mb-6"></div>
                    <p className="text-gray-600 text-lg">
                      جاري إنتاج رمز الدخول...
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
