"use client";

import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import Image from "next/image";
import {
  FiCalendar,
  FiMapPin,
  FiClock,
  FiCheck,
  FiX,
  FiMessageSquare,
} from "react-icons/fi";
import confetti from "canvas-confetti";

export default function InvitationPage({ params }) {
  const [invitation, setInvitation] = useState(null);
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  const [notes, setNotes] = useState("");
  const [responseStatus, setResponseStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [additionalInfo, setAdditionalInfo] = useState(null);

  useEffect(() => {
    const fetchInvitation = async () => {
      try {
        const { data, error } = await supabase
          .from("invitations")
          .select("*")
          .eq("id", params.id)
          .single();

        if (error) throw error;

        if (!data) {
          setError("لا يمكن العثور على الدعوة المطلوبة");
          return;
        }

        setInvitation(data);

        // استخراج المعلومات الإضافية من الوصف إذا كانت موجودة
        try {
          if (data.additional_data) {
            setAdditionalInfo(data.additional_data);
          } else if (data.description && data.description.startsWith("{")) {
            const jsonEndIndex = data.description.indexOf("}") + 1;
            const jsonStr = data.description.substring(0, jsonEndIndex);
            const additionalData = JSON.parse(jsonStr);
            setAdditionalInfo(additionalData);
          }
        } catch (err) {
          console.error("Error parsing additional info:", err);
        }
      } catch (err) {
        console.error("Error fetching invitation:", err);
        setError("لا يمكن العثور على الدعوة المطلوبة");
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [params.id]);

  const handleResponse = async (status) => {
    try {
      setSubmitting(true);
      setError(null);

      // التحقق من المدخلات
      if (!phone) {
        setError("يرجى إدخال رقم الهاتف");
        return;
      }

      if (!name) {
        setError("يرجى إدخال الاسم");
        return;
      }

      if (!params.id) {
        setError("معرف الدعوة غير صالح");
        return;
      }

      // تنظيف البيانات
      const guestData = {
        invitation_id: params.id,
        name: name.trim(),
        phone: phone.replace(/\s+/g, "").replace(/^0/, "+966"),
        status: status,
        notes: notes?.trim() || "",
        number_of_companions: 0,
      };

      // محاولة إضافة الضيف
      const { data, error } = await supabase
        .from("guests")
        .insert([guestData])
        .select()
        .single();

      if (error) {
        // إذا كان الضيف موجود، قم بالتحديث
        if (error.code === "23505") {
          const { data: updateData, error: updateError } = await supabase
            .from("guests")
            .update({
              name: guestData.name,
              status: guestData.status,
              notes: guestData.notes,
              updated_at: new Date().toISOString(),
            })
            .eq("invitation_id", params.id)
            .eq("phone", guestData.phone)
            .select()
            .single();

          if (updateError) throw updateError;
        } else {
          throw error;
        }
      }

      // إظهار رسالة النجاح
      setSuccess(
        status === "confirmed" ? "شكراً لتأكيد حضورك!" : "شكراً لإبلاغنا"
      );
      setResponseStatus(status);

      // تأثيرات النجاح
      if (status === "confirmed") {
        confetti({
          particleCount: 100,
          spread: 70,
          origin: { y: 0.6 },
        });
      }
    } catch (error) {
      console.error("Error:", error);
      setError(error.message || "عذراً، حدث خطأ أثناء تحديث حالة الحضور");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <div className="text-red-500 text-4xl mb-4">
            <FiX className="inline-block" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">عذراً</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
        {/* رأس الدعوة */}
        <div className="relative h-48 bg-gradient-to-r from-primary to-secondary">
          <div className="absolute inset-0 bg-black opacity-30"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white">
              <h1 className="text-3xl font-bold mb-2">{invitation.title}</h1>
              {additionalInfo && (
                <div className="text-lg">
                  {additionalInfo.groom_name && additionalInfo.bride_name && (
                    <p>
                      {additionalInfo.groom_name} & {additionalInfo.bride_name}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* تفاصيل الدعوة */}
        <div className="p-8">
          <div className="space-y-6">
            {invitation.event_date && (
              <div className="flex items-center space-x-4 rtl:space-x-reverse text-gray-600">
                <FiCalendar className="flex-shrink-0 h-6 w-6" />
                <div>
                  <div className="font-semibold">التاريخ</div>
                  <div>
                    {new Date(invitation.event_date).toLocaleDateString(
                      "ar-SA",
                      {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      }
                    )}
                  </div>
                  <div className="text-sm text-gray-500">
                    {new Date(invitation.event_date).toLocaleTimeString(
                      "ar-SA",
                      {
                        hour: "2-digit",
                        minute: "2-digit",
                      }
                    )}
                  </div>
                </div>
              </div>
            )}

            {invitation.location && (
              <div className="flex items-center space-x-4 rtl:space-x-reverse text-gray-600">
                <FiMapPin className="flex-shrink-0 h-6 w-6" />
                <div>
                  <div className="font-semibold">المكان</div>
                  <div>{invitation.location}</div>
                </div>
              </div>
            )}

            {invitation.description && (
              <div className="flex items-start space-x-4 rtl:space-x-reverse text-gray-600">
                <FiMessageSquare className="flex-shrink-0 h-6 w-6 mt-1" />
                <div>
                  <div className="font-semibold">تفاصيل إضافية</div>
                  <div className="whitespace-pre-wrap">
                    {invitation.description}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* نموذج تأكيد الحضور */}
          {!responseStatus && (
            <div className="mt-8 space-y-6">
              <h2 className="text-2xl font-bold text-center text-gray-800">
                تأكيد الحضور
              </h2>

              {error && (
                <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <FiX className="h-5 w-5 text-red-400" />
                    </div>
                    <div className="mr-3">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <input
                  type="text"
                  placeholder="الاسم"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                  required
                />

                <input
                  type="tel"
                  placeholder="رقم الجوال"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                  required
                />

                <textarea
                  placeholder="ملاحظات (اختياري)"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary h-24"
                />

                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={() => handleResponse("confirmed")}
                    disabled={submitting}
                    className="w-full flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  >
                    <FiCheck className="mr-2" />
                    تأكيد الحضور
                  </button>

                  <button
                    onClick={() => handleResponse("declined")}
                    disabled={submitting}
                    className="w-full flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    <FiX className="mr-2" />
                    اعتذار
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* رسالة النجاح */}
          {success && (
            <div className="mt-8 text-center">
              <div className="rounded-full bg-green-100 p-3 w-16 h-16 mx-auto mb-4">
                <FiCheck className="h-10 w-10 text-green-500 mx-auto" />
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">
                {success}
              </h3>
              <p className="text-gray-600">
                {responseStatus === "confirmed"
                  ? "نتطلع للقائك في المناسبة"
                  : "شكراً لإبلاغنا. نتمنى رؤيتك في مناسبة قادمة"}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
