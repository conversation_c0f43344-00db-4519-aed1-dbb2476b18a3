"use client";

import { useState } from "react";
import { addGuestToInvitation } from "@/app/actions";
import Alert from "@/components/Alert";
import Button from "@/components/Button";
import FormInput from "@/components/FormInput";

export default function AddTestGuest() {
  const [formData, setFormData] = useState({
    invitationId: "",
    guestName: "",
    phoneNumber: "",
  });
  const [message, setMessage] = useState({ type: "", text: "" });
  const [confirmationLink, setConfirmationLink] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const result = await addGuestToInvitation(
        formData.invitationId,
        formData.guestName,
        formData.phoneNumber
      );

      if (result.success) {
        setMessage({
          type: "success",
          text: "تم إضافة الضيف بنجاح!",
        });
        // إنشاء رابط التأكيد
        const confirmationLink = `/guest/confirm/${result.data.confirmation_token}`;
        setConfirmationLink(confirmationLink);
      } else {
        setMessage({
          type: "error",
          text: result.error || "حدث خطأ أثناء إضافة الضيف",
        });
      }
    } catch (error) {
      setMessage({
        type: "error",
        text: "حدث خطأ غير متوقع",
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-center mb-6">
          إضافة ضيف للاختبار
        </h1>

        {message.text && (
          <Alert type={message.type} message={message.text} className="mb-4" />
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <FormInput
            label="معرف الدعوة"
            value={formData.invitationId}
            onChange={(e) =>
              setFormData({ ...formData, invitationId: e.target.value })
            }
            required
          />

          <FormInput
            label="اسم الضيف"
            value={formData.guestName}
            onChange={(e) =>
              setFormData({ ...formData, guestName: e.target.value })
            }
            required
          />

          <FormInput
            label="رقم الجوال"
            value={formData.phoneNumber}
            onChange={(e) =>
              setFormData({ ...formData, phoneNumber: e.target.value })
            }
            required
          />

          <Button type="submit" className="w-full">
            إضافة الضيف
          </Button>
        </form>

        {confirmationLink && (
          <div className="mt-6">
            <h2 className="text-lg font-semibold mb-2">رابط تأكيد الحضور:</h2>
            <div className="bg-gray-100 p-3 rounded break-all">
              <a
                href={confirmationLink}
                className="text-blue-600 hover:text-blue-800"
                target="_blank"
                rel="noopener noreferrer"
              >
                {confirmationLink}
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
