'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function ErrorBoundary({ error, reset }) {
  const [errorInfo, setErrorInfo] = useState({
    message: error?.message || 'حدث خطأ غير متوقع',
    stack: error?.stack || '',
  });

  useEffect(() => {
    // يمكنك إرسال الأخطاء إلى خدمة تتبع الأخطاء هنا
    console.error('Error caught by ErrorBoundary:', error);
  }, [error]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        <div className="text-red-500 text-5xl mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">عذراً، حدث خطأ</h2>
        <p className="text-gray-600 mb-6">{errorInfo.message}</p>
        <div className="flex justify-center space-x-4 space-x-reverse">
          <button
            onClick={() => reset()}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
          >
            إعادة المحاولة
          </button>
          <Link href="/">
            <button className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors">
              العودة للرئيسية
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
}