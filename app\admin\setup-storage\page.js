
'use client';
import { useState, useEffect } from 'react';
import { supabase } from '../../../lib/supabase';
import Navbar from '../../../components/Navbar';
import Link from 'next/link';

export default function SetupStorage() {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [buckets, setBuckets] = useState([]);

  // التحقق من وجود buckets عند تحميل الصفحة
  useEffect(() => {
    checkBuckets();
  }, []);

  // التحقق من وجود buckets
  const checkBuckets = async () => {
    try {
      setLoading(true);
      setError(''); // مسح الأخطاء السابقة
      console.log('Checking buckets...');
      
      // طباعة معلومات الاتصال (بدون المفاتيح السرية)
      console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set');
      
      const { data, error } = await supabase.storage.listBuckets();
      
      if (error) {
        console.error('Error listing buckets:', error);
        throw error;
      }
      
      console.log('Buckets data:', data);
      setBuckets(data || []);
      
      // التحقق من وجود bucket templates
      const templatesBucket = data.find(bucket => bucket.name === 'templates');
      if (templatesBucket) {
        setMessage('مساحة التخزين "templates" موجودة بالفعل.');
      } else {
        setMessage('مساحة التخزين "templates" غير موجودة. يرجى إنشاؤها يدويًا من لوحة تحكم Supabase.');
      }
      
      return data;
    } catch (error) {
      console.error('Error checking buckets:', error);
      setError(`حدث خطأ أثناء التحقق من مساحات التخزين: ${error.message}`);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // إنشاء bucket templates
  const createTemplatesBucket = async () => {
    try {
      setLoading(true);
      setError('');
      setMessage('');
      
      console.log('Creating templates bucket...');
      
      // التحقق أولاً مما إذا كان bucket موجودًا بالفعل
      const { data: existingBuckets, error: listError } = await supabase.storage.listBuckets();
      
      if (listError) throw listError;
      
      const templatesBucket = existingBuckets.find(bucket => bucket.name === 'templates');
      if (templatesBucket) {
        setMessage('مساحة التخزين "templates" موجودة بالفعل.');
        return;
      }
      
      // إنشاء bucket لتخزين صور القوالب
      const { data, error: bucketError } = await supabase.storage.createBucket('templates', {
        public: true,
        fileSizeLimit: 10485760, // 10MB
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/jpg', 'image/gif']
      });

      if (bucketError) {
        console.error('Error creating bucket:', bucketError);
        
        // إذا كان الخطأ متعلقًا بسياسات RLS
        if (bucketError.message.includes('row-level security policy')) {
          throw new Error('ليس لديك صلاحيات كافية لإنشاء مساحة تخزين. يرجى إنشاء مساحة التخزين يدويًا من لوحة تحكم Supabase أو تعديل سياسات RLS.');
        }
        
        throw bucketError;
      }

      console.log('Bucket created:', data);
      setMessage('تم إنشاء مساحة التخزين "templates" بنجاح!');
      
      // تحديث قائمة buckets
      await checkBuckets();
    } catch (error) {
      console.error('Error creating templates bucket:', error);
      setError(`حدث خطأ أثناء إنشاء مساحة التخزين: ${error.message || error}`);
    } finally {
      setLoading(false);
    }
  };

  // تعليمات إنشاء bucket يدوين
  const manualInstructions = () => {
    return (
      <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
        <h3 className="font-bold mb-2">تعليمات إنشاء مساحة التخزين يدوين:</h3>
        <ol className="list-decimal pl-5">
          <li className="mb-1">قم بتسجيل الدخول إلى لوحة تحكم Supabase</li>
          <li className="mb-1">اختر مشروعك</li>
          <li className="mb-1">انتقل إلى قسم "Storage"</li>
          <li className="mb-1">اضغط على "New Bucket"</li>
          <li className="mb-1">أدخل اسم "templates" للـ bucket</li>
          <li className="mb-1">حدد خيار "Public bucket" لجعل الصور متاحة للعرض العام</li>
          <li className="mb-1">اضغط على "Create bucket"</li>
          <li className="mb-1">بعد الإنشاء، اضغط على زر "التحقق من مساحات التخزين" أدناه للتأكد من وجود الـ bucket</li>
        </ol>
      </div>
    );
  };

  return (
    <>
      <Navbar userRole="admin" />
      <div className="p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">إعداد مساحة التخزين</h1>
          <Link 
            href="/admin/dashboard" 
            className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
          >
            العودة للوحة التحكم
          </Link>
        </div>
        
        {message && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {message}
          </div>
        )}
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <p>{error}</p>
            {error.includes('row-level security policy') && (
              <p className="mt-2">
                هذا الخطأ يشير إلى مشكلة في سياسات الأمان (RLS). يرجى اتباع التعليمات أدناه لإنشاء مساحة التخزين يدويًا وتعديل سياسات RLS.
              </p>
            )}
          </div>
        )}
        
        {manualInstructions()}
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-bold mb-4">التحقق من مساحات التخزين الموجودة</h2>
          <p className="mb-4">
            انقر على الزر أدناه للتحقق من مساحات التخزين الموجودة حال.
          </p>
          
          <button
            onClick={checkBuckets}
            disabled={loading}
            className={`${
              loading ? 'bg-gray-400' : 'bg-gray-600 hover:bg-gray-700'
            } text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 mb-4`}
          >
            {loading ? 'جاري التحقق...' : 'التحقق من مساحات التخزين'}
          </button>
          
          {buckets.length > 0 && (
            <div className="mt-4">
              <h3 className="font-bold mb-2">مساحات التخزين الموجودة:</h3>
              <ul className="list-disc pl-5">
                {buckets.map(bucket => (
                  <li key={bucket.id || bucket.name} className="mb-1">
                    {bucket.name} {bucket.public ? '(عام)' : '(خاص)'}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">إنشاء مساحة تخزين templates</h2>
          <p className="mb-4">
            انقر على الزر أدناه لإنشاء مساحة تخزين "templates" لصور قوالب الدعوات.
          </p>
          
          <button
            onClick={createTemplatesBucket}
            disabled={loading}
            className={`${
              loading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
            } text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            {loading ? 'جاري الإنشاء...' : 'إنشاء مساحة التخزين'}
          </button>
        </div>
      </div>
    </>
  );
}






