"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { FiMessageCircle } from "react-icons/fi";

export default function ActivationPendingPage() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const supportWhatsApp = "+966500000000"; // رقم الواتساب الخاص بالدعم

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      if (parsedUser.isActive) {
        router.push("/dashboard");
        return;
      }
      setUser(parsedUser);
    } catch (error) {
      console.error("Error parsing user data:", error);
      router.push("/login");
    }
  }, [router]);

  const handleWhatsAppSupport = () => {
    const message = `مرحباً، أنا ${
      user?.name || "مستخدم جديد"
    } وأود تفعيل حسابي في نظام الدعوات.`;
    const whatsappUrl = `https://wa.me/${supportWhatsApp}?text=${encodeURIComponent(
      message
    )}`;
    window.open(whatsappUrl, "_blank");
  };

  if (!user) {
    return <div>جاري التحميل...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="p-8">
          <div className="flex justify-center mb-6">
            <div className="bg-yellow-100 rounded-full p-3">
              <FiMessageCircle className="h-8 w-8 text-yellow-500" />
            </div>
          </div>

          <h2 className="text-center text-2xl font-bold text-gray-900 mb-4">
            جاري مراجعة حسابك
          </h2>

          <p className="text-center text-gray-600 mb-6">
            شكراً لتسجيلك في منصتنا. حسابك قيد المراجعة حالياً وسيتم تفعيله
            قريباً. للتواصل مع الدعم الفني وتسريع عملية التفعيل، يرجى الضغط على
            زر الواتساب أدناه.
          </p>

          <div className="mt-6">
            <button
              onClick={handleWhatsAppSupport}
              className="w-full flex justify-center items-center px-4 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <svg
                className="w-5 h-5 ml-2"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M17.498 14.382c-.301-.15-1.767-.867-2.04-.966-.273-.101-.473-.15-.673.15-.197.295-.771.964-.944 1.162-.175.195-.349.21-.646.075-.3-.15-1.263-.465-2.403-1.485-.888-.795-1.484-1.77-1.66-2.07-.174-.3-.019-.465.13-.615.136-.135.301-.345.451-.523.146-.181.194-.301.297-.496.1-.21.049-.375-.025-.524-.075-.15-.672-1.62-.922-2.206-.24-.584-.487-.51-.672-.51-.172-.015-.371-.015-.571-.015-.2 0-.523.074-.797.359-.273.3-1.045 1.02-1.045 2.475s1.07 2.865 1.219 3.075c.149.195 2.105 3.195 5.1 4.485.714.3 1.27.48 1.704.629.714.227 1.365.195 1.88.121.574-.091 1.767-.721 2.016-1.426.255-.705.255-1.29.18-1.425-.074-.135-.27-.21-.57-.345z" />
                <path d="M20.52 3.449C12.831-3.984.106 1.407.101 11.893c0 1.96.5 3.891 1.44 5.592l-1.54 5.616 5.763-1.506c1.631.87 3.471 1.331 5.346 1.335 9.579 0 15.135-7.819 15.14-15.354.004-4.108-1.595-7.961-4.511-10.876-2.919-2.917-6.78-4.521-10.889-4.525H20.52v.015zm-9.399 21.441h-.005c-2.643-.003-5.236-.732-7.484-2.114l-.537-.319-5.562 1.446 1.476-5.385-.345-.546c-1.461-2.325-2.236-5.015-2.236-7.806 0-8.048 6.573-14.61 14.656-14.61 3.915.004 7.595 1.529 10.366 4.295 2.77 2.777 4.295 6.461 4.29 10.382-.004 8.05-6.576 14.616-14.65 14.616l.015-.006z" />
              </svg>
              تواصل مع الدعم عبر واتساب
            </button>
          </div>

          <div className="mt-4 text-center">
            <button
              onClick={() => router.push("/login")}
              className="text-sm text-gray-600 hover:text-gray-900"
            >
              العودة لتسجيل الدخول
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
