# دليل إعداد قاعدة البيانات

## الخطوات

1. قم بتسجيل الدخول إلى لوحة تحكم Supabase
2. انتقل إلى مشروعك
3. افتح SQL Editor
4. قم بتنفيذ الملفات التالية بالترتيب:
   - `schema.sql` - لإنشاء الجداول الأساسية
   - `add-test-users.sql` - لإضافة مستخدمين تجريبيين

## التحقق من الإعداد

1. افتح متصفحك وانتقل إلى `http://localhost:3000/admin/test-connection`
2. تأكد من نجاح الاتصال
3. انتقل إلى `http://localhost:3000/admin/setup-database`
4. اضغط على زر "التحقق من قاعدة البيانات" للتأكد من وجود جميع الجداول

## في حالة وجود مشاكل

1. تأكد من وجود وصحة المتغيرات البيئية في ملف `.env.local`:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key
   ```
2. تأكد من تنفيذ جميع الأوامر في SQL Editor
3. تحقق من سجلات الخطأ في وحدة التحكم بالمتصفح

## هيكل قاعدة البيانات

### جدول users

- المستخدمين الأساسيين في النظام
- يحتوي على: id, name, phone, password, role

### جدول templates

- قوالب الدعوات
- يحتوي على: id, name, image_url

### جدول invitations

- الدعوات المنشأة
- يحتوي على: id, user_id, title, description, event_date, location, template_id

### جدول guests

- الضيوف المدعوين
- يحتوي على: id, invitation_id, name, phone, status

### جدول invitation_tracking

- تتبع حالات الدعوات
- يحتوي على: id, guest_id, action, timestamp
