"use client";

import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  FiUsers,
  FiMail,
  FiCheckCircle,
  FiBarChart2,
  FiPlus,
  FiFileText,
  FiDatabase,
  FiServer,
  FiSettings,
  FiCalendar,
  FiClock,
  FiUserPlus,
  FiAlertCircle,
  FiTrendingUp,
  FiActivity,
  FiPieChart,
  FiRefreshCw,
} from "react-icons/fi";
import Header from "@/components/Header";
import dynamic from "next/dynamic";

// استيراد مكتبة ApexCharts بشكل ديناميكي لتجنب مشاكل SSR
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    subscribers: 0,
    invitations: 0,
    guests: 0,
    attendance: 0,
    pendingInvitations: 0,
    activeEvents: 0,
  });
  const [recentSubscribers, setRecentSubscribers] = useState([]);
  const [recentInvitations, setRecentInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [chartData, setChartData] = useState({
    invitationsPerDay: [],
    guestResponses: [],
    subscriberGrowth: [],
  });
  const router = useRouter();

  // تعريف الأنيميشنات
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 },
    },
  };

  useEffect(() => {
    const checkUser = async () => {
      try {
        const userData = localStorage.getItem("user");
        if (!userData) {
          router.push("/login");
          return;
        }

        const parsedUser = JSON.parse(userData);
        if (parsedUser.role !== "admin") {
          router.push("/login");
          return;
        }

        setUser(parsedUser);
        fetchStats();
        fetchChartData();
      } catch (error) {
        console.error("Error checking user:", error);
        router.push("/login");
      }
    };

    checkUser();
  }, [router]);

  const fetchStats = async () => {
    try {
      setLoading(true);

      // عدد المشتركين
      const { count: subscribersCount, error: subscribersError } =
        await supabase
          .from("users")
          .select("*", { count: "exact", head: true })
          .eq("role", "subscriber");

      if (subscribersError) throw subscribersError;

      // عدد الدعوات
      const { count: invitationsCount, error: invitationsError } =
        await supabase
          .from("invitations")
          .select("*", { count: "exact", head: true });

      if (invitationsError) throw invitationsError;

      // عدد الدعوات المعلقة
      const { count: pendingInvitationsCount, error: pendingError } =
        await supabase
          .from("invitations")
          .select("*", { count: "exact", head: true })
          .eq("status", "pending");

      if (pendingError) throw pendingError;

      // عدد المناسبات النشطة
      const today = new Date().toISOString();
      const { count: activeEventsCount, error: eventsError } = await supabase
        .from("events")
        .select("*", { count: "exact", head: true })
        .gt("event_date", today);

      if (eventsError) throw eventsError;

      // عدد المدعوين
      const { count: guestsCount, error: guestsError } = await supabase
        .from("guests")
        .select("*", { count: "exact", head: true });

      if (guestsError) throw guestsError;

      // عدد الحضور
      const { count: attendanceCount, error: attendanceError } = await supabase
        .from("guests")
        .select("*", { count: "exact", head: true })
        .eq("attended", true);

      if (attendanceError) throw attendanceError;

      // آخر المشتركين
      const { data: subscribers, error: recentError } = await supabase
        .from("users")
        .select("*")
        .eq("role", "subscriber")
        .order("created_at", { ascending: false })
        .limit(5);

      if (recentError) throw recentError;

      // آخر الدعوات
      const { data: invitations, error: recentInvError } = await supabase
        .from("invitations")
        .select("*, users(name)")
        .order("created_at", { ascending: false })
        .limit(5);

      if (recentInvError) throw recentInvError;

      setStats({
        subscribers: subscribersCount || 0,
        invitations: invitationsCount || 0,
        guests: guestsCount || 0,
        attendance: attendanceCount || 0,
        pendingInvitations: pendingInvitationsCount || 0,
        activeEvents: activeEventsCount || 0,
      });

      setRecentSubscribers(subscribers || []);
      setRecentInvitations(invitations || []);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching stats:", error);
      setLoading(false);
    }
  };

  const fetchChartData = async () => {
    try {
      // بيانات الدعوات حسب اليوم (آخر 7 أيام)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const { data: invitationsData, error: invitationsError } = await supabase
        .from("invitations")
        .select("created_at")
        .gte("created_at", sevenDaysAgo.toISOString());

      if (invitationsError) throw invitationsError;

      // تجميع البيانات حسب اليوم
      const invitationsByDay = {};
      const days = [];
      for (let i = 0; i < 7; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split("T")[0];
        days.unshift(dateStr);
        invitationsByDay[dateStr] = 0;
      }

      invitationsData?.forEach((inv) => {
        const dateStr = inv.created_at.split("T")[0];
        if (invitationsByDay[dateStr] !== undefined) {
          invitationsByDay[dateStr]++;
        }
      });

      const invitationsPerDay = days.map((day) => invitationsByDay[day]);

      // بيانات استجابة المدعوين
      const { data: guestsData, error: guestsError } = await supabase
        .from("guests")
        .select("status");

      if (guestsError) throw guestsError;

      const guestResponses = {
        accepted: 0,
        declined: 0,
        pending: 0,
      };

      guestsData?.forEach((guest) => {
        if (guest.status === "accepted") guestResponses.accepted++;
        else if (guest.status === "declined") guestResponses.declined++;
        else guestResponses.pending++;
      });

      // بيانات نمو المشتركين (آخر 6 أشهر)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      const { data: subscribersData, error: subscribersError } = await supabase
        .from("users")
        .select("created_at")
        .eq("role", "subscriber")
        .gte("created_at", sixMonthsAgo.toISOString());

      if (subscribersError) throw subscribersError;

      // تجميع البيانات حسب الشهر
      const subscribersByMonth = {};
      const months = [];
      for (let i = 0; i < 6; i++) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthStr = `${date.getFullYear()}-${String(
          date.getMonth() + 1
        ).padStart(2, "0")}`;
        months.unshift(monthStr);
        subscribersByMonth[monthStr] = 0;
      }

      subscribersData?.forEach((sub) => {
        const date = new Date(sub.created_at);
        const monthStr = `${date.getFullYear()}-${String(
          date.getMonth() + 1
        ).padStart(2, "0")}`;
        if (subscribersByMonth[monthStr] !== undefined) {
          subscribersByMonth[monthStr]++;
        }
      });

      const subscriberGrowth = months.map((month) => subscribersByMonth[month]);

      setChartData({
        invitationsPerDay: {
          options: {
            chart: {
              id: "invitations-per-day",
              toolbar: {
                show: false,
              },
            },
            xaxis: {
              categories: days.map((day) => {
                const date = new Date(day);
                return `${date.getDate()}/${date.getMonth() + 1}`;
              }),
            },
            colors: ["#6366F1"],
            stroke: {
              curve: "smooth",
              width: 3,
            },
            fill: {
              type: "gradient",
              gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.2,
                stops: [0, 90, 100],
              },
            },
            dataLabels: {
              enabled: false,
            },
            title: {
              text: "الدعوات المرسلة (آخر 7 أيام)",
              align: "center",
              style: {
                fontSize: "14px",
                fontWeight: "bold",
              },
            },
          },
          series: [
            {
              name: "الدعوات",
              data: invitationsPerDay,
            },
          ],
        },
        guestResponses: {
          options: {
            chart: {
              id: "guest-responses",
              toolbar: {
                show: false,
              },
            },
            labels: ["قبول", "رفض", "معلق"],
            colors: ["#10B981", "#EF4444", "#F59E0B"],
            legend: {
              position: "bottom",
            },
            dataLabels: {
              enabled: true,
            },
            title: {
              text: "استجابة المدعوين",
              align: "center",
              style: {
                fontSize: "14px",
                fontWeight: "bold",
              },
            },
          },
          series: [
            guestResponses.accepted,
            guestResponses.declined,
            guestResponses.pending,
          ],
        },
        subscriberGrowth: {
          options: {
            chart: {
              id: "subscriber-growth",
              toolbar: {
                show: false,
              },
            },
            xaxis: {
              categories: months.map((month) => {
                const [year, monthNum] = month.split("-");
                return `${monthNum}/${year.substring(2)}`;
              }),
            },
            colors: ["#8B5CF6"],
            stroke: {
              curve: "smooth",
              width: 3,
            },
            fill: {
              type: "gradient",
              gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.2,
                stops: [0, 90, 100],
              },
            },
            dataLabels: {
              enabled: false,
            },
            title: {
              text: "نمو المشتركين (آخر 6 أشهر)",
              align: "center",
              style: {
                fontSize: "14px",
                fontWeight: "bold",
              },
            },
          },
          series: [
            {
              name: "المشتركين الجدد",
              data: subscriberGrowth,
            },
          ],
        },
      });
    } catch (error) {
      console.error("Error fetching chart data:", error);
    }
  };

  const refreshData = () => {
    fetchStats();
    fetchChartData();
  };

  // مكون بطاقة الإحصائيات
  const StatCard = ({ title, value, icon, color, iconBgColor, subtitle }) => (
    <motion.div
      className={`bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 border-t-4 ${color}`}
      variants={itemVariants}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
    >
      <div className="p-5">
        <div className="flex items-center">
          <div className={`rounded-full p-3 ${iconBgColor} text-gray-800`}>
            {icon}
          </div>
          <div className="mr-4">
            <h3 className="text-lg font-semibold text-gray-700">{title}</h3>
            <p className="text-3xl font-bold text-gray-900">{value}</p>
            {subtitle && (
              <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gray-100">
      <Header userRole="admin" userName={user?.name} />

      {/* الرأس */}
      <motion.div
        className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-white p-3 rounded-full shadow-md">
                <FiBarChart2 className="h-6 w-6 text-indigo-600" />
              </div>
              <div className="mr-4">
                <h1 className="text-2xl font-bold">لوحة تحكم المدير</h1>
                <p className="text-indigo-200">مرحباً {user?.name}</p>
              </div>
            </div>
            <motion.button
              onClick={refreshData}
              className="flex items-center bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-colors duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FiRefreshCw className="ml-2" />
              تحديث البيانات
            </motion.button>
          </div>
        </div>
      </motion.div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* بطاقات الإحصائيات */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <StatCard
            title="المشتركين"
            value={stats.subscribers}
            icon={<FiUsers className="h-6 w-6" />}
            color="border-blue-600"
            iconBgColor="bg-blue-100"
          />
          <StatCard
            title="الدعوات"
            value={stats.invitations}
            icon={<FiMail className="h-6 w-6" />}
            color="border-green-600"
            iconBgColor="bg-green-100"
          />
          <StatCard
            title="المدعوين"
            value={stats.guests}
            icon={<FiUsers className="h-6 w-6" />}
            color="border-yellow-600"
            iconBgColor="bg-yellow-100"
          />
          <StatCard
            title="الحضور"
            value={stats.attendance}
            icon={<FiCheckCircle className="h-6 w-6" />}
            color="border-purple-600"
            iconBgColor="bg-purple-100"
          />
          <StatCard
            title="دعوات معلقة"
            value={stats.pendingInvitations}
            icon={<FiAlertCircle className="h-6 w-6" />}
            color="border-orange-600"
            iconBgColor="bg-orange-100"
          />
          <StatCard
            title="مناسبات نشطة"
            value={stats.activeEvents}
            icon={<FiCalendar className="h-6 w-6" />}
            color="border-teal-600"
            iconBgColor="bg-teal-100"
          />
        </motion.div>

        {/* المعلومات التفصيلية */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* آخر المشتركين */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold text-gray-800 flex items-center">
                  <FiUsers className="ml-2 text-blue-600" />
                  آخر المشتركين
                </h2>
                <Link
                  href="/admin/subscribers/add"
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex items-center transition-colors duration-300"
                >
                  <FiPlus className="ml-1" />
                  إضافة مشترك جديد
                </Link>
              </div>
            </div>

            {recentSubscribers.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">لا يوجد مشتركين حتى الآن</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الاسم
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        البريد الإلكتروني
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ التسجيل
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {recentSubscribers.map((subscriber) => (
                      <tr
                        key={subscriber.id}
                        className="hover:bg-gray-50 transition-colors duration-150"
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 font-bold">
                              {subscriber.name?.charAt(0) || "U"}
                            </div>
                            <div className="mr-3">
                              <div className="text-sm font-medium text-gray-900">
                                {subscriber.name}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">
                            {subscriber.email}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500 flex items-center">
                            <FiCalendar className="ml-1 text-gray-400" />
                            {new Date(subscriber.created_at).toLocaleDateString(
                              "ar-SA"
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            <div className="p-4 bg-gray-50 border-t border-gray-200">
              <Link
                href="/admin/subscribers"
                className="text-indigo-600 hover:text-indigo-900 font-medium flex items-center justify-center"
              >
                عرض جميع المشتركين
              </Link>
            </div>
          </div>

          {/* إحصائيات الدعوات */}
          <div className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-800 flex items-center">
                <FiBarChart2 className="ml-2 text-purple-600" />
                إحصائيات الدعوات
              </h2>
            </div>

            <div className="space-y-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600 flex items-center">
                    <FiMail className="ml-1 text-blue-500" />
                    إجمالي الدعوات
                  </span>
                  <span className="font-bold text-lg">{stats.invitations}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full"
                    style={{ width: "100%" }}
                  ></div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600 flex items-center">
                    <FiUsers className="ml-1 text-green-500" />
                    إجمالي المدعوين
                  </span>
                  <span className="font-bold text-lg">{stats.guests}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-green-600 h-2.5 rounded-full"
                    style={{ width: "100%" }}
                  ></div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600 flex items-center">
                    <FiCheckCircle className="ml-1 text-purple-500" />
                    إجمالي الحضور
                  </span>
                  <span className="font-bold text-lg">{stats.attendance}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-purple-600 h-2.5 rounded-full"
                    style={{
                      width: `${
                        stats.guests > 0
                          ? (stats.attendance / stats.guests) * 100
                          : 0
                      }%`,
                    }}
                  ></div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600 flex items-center">
                    <FiUsers className="ml-1 text-indigo-500" />
                    متوسط المدعوين لكل دعوة
                  </span>
                  <span className="font-bold text-lg">
                    {stats.invitations > 0
                      ? Math.round(stats.guests / stats.invitations)
                      : 0}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
