"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  FiUsers,
  FiMail,
  FiCheckCircle,
  FiBarChart2,
  FiCalendar,
  FiTrendingUp,
  FiActivity,
  FiPieChart,
  FiRefreshCw,
  FiInfo,
} from "react-icons/fi";
import Header from "@/components/Header";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  fetchAdminStats,
  handleAdminDashboardError,
} from "@/lib/adminDashboardHelpers";
import { getValidatedUser } from "@/lib/userValidation";

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    users: {
      total: 0,
      active: 0,
      subscribers: 0,
      admins: 0,
      newThisMonth: 0,
      growthRate: 0,
    },
    invitations: {
      total: 0,
      active: 0,
      upcoming: 0,
      newThisMonth: 0,
      growthRate: 0,
    },
    guests: {
      total: 0,
      accepted: 0,
      declined: 0,
      pending: 0,
      attended: 0,
      acceptanceRate: 0,
      attendanceRate: 0,
      avgPerInvitation: 0,
    },
    performance: {
      acceptanceRate: 0,
      attendanceRate: 0,
      avgGuestsPerInvitation: 0,
      userEngagement: 0,
    },
  });
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const router = useRouter();

  // تعريف الأنيميشنات
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 },
    },
  };

  useEffect(() => {
    const checkUser = async () => {
      try {
        setLoading(true);

        // استخدام دالة التحقق الجديدة
        const validatedUser = await getValidatedUser();
        if (!validatedUser) {
          console.log("No validated user found, redirecting to login");
          router.push("/login");
          return;
        }

        // التحقق من صلاحيات الأدمن
        if (validatedUser.role !== "admin") {
          console.log("User is not admin, redirecting to login");
          router.push("/login");
          return;
        }

        console.log("Validated admin user:", validatedUser);
        setUser(validatedUser);

        // جلب الإحصائيات باستخدام الدالة الجديدة
        console.log("Fetching admin stats...");
        const adminStats = await fetchAdminStats();
        setStats(adminStats);

        console.log("Admin dashboard data loaded successfully");

        console.log("Admin dashboard data loaded successfully");
      } catch (error) {
        console.error("Error in admin dashboard useEffect:", error);
        const errorMessage = handleAdminDashboardError(
          error,
          "تحميل بيانات الداشبورد"
        );
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    checkUser();
  }, [router]);

  // دالة تحديث البيانات
  const refreshData = async () => {
    try {
      setLoading(true);
      console.log("Refreshing admin dashboard data...");

      // جلب الإحصائيات المحدثة
      const adminStats = await fetchAdminStats();
      setStats(adminStats);

      toast.success("تم تحديث البيانات بنجاح!");
      console.log("Admin dashboard data refreshed successfully");
    } catch (error) {
      console.error("Error refreshing admin dashboard:", error);
      const errorMessage = handleAdminDashboardError(error, "تحديث البيانات");
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // مكون بطاقة الإحصائيات المحسن
  const StatCard = ({
    title,
    value,
    icon,
    gradient,
    subtitle,
    trend,
    trendValue,
    onClick,
  }) => (
    <motion.div
      className={`relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer ${gradient}`}
      variants={itemVariants}
      whileHover={{ y: -8, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
    >
      {/* خلفية متدرجة */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>

      {/* المحتوى */}
      <div className="relative p-6 text-white">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                {icon}
              </div>
              {trend && (
                <div
                  className={`mr-2 flex items-center text-sm ${
                    trend === "up"
                      ? "text-green-200"
                      : trend === "down"
                      ? "text-red-200"
                      : "text-yellow-200"
                  }`}
                >
                  {trend === "up" ? (
                    <FiTrendingUp className="w-4 h-4 ml-1" />
                  ) : trend === "down" ? (
                    <FiActivity className="w-4 h-4 ml-1 rotate-180" />
                  ) : (
                    <FiActivity className="w-4 h-4 ml-1" />
                  )}
                  {trendValue}
                </div>
              )}
            </div>

            <h3 className="text-sm font-medium text-white/80 mb-1">{title}</h3>
            <p className="text-3xl font-bold text-white mb-1">{value}</p>

            {subtitle && <p className="text-sm text-white/70">{subtitle}</p>}
          </div>
        </div>
      </div>

      {/* تأثير الإضاءة */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
    </motion.div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">
            جاري تحميل بيانات الداشبورد...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <Header userRole="admin" userName={user?.name} />
      <ToastContainer position="top-right" autoClose={3000} />

      {/* الرأس المحسن */}
      <motion.div
        className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white relative overflow-hidden"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* خلفية متحركة */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-pink-600/20 animate-pulse"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex flex-col lg:flex-row items-center justify-between">
            <div className="flex items-center mb-6 lg:mb-0">
              <motion.div
                className="bg-white/20 p-4 rounded-2xl backdrop-blur-sm shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <FiBarChart2 className="h-8 w-8 text-white" />
              </motion.div>
              <div className="mr-6">
                <h1 className="text-3xl font-bold mb-2">لوحة تحكم المدير</h1>
                <p className="text-white/80 text-lg">
                  مرحباً {user?.name} - إدارة شاملة للنظام
                </p>
                <div className="flex items-center mt-2 text-white/70">
                  <FiCalendar className="w-4 h-4 ml-2" />
                  <span>
                    {new Date().toLocaleDateString("ar-SA", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">


              {/* زر التحديث */}
              <motion.button
                onClick={refreshData}
                disabled={loading}
                className="flex items-center bg-white/20 hover:bg-white/30 disabled:opacity-50 text-white px-6 py-3 rounded-xl transition-all duration-300 backdrop-blur-sm border border-white/30"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <FiRefreshCw
                  className={`ml-2 w-5 h-5 ${loading ? "animate-spin" : ""}`}
                />
                تحديث البيانات
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* بطاقات الإحصائيات المحسنة */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <StatCard
            title="إجمالي المستخدمين"
            value={stats.users.total}
            icon={<FiUsers className="h-6 w-6" />}
            gradient="bg-gradient-to-br from-blue-500 to-blue-700"
            subtitle={`${stats.users.active} نشط`}
            trend={
              stats.users.growthRate > 0
                ? "up"
                : stats.users.growthRate < 0
                ? "down"
                : "stable"
            }
            trendValue={`${stats.users.growthRate}%`}
            onClick={() => router.push("/admin/subscribers")}
          />

          <StatCard
            title="إجمالي الدعوات"
            value={stats.invitations.total}
            icon={<FiMail className="h-6 w-6" />}
            gradient="bg-gradient-to-br from-green-500 to-emerald-700"
            subtitle={`${stats.invitations.upcoming} قادمة`}
            trend={
              stats.invitations.growthRate > 0
                ? "up"
                : stats.invitations.growthRate < 0
                ? "down"
                : "stable"
            }
            trendValue={`${stats.invitations.growthRate}%`}
            onClick={() => router.push("/admin/invitations")}
          />

          <StatCard
            title="إجمالي المدعوين"
            value={stats.guests.total}
            icon={<FiUserCheck className="h-6 w-6" />}
            gradient="bg-gradient-to-br from-purple-500 to-purple-700"
            subtitle={`${stats.guests.accepted} مقبول`}
            trend={
              stats.guests.acceptanceRate > 70
                ? "up"
                : stats.guests.acceptanceRate < 50
                ? "down"
                : "stable"
            }
            trendValue={`${stats.guests.acceptanceRate}%`}
            onClick={() => router.push("/admin/guests")}
          />

          <StatCard
            title="معدل الحضور"
            value={`${stats.guests.attendanceRate}%`}
            icon={<FiCheckCircle className="h-6 w-6" />}
            gradient="bg-gradient-to-br from-orange-500 to-red-600"
            subtitle={`${stats.guests.attended} حضر`}
            trend={
              stats.guests.attendanceRate > 80
                ? "up"
                : stats.guests.attendanceRate < 60
                ? "down"
                : "stable"
            }
            trendValue={`${stats.guests.attended} من ${stats.guests.accepted}`}
          />
        </motion.div>

        {/* إحصائيات تفصيلية */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="bg-white rounded-2xl shadow-lg p-6 border-l-4 border-blue-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المشتركين</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.users.subscribers}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <FiUsers className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <div className="flex items-center text-sm text-gray-500">
                <span>جديد هذا الشهر: {stats.users.newThisMonth}</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-6 border-l-4 border-green-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">معدل القبول</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.guests.acceptanceRate}%
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <FiTrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: `${stats.guests.acceptanceRate}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-6 border-l-4 border-purple-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  متوسط المدعوين
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.guests.avgPerInvitation}
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <FiPieChart className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4">
              <div className="flex items-center text-sm text-gray-500">
                <span>لكل دعوة</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-6 border-l-4 border-orange-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  تفاعل المستخدمين
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.performance.userEngagement}%
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <FiActivity className="h-6 w-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-orange-500 h-2 rounded-full"
                  style={{ width: `${stats.performance.userEngagement}%` }}
                ></div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* إحصائيات النظام */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* نشاط النظام */}
          <motion.div
            className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
            variants={itemVariants}
          >
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-bold text-gray-800 flex items-center">
                <FiActivity className="ml-2 text-blue-600" />
                نشاط النظام
              </h2>
            </div>

            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-xl">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FiUsers className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="mr-3">
                      <p className="text-sm font-medium text-gray-900">
                        المستخدمون النشطون
                      </p>
                      <p className="text-xs text-gray-500">
                        من إجمالي المسجلين
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-blue-600">
                      {stats.users.active}
                    </p>
                    <p className="text-xs text-gray-500">
                      من {stats.users.total}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-green-50 rounded-xl">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <FiMail className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="mr-3">
                      <p className="text-sm font-medium text-gray-900">
                        الدعوات النشطة
                      </p>
                      <p className="text-xs text-gray-500">جاهزة للإرسال</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-green-600">
                      {stats.invitations.active}
                    </p>
                    <p className="text-xs text-gray-500">
                      من {stats.invitations.total}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-purple-50 rounded-xl">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <FiCalendar className="h-5 w-5 text-purple-600" />
                    </div>
                    <div className="mr-3">
                      <p className="text-sm font-medium text-gray-900">
                        الأحداث القادمة
                      </p>
                      <p className="text-xs text-gray-500">خلال الشهر القادم</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-purple-600">
                      {stats.invitations.upcoming}
                    </p>
                    <p className="text-xs text-gray-500">حدث</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* إحصائيات الاستجابة */}
          <motion.div
            className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
            variants={itemVariants}
          >
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-bold text-gray-800 flex items-center">
                <FiPieChart className="ml-2 text-orange-600" />
                إحصائيات الاستجابة
              </h2>
            </div>

            <div className="p-6">
              <div className="space-y-6">
                {/* معدل القبول */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      معدل قبول الدعوات
                    </span>
                    <span className="text-sm font-bold text-green-600">
                      {stats.guests.acceptanceRate}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${stats.guests.acceptanceRate}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>{stats.guests.accepted} مقبول</span>
                    <span>{stats.guests.total} إجمالي</span>
                  </div>
                </div>

                {/* معدل الحضور */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      معدل الحضور الفعلي
                    </span>
                    <span className="text-sm font-bold text-blue-600">
                      {stats.guests.attendanceRate}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-blue-400 to-blue-600 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${stats.guests.attendanceRate}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>{stats.guests.attended} حضر</span>
                    <span>{stats.guests.accepted} مقبول</span>
                  </div>
                </div>

                {/* متوسط الضيوف */}
                <div className="bg-gray-50 rounded-xl p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-800">
                      {stats.guests.avgPerInvitation}
                    </p>
                    <p className="text-sm text-gray-600">
                      متوسط الضيوف لكل دعوة
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* روابط سريعة للإدارة */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Link href="/admin/subscribers">
            <motion.div
              className="bg-gradient-to-br from-blue-500 to-blue-700 rounded-2xl p-6 text-white hover:shadow-xl transition-all duration-300 cursor-pointer"
              whileHover={{ y: -5, scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <FiUsers className="h-8 w-8 mb-3" />
                  <h3 className="text-lg font-bold">إدارة المشتركين</h3>
                  <p className="text-blue-100 text-sm">
                    عرض وإدارة جميع المشتركين
                  </p>
                </div>
                <div className="text-3xl font-bold opacity-20">
                  {stats.users.subscribers}
                </div>
              </div>
            </motion.div>
          </Link>

          <Link href="/admin/invitations">
            <motion.div
              className="bg-gradient-to-br from-green-500 to-emerald-700 rounded-2xl p-6 text-white hover:shadow-xl transition-all duration-300 cursor-pointer"
              whileHover={{ y: -5, scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <FiMail className="h-8 w-8 mb-3" />
                  <h3 className="text-lg font-bold">إدارة الدعوات</h3>
                  <p className="text-green-100 text-sm">
                    عرض وإدارة جميع الدعوات
                  </p>
                </div>
                <div className="text-3xl font-bold opacity-20">
                  {stats.invitations.total}
                </div>
              </div>
            </motion.div>
          </Link>

          <Link href="/admin/guests">
            <motion.div
              className="bg-gradient-to-br from-purple-500 to-purple-700 rounded-2xl p-6 text-white hover:shadow-xl transition-all duration-300 cursor-pointer"
              whileHover={{ y: -5, scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <FiUserCheck className="h-8 w-8 mb-3" />
                  <h3 className="text-lg font-bold">إدارة الضيوف</h3>
                  <p className="text-purple-100 text-sm">
                    عرض وإدارة جميع الضيوف
                  </p>
                </div>
                <div className="text-3xl font-bold opacity-20">
                  {stats.guests.total}
                </div>
              </div>
            </motion.div>
          </Link>

          <Link href="/admin/reports">
            <motion.div
              className="bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl p-6 text-white hover:shadow-xl transition-all duration-300 cursor-pointer"
              whileHover={{ y: -5, scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <FiBarChart2 className="h-8 w-8 mb-3" />
                  <h3 className="text-lg font-bold">التقارير</h3>
                  <p className="text-orange-100 text-sm">
                    عرض التقارير والإحصائيات
                  </p>
                </div>
                <div className="text-3xl font-bold opacity-20">
                  <FiTrendingUp className="h-8 w-8" />
                </div>
              </div>
            </motion.div>
          </Link>
        </motion.div>

        {/* ملخص الأداء */}
        <motion.div
          className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          variants={itemVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-800 flex items-center">
              <FiBarChart2 className="ml-3 text-indigo-600" />
              ملخص الأداء العام
            </h2>
            <div className="flex items-center text-sm text-gray-500">
              <FiInfo className="ml-1 w-4 h-4" />
              آخر تحديث: {new Date().toLocaleTimeString("ar-SA")}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {stats.performance.userEngagement}%
              </div>
              <div className="text-sm font-medium text-blue-800">
                تفاعل المستخدمين
              </div>
              <div className="text-xs text-blue-600 mt-1">
                {stats.users.active} من {stats.users.total} مستخدم نشط
              </div>
            </div>

            <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {stats.guests.acceptanceRate}%
              </div>
              <div className="text-sm font-medium text-green-800">
                معدل قبول الدعوات
              </div>
              <div className="text-xs text-green-600 mt-1">
                {stats.guests.accepted} من {stats.guests.total} ضيف
              </div>
            </div>

            <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {stats.guests.attendanceRate}%
              </div>
              <div className="text-sm font-medium text-purple-800">
                معدل الحضور
              </div>
              <div className="text-xs text-purple-600 mt-1">
                {stats.guests.attended} من {stats.guests.accepted} حضر
              </div>
            </div>

            <div className="text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {stats.guests.avgPerInvitation}
              </div>
              <div className="text-sm font-medium text-orange-800">
                متوسط الضيوف
              </div>
              <div className="text-xs text-orange-600 mt-1">لكل دعوة</div>
            </div>
          </div>

          {/* مؤشرات الأداء */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-600">
                  نمو المستخدمين
                </span>
                <span
                  className={`text-sm font-bold ${
                    stats.users.growthRate > 0
                      ? "text-green-600"
                      : stats.users.growthRate < 0
                      ? "text-red-600"
                      : "text-gray-600"
                  }`}
                >
                  {stats.users.growthRate > 0 ? "+" : ""}
                  {stats.users.growthRate}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    stats.users.growthRate > 0
                      ? "bg-green-500"
                      : stats.users.growthRate < 0
                      ? "bg-red-500"
                      : "bg-gray-500"
                  }`}
                  style={{
                    width: `${Math.min(
                      Math.abs(stats.users.growthRate),
                      100
                    )}%`,
                  }}
                ></div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-600">
                  نمو الدعوات
                </span>
                <span
                  className={`text-sm font-bold ${
                    stats.invitations.growthRate > 0
                      ? "text-green-600"
                      : stats.invitations.growthRate < 0
                      ? "text-red-600"
                      : "text-gray-600"
                  }`}
                >
                  {stats.invitations.growthRate > 0 ? "+" : ""}
                  {stats.invitations.growthRate}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    stats.invitations.growthRate > 0
                      ? "bg-green-500"
                      : stats.invitations.growthRate < 0
                      ? "bg-red-500"
                      : "bg-gray-500"
                  }`}
                  style={{
                    width: `${Math.min(
                      Math.abs(stats.invitations.growthRate),
                      100
                    )}%`,
                  }}
                ></div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-600">
                  الأحداث القادمة
                </span>
                <span className="text-sm font-bold text-blue-600">
                  {stats.invitations.upcoming}
                </span>
              </div>
              <div className="text-xs text-gray-500">خلال الشهر القادم</div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
