"use client";

import { useState } from "react";
import Link from "next/link";
import { FiMenu, FiX } from "react-icons/fi";
import { motion, AnimatePresence } from "framer-motion";
import Logo from "./Logo";

export default function LandingHeader({ isLoggedIn }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // تعريف الأنيميشن للقائمة المتحركة
  const menuVariants = {
    closed: {
      opacity: 0,
      height: 0,
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
    open: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.3,
        ease: "easeInOut",
        staggerChildren: 0.07,
        delayChildren: 0.1,
      },
    },
  };

  // أنيميشن لعناصر القائمة
  const itemVariants = {
    closed: { opacity: 0, y: -10 },
    open: { opacity: 1, y: 0 },
  };

  // أنيميشن للوجو
  const logoVariants = {
    initial: { opacity: 0, x: -20 },
    animate: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.5 },
    },
  };

  // أنيميشن للروابط
  const linkVariants = {
    initial: { opacity: 0, y: -10 },
    animate: (custom) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: custom * 0.1,
        duration: 0.5,
      },
    }),
    hover: {
      scale: 1.05,
      color: "#4F46E5",
      transition: { duration: 0.2 },
    },
  };

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          <motion.div
            className="flex items-center"
            variants={logoVariants}
            initial="initial"
            animate="animate"
          >
            <Link href="/" className="flex-shrink-0">
              <Logo />
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-4 space-x-reverse">
            <motion.div
              variants={linkVariants}
              initial="initial"
              animate="animate"
              custom={0}
              whileHover="hover"
            >
              <Link
                href="/"
                className="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                الرئيسية
              </Link>
            </motion.div>

            <motion.div
              variants={linkVariants}
              initial="initial"
              animate="animate"
              custom={1}
              whileHover="hover"
            >
              <Link
                href="#features"
                className="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                المميزات
              </Link>
            </motion.div>

            <motion.div
              variants={linkVariants}
              initial="initial"
              animate="animate"
              custom={2}
              whileHover="hover"
            >
              <Link
                href="#how-it-works"
                className="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                كيف يعمل
              </Link>
            </motion.div>

            <motion.div
              variants={linkVariants}
              initial="initial"
              animate="animate"
              custom={3}
              whileHover="hover"
            >
              <Link
                href="/help"
                className="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                المساعدة
              </Link>
            </motion.div>

            {isLoggedIn ? (
              <motion.div
                variants={linkVariants}
                initial="initial"
                animate="animate"
                custom={4}
                whileHover={{ scale: 1.05 }}
              >
                <Link
                  href="/subscriber/dashboard"
                  className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  لوحة التحكم
                </Link>
              </motion.div>
            ) : (
              <div className="flex items-center space-x-4 space-x-reverse">
                <motion.div
                  variants={linkVariants}
                  initial="initial"
                  animate="animate"
                  custom={4}
                  whileHover="hover"
                >
                  <Link
                    href="/login"
                    className="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    تسجيل الدخول
                  </Link>
                </motion.div>

                <motion.div
                  variants={linkVariants}
                  initial="initial"
                  animate="animate"
                  custom={5}
                  whileHover={{ scale: 1.05 }}
                >
                  <Link
                    href="/register"
                    className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    إنشاء حساب
                  </Link>
                </motion.div>
              </div>
            )}
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <motion.button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-indigo-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="sr-only">فتح القائمة</span>
              <AnimatePresence mode="wait" initial={false}>
                {isMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ opacity: 0, rotate: -90 }}
                    animate={{ opacity: 1, rotate: 0 }}
                    exit={{ opacity: 0, rotate: 90 }}
                    transition={{ duration: 0.2 }}
                  >
                    <FiX className="block h-6 w-6" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ opacity: 0, rotate: 90 }}
                    animate={{ opacity: 1, rotate: 0 }}
                    exit={{ opacity: 0, rotate: -90 }}
                    transition={{ duration: 0.2 }}
                  >
                    <FiMenu className="block h-6 w-6" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            className="md:hidden overflow-hidden"
            variants={menuVariants}
            initial="closed"
            animate="open"
            exit="closed"
          >
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              <motion.div variants={itemVariants}>
                <Link
                  href="/"
                  className="block text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-base font-medium transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  الرئيسية
                </Link>
              </motion.div>

              <motion.div variants={itemVariants}>
                <Link
                  href="#features"
                  className="block text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-base font-medium transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  المميزات
                </Link>
              </motion.div>

              <motion.div variants={itemVariants}>
                <Link
                  href="#how-it-works"
                  className="block text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-base font-medium transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  كيف يعمل
                </Link>
              </motion.div>

              <motion.div variants={itemVariants}>
                <Link
                  href="/help"
                  className="block text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-base font-medium transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  المساعدة
                </Link>
              </motion.div>

              {isLoggedIn ? (
                <motion.div variants={itemVariants}>
                  <Link
                    href="/subscriber/dashboard"
                    className="block bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-base font-medium transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    لوحة التحكم
                  </Link>
                </motion.div>
              ) : (
                <>
                  <motion.div variants={itemVariants}>
                    <Link
                      href="/login"
                      className="block text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-base font-medium transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      تسجيل الدخول
                    </Link>
                  </motion.div>

                  <motion.div variants={itemVariants}>
                    <Link
                      href="/register"
                      className="block bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-base font-medium transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      إنشاء حساب
                    </Link>
                  </motion.div>
                </>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}
