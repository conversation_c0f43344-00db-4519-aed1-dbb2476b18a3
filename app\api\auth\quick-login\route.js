import { NextResponse } from 'next/server';
import { supabase } from '../../../../lib/supabase';

export async function POST(request) {
  try {
    const { role } = await request.json();

    if (!role || (role !== 'admin' && role !== 'subscriber')) {
      return NextResponse.json({
        success: false,
        error: 'دور غير صالح',
      }, { status: 400 });
    }

    // جلب أول مستخدم من الدور المطلوب
    const { data: users, error } = await supabase
      .from('users')
      .select('*')
      .eq('role', role)
      .limit(1);

    if (error) {
      return NextResponse.json({
        success: false,
        error: 'خطأ في قاعدة البيانات',
        details: error.message,
      }, { status: 500 });
    }

    if (!users || users.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لا يوجد مستخدم لهذا الدور',
      }, { status: 404 });
    }

    const user = users[0];
    const { password, ...userWithoutPassword } = user;

    return NextResponse.json({
      success: true,
      user: userWithoutPassword,
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ غير متوقع',
      details: error.message,
    }, { status: 500 });
  }
}
