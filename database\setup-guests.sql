-- إنشاء جدول الضيوف
CREATE TABLE IF NOT EXISTS public.guests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invitation_id UUID NOT NULL REFERENCES public.invitations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    status VARCHAR(50) DEFAULT 'pending',  -- pending, confirmed, declined
    number_of_companions INTEGER DEFAULT 0,
    confirmation_token UUID DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- تفعيل RLS على جدول الضيوف
ALTER TABLE public.guests ENABLE ROW LEVEL SECURITY;

-- منح الصلاحيات الأساسية
GRANT ALL ON public.guests TO authenticated;
GRANT SELECT, UPDATE ON public.guests TO anon;  -- السماح للضيوف غير المسجلين بتحديث حالة الحضور

-- سياسات جدول الضيوف

-- السماح بعرض الضيوف لصاحب الدعوة والمشرفين
CREATE POLICY "View guests policy" ON public.guests
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.invitations i
            WHERE i.id = invitation_id
            AND (
                i.user_id = auth.uid() -- صاحب الدعوة
                OR EXISTS ( -- أو مشرف
                    SELECT 1 FROM public.users
                    WHERE users.id = auth.uid() AND users.role = 'admin'
                )
            )
        )
    );

-- السماح للضيف بعرض بياناته باستخدام رمز التأكيد
CREATE POLICY "Guests can view their own data" ON public.guests
    FOR SELECT TO anon
    USING (confirmation_token::text = current_setting('app.confirmation_token', true)::text);

-- السماح للضيف بتحديث حالة الحضور باستخدام رمز التأكيد
CREATE POLICY "Guests can update their status" ON public.guests
    FOR UPDATE TO anon
    USING (confirmation_token::text = current_setting('app.confirmation_token', true)::text)
    WITH CHECK (
        -- السماح فقط بتحديث حالة الحضور وعدد المرافقين
        (
            status IN ('confirmed', 'declined')
            AND number_of_companions >= 0
            AND number_of_companions <= 10
        )
    );

-- السماح لصاحب الدعوة والمشرفين بإدارة الضيوف
CREATE POLICY "Manage guests policy" ON public.guests
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.invitations i
            WHERE i.id = invitation_id
            AND (
                i.user_id = auth.uid() -- صاحب الدعوة
                OR EXISTS ( -- أو مشرف
                    SELECT 1 FROM public.users
                    WHERE users.id = auth.uid() AND users.role = 'admin'
                )
            )
        )
    );

-- إنشاء function لتحديث حالة الضيف باستخدام رمز التأكيد
CREATE OR REPLACE FUNCTION public.update_guest_status(
    _confirmation_token UUID,
    _status VARCHAR(50),
    _number_of_companions INTEGER DEFAULT 0
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- تعيين رمز التأكيد في إعدادات التطبيق
    PERFORM set_config('app.confirmation_token', _confirmation_token::text, true);
    
    -- تحديث حالة الضيف
    UPDATE public.guests
    SET 
        status = _status,
        number_of_companions = _number_of_companions,
        updated_at = CURRENT_TIMESTAMP
    WHERE confirmation_token = _confirmation_token;

    RETURN FOUND;
END;
$$;
