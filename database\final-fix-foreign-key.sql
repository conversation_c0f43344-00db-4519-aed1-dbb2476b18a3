-- الحل النهائي لمشكلة foreign key constraint
-- هذا السكريبت سيحل المشكلة نهائياً

-- 1. حذف جميع foreign key constraints الحالية
ALTER TABLE public.invitations DROP CONSTRAINT IF EXISTS fk_user;
ALTER TABLE public.invitations DROP CONSTRAINT IF EXISTS invitations_user_id_fkey;
ALTER TABLE public.invitations DROP CONSTRAINT IF EXISTS fk_invitations_user_id;

-- 2. حذف جميع الجداول وإعادة إنشائها بالهيكل الصحيح
DROP TABLE IF EXISTS public.guests CASCADE;
DROP TABLE IF EXISTS public.notifications CASCADE;
DROP TABLE IF EXISTS public.invitations CASCADE;

-- 3. التأكد من وجود جدول users مع الهيكل الصحيح
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    phone TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    role TEXT DEFAULT 'subscriber',
    is_active BOOLEAN DEFAULT false,
    activated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 4. إنشاء جدول invitations مع foreign key صحيح
CREATE TABLE public.invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_date TIMESTAMP WITH TIME ZONE,
    location TEXT,
    status VARCHAR(50) DEFAULT 'draft',
    template_id UUID,
    additional_data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_invitations_user_id FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
);

-- 5. إنشاء جدول notifications
CREATE TABLE public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'general',
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_notifications_user_id FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
);

-- 6. إنشاء جدول guests
CREATE TABLE public.guests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invitation_id UUID NOT NULL,
    name VARCHAR(255),
    phone VARCHAR(20),
    status VARCHAR(50) DEFAULT 'pending',
    qr_code TEXT,
    notes TEXT,
    number_of_companions INTEGER DEFAULT 0,
    attended_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_guests_invitation_id FOREIGN KEY (invitation_id) REFERENCES public.invitations(id) ON DELETE CASCADE
);

-- 7. تعطيل RLS مؤقتاً للسماح بجميع العمليات
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.invitations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.guests DISABLE ROW LEVEL SECURITY;

-- 8. منح الصلاحيات اللازمة
GRANT ALL ON public.users TO authenticated, anon, service_role;
GRANT ALL ON public.invitations TO authenticated, anon, service_role;
GRANT ALL ON public.notifications TO authenticated, anon, service_role;
GRANT ALL ON public.guests TO authenticated, anon, service_role;

-- 9. إضافة trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إضافة triggers للجداول
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_invitations_updated_at ON public.invitations;
CREATE TRIGGER update_invitations_updated_at
    BEFORE UPDATE ON public.invitations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 10. إضافة مستخدم أدمن افتراضي
INSERT INTO public.users (name, phone, password, role, is_active) 
VALUES ('مدير النظام', '0500000000', '123456', 'admin', true)
ON CONFLICT (phone) DO UPDATE SET 
    role = 'admin',
    is_active = true;

-- إضافة مستخدم تجريبي
INSERT INTO public.users (name, phone, password, role, is_active) 
VALUES ('مستخدم تجريبي', '0500000001', '123456', 'subscriber', true)
ON CONFLICT (phone) DO NOTHING;

-- 11. إضافة extension للـ UUID إذا لم يكن موجوداً
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 12. التحقق من النتائج
SELECT 
    'Database setup completed successfully' as status,
    (SELECT COUNT(*) FROM public.users) as users_count,
    (SELECT COUNT(*) FROM public.invitations) as invitations_count;

-- 13. عرض foreign keys للتأكد
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema='public'
    AND tc.table_name IN ('invitations', 'notifications', 'guests');
