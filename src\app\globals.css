@tailwind base;
@tailwind components;
@tailwind utilities;

/* تخصيص الأنماط الإضافية */
@layer components {
  .card {
    @apply bg-white dark:bg-neutral-800 rounded-lg shadow-md p-4 border border-neutral-200 dark:border-neutral-700;
  }
  
  .input {
    @apply block w-full px-4 py-2 text-neutral-900 dark:text-white bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }
  
  .label {
    @apply block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1;
  }
  
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-neutral-600 hover:bg-neutral-700 text-white focus:ring-neutral-500;
  }
  
  .btn-accent {
    @apply bg-indigo-600 hover:bg-indigo-700 text-white focus:ring-indigo-500;
  }
  
  .btn-outline {
    @apply bg-transparent border border-neutral-300 dark:border-neutral-700 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-800 focus:ring-neutral-500;
  }
}

/* تخصيص الاتجاه للغة العربية */
html {
  direction: rtl;
}