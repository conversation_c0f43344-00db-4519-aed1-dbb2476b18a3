// pages/api/attendance.js
import { supabase } from '@/lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { qrCode } = req.body;

    if (!qrCode) {
      return res.status(400).json({ error: 'QR code is required' });
    }

    // البحث عن المدعو باستخدام رمز QR
    const { data: attendee, error: findError } = await supabase
      .from('attendance')
      .select('*, invitations(*)')
      .eq('qr_code', qrCode)
      .single();
    
    if (findError || !attendee) {
      return res.status(404).json({ error: 'Invalid QR code' });
    }

    // تحديث حالة الحضور
    const { data, error } = await supabase
      .from('attendance')
      .update({ 
        attended: true,
        attended_at: new Date().toISOString()
      })
      .eq('id', attendee.id)
      .select();
    
    if (error) {
      return res.status(500).json({ error: error.message });
    }

    return res.status(200).json({ 
      success: true, 
      data: {
        ...data[0],
        invitation: attendee.invitations
      }
    });
  } catch (error) {
    console.error('Error marking attendance:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}