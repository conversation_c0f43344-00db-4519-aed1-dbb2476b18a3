import { NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function POST(request) {
  try {
    const { name, phone, password } = await request.json();
    if (!name || !phone || !password) {
      return NextResponse.json(
        { success: false, error: "جميع الحقول مطلوبة" },
        { status: 400 }
      );
    }
    const supabase = createRouteHandlerClient({ cookies });
    // تحقق إذا كان رقم الهاتف مستخدم مسبقًا
    const { data: existing, error: findError } = await supabase
      .from("users")
      .select("id")
      .eq("phone", phone)
      .maybeSingle();
    if (existing) {
      return NextResponse.json(
        { success: false, error: "رقم الهاتف مستخدم بالفعل" },
        { status: 409 }
      );
    }
    // إضافة المستخدم بكلمة مرور نصية عادية
    const { data, error } = await supabase
      .from("users")
      .insert({ name, phone, password, is_active: false, role: "subscriber" })
      .select()
      .maybeSingle();
    if (error) throw error;

    // إضافة إشعار تفعيل جديد للأدمن
    await supabase.from("notifications").insert({
      user_id: data.id,
      message: `طلب تفعيل حساب جديد باسم ${name}`,
      type: "activation_request",
    });

    return NextResponse.json({
      success: true,
      user: { id: data.id, name: data.name, phone: data.phone },
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: error.message || "حدث خطأ أثناء التسجيل" },
      { status: 500 }
    );
  }
}
