-- حذ<PERSON> الجداول الحالية
DROP TABLE IF EXISTS public.invitations CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- إن<PERSON>اء جدول المستخدمين
CREATE TABLE public.users (
    id UUID PRIMARY KEY,
    name TEXT,
    phone TEXT UNIQUE,
    password TEXT NOT NULL,  -- إضا<PERSON>ة حقل كلمة المرور
    role TEXT DEFAULT 'subscriber',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إدخال بيانات المستخدمين للاختبار
INSERT INTO public.users (id, name, phone, password, role) VALUES 
('46cb13f8-3c35-490f-bf56-88f0ccd0aaef', 'احمد فؤاد', '0500000002', '123', 'subscriber'),
(gen_random_uuid(), 'مدير النظام', '0500000001', '123', 'admin')
ON CONFLICT (id) DO UPDATE 
SET name = EXCLUDED.name,
    phone = EXCLUDED.phone,
    password = EXCLUDED.password,
    role = EXCLUDED.role;

-- إنشاء جدول الدعوات
CREATE TABLE public.invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    event_date TIMESTAMP WITH TIME ZONE,
    location TEXT,
    description TEXT,
    status VARCHAR(50) DEFAULT 'draft',
    additional_data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
);

-- تفعيل RLS على الجداول
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invitations ENABLE ROW LEVEL SECURITY;

-- منح الصلاحيات الأساسية
GRANT ALL ON public.users TO authenticated;
GRANT ALL ON public.invitations TO authenticated;
GRANT ALL ON public.users TO anon;  -- السماح للمستخدمين غير المسجلين بالوصول لعملية تسجيل الدخول

-- سياسات جدول المستخدمين
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Enable read access for login" ON public.users;

-- السماح بقراءة جدول المستخدمين للتحقق من تسجيل الدخول
CREATE POLICY "Enable read access for login" ON public.users
    FOR SELECT TO anon
    USING (true);

-- السماح للمستخدمين برؤية بياناتهم
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT TO authenticated
    USING (auth.uid() = id OR role = 'admin');

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE TO authenticated
    USING (auth.uid() = id);

-- سياسات جدول الدعوات
CREATE POLICY "Users can view invitations" ON public.invitations
    FOR SELECT TO authenticated
    USING (
        user_id = auth.uid() -- يمكن للمستخدم رؤية دعواته
        OR EXISTS ( -- أو إذا كان مشرف
            SELECT 1 FROM public.users
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    );

CREATE POLICY "Users can create invitations" ON public.invitations
    FOR INSERT TO authenticated
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their invitations" ON public.invitations
    FOR UPDATE TO authenticated
    USING (
        user_id = auth.uid() -- يمكن للمستخدم تحديث دعواته
        OR EXISTS ( -- أو إذا كان مشرف
            SELECT 1 FROM public.users
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    );

CREATE POLICY "Users can delete their invitations" ON public.invitations
    FOR DELETE TO authenticated
    USING (
        user_id = auth.uid() -- يمكن للمستخدم حذف دعواته
        OR EXISTS ( -- أو إذا كان مشرف
            SELECT 1 FROM public.users
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    );

-- تعطيل RLS مؤقتاً للسماح بجميع العمليات
ALTER TABLE public.invitations DISABLE ROW LEVEL SECURITY;

-- منح الصلاحيات للمستخدمين
GRANT ALL ON public.invitations TO authenticated;
GRANT ALL ON public.invitations TO anon;
GRANT ALL ON public.invitations TO service_role;

-- إضافة trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_invitations_updated_at
    BEFORE UPDATE ON public.invitations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
