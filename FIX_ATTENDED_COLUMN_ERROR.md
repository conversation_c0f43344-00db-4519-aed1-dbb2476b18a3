# حل خطأ العمو<PERSON> attended المفقود

## المشكلة:
```
Error updating attendance: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'attended' column of 'guests' in the schema cache"}
```

## السبب:
العمود `attended` غير موجود في جدول `guests` في قاعدة البيانات.

## الحل السريع:

### الخطوة 1: تشغيل SQL لإضافة العمود
في Supabase SQL Editor، قم بتشغيل:

```sql
-- إ<PERSON><PERSON><PERSON><PERSON> العمود attended
ALTER TABLE public.guests ADD COLUMN IF NOT EXISTS attended BOOLEAN DEFAULT false;

-- إضا<PERSON><PERSON> العمود attended_at
ALTER TABLE public.guests ADD COLUMN IF NOT EXISTS attended_at TIMESTAMP WITH TIME ZONE;

-- إ<PERSON><PERSON>ة تحميل schema cache
NOTIFY pgrst, 'reload schema';
```

### الخطوة 2: تشغيل الملف الكامل (اختياري)
```bash
# في Supabase SQL Editor
\i database/add-attended-column.sql
```

### الخطوة 3: تشغيل ملف توضيح الحالات (اختياري)
```bash
# في Supabase SQL Editor  
\i database/clarify-guest-states.sql
```

## التحسينات المضافة:

### 1. توضيح الفرق بين الحالات:
- **رد الضيف** (`status`): يحدده الضيف نفسه
  - `pending` = لم يرد بعد
  - `accepted` = قبل الدعوة  
  - `declined` = اعتذر
  
- **الحضور الفعلي** (`attended`): يحدده المستخدم (صاحب الدعوة)
  - `true` = حضر فعلياً
  - `false` = لم يحضر

### 2. تحديث واجهة المستخدم:
- ✅ عناوين أعمدة أوضح في جدول المدعوين
- ✅ توضيح في أعلى الصفحة يشرح الفرق بين الحالات
- ✅ نصوص محدثة للحالات

### 3. قاعدة البيانات:
- ✅ إضافة العمود `attended` (boolean)
- ✅ إضافة العمود `attended_at` (timestamp)
- ✅ إضافة تعليقات توضيحية للأعمدة
- ✅ إنشاء دوال مساعدة للإحصائيات
- ✅ إنشاء trigger لتحديث التوقيت تلقائياً

## الاستخدام بعد الإصلاح:

### للمستخدم (صاحب الدعوة):
1. يرى رد الضيف على الدعوة (قبول/رفض/لم يرد)
2. يمكنه تحديد من حضر فعلياً بالضغط على زر الحضور
3. يحصل على إحصائيات دقيقة للحضور الفعلي

### للضيف:
1. يرد على الدعوة (قبول/رفض) من خلال رابط الدعوة
2. لا يمكنه تغيير حالة الحضور الفعلي

## مثال على الحالات:
- ضيف قبل الدعوة وحضر فعلياً: `status = 'accepted'` و `attended = true`
- ضيف قبل الدعوة لكن لم يحضر: `status = 'accepted'` و `attended = false`  
- ضيف اعتذر: `status = 'declined'` و `attended = false`
- ضيف لم يرد ولم يحضر: `status = 'pending'` و `attended = false`

## التحقق من نجاح الإصلاح:
1. تسجيل الدخول إلى النظام
2. الذهاب إلى صفحة المدعوين لأي دعوة
3. الضغط على زر "حضر/لم يحضر" - يجب أن يعمل بدون أخطاء
4. التحقق من تحديث الحالة في الجدول

## في حالة استمرار المشكلة:
1. تأكد من تشغيل SQL في Supabase
2. تحقق من console المتصفح للأخطاء
3. تأكد من إعادة تحميل الصفحة بعد تشغيل SQL
4. تحقق من أن العمود `attended` موجود في جدول `guests`
