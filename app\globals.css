@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 245, 245, 245;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
}

/* تخصيص الخطوط والاتجاه للغة العربية */
html[dir="rtl"] {
  text-align: right;
}

/* تخصيص الأزرار */
.btn-primary {
  @apply bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors;
}

.btn-secondary {
  @apply bg-white text-indigo-600 border border-indigo-600 py-2 px-4 rounded-md hover:bg-indigo-50 transition-colors;
}

/* تأثيرات الانتقال */
.fade-in {
  @apply transition-opacity duration-500;
}

.slide-up {
  @apply transition-transform duration-500;
}

/* تخصيص الجداول */
.table-custom {
  @apply min-w-full divide-y divide-gray-200;
}

.table-custom th {
  @apply px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-custom td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-500;
}

/* تخصيص البطاقات */
.card {
  @apply bg-white rounded-lg shadow-md p-6;
}

.card-hover {
  @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
}

/* تخصيص النماذج */
.form-input {
  @apply mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

/* تخصيص الرسائل */
.alert-success {
  @apply p-4 mb-4 bg-green-100 text-green-700 rounded-md;
}

.alert-error {
  @apply p-4 mb-4 bg-red-100 text-red-700 rounded-md;
}

.alert-warning {
  @apply p-4 mb-4 bg-yellow-100 text-yellow-700 rounded-md;
}

.alert-info {
  @apply p-4 mb-4 bg-blue-100 text-blue-700 rounded-md;
}
