# نظام إدارة الاشتراكات - Subscription Management System

## نظرة عامة
تم تطوير نظام شامل لإدارة الاشتراكات في نظام الدعوات العربي، يتضمن إدارة الاشتراكات، حساب الإيرادات، والتحليلات المتقدمة.

## الميزات الجديدة

### 1. إدارة الاشتراكات (`/admin/subscriptions`)
- **عرض جميع الاشتراكات**: جدول شامل بجميع اشتراكات المستخدمين
- **إحصائيات مباشرة**: إجمالي الاشتراكات، النشطة، الدخل الشهري، متوسط الرسوم
- **البحث والتصفية**: بحث بالاسم أو البريد الإلكتروني، تصفية حسب الحالة
- **إدارة الاشتراكات**: حذف الاشتراكات غير المرغوب فيها
- **ألوان تمييزية**: 
  - مميز (Premium): بنفسجي
  - قياسي (Standard): أزرق  
  - أساسي (Basic): رمادي

### 2. إضافة اشتراك جديد (`/admin/subscriptions/add`)
- **اختيار المستخدم**: قائمة منسدلة بجميع المشتركين
- **أنواع الخطط**: أساسي (9.99$)، قياسي (19.99$)، مميز (29.99$)
- **تحديث تلقائي للرسوم**: عند تغيير نوع الخطة
- **تواريخ مرنة**: تاريخ البداية والانتهاء
- **طرق دفع متعددة**: بطاقة ائتمان، تحويل بنكي، PayPal، نقداً، إدخال يدوي
- **التحقق من التكرار**: منع إنشاء اشتراكات متعددة للمستخدم الواحد
- **إنشاء دفعة أولى**: تلقائياً عند إنشاء اشتراك نشط

### 3. التحليلات المتقدمة (`/admin/analytics`)
- **إحصائيات شاملة**: المستخدمين، الدعوات، الضيوف، الإيرادات
- **رسوم بيانية تفاعلية**:
  - نمو المستخدمين (12 شهر)
  - اتجاهات الدعوات
  - معدلات قبول الضيوف
  - تحليل الإيرادات الشهرية
- **أفضل المؤدين**: ترتيب المستخدمين حسب النشاط
- **صحة النظام**: معدلات الاستجابة، التحويل، التسرب
- **تصدير البيانات**: إمكانية تصدير التحليلات بصيغة JSON

### 4. تحديث صفحة المشتركين (`/admin/subscribers`)
- **عمود الاشتراك الجديد**: عرض معلومات الاشتراك لكل مستخدم
- **عرض متعدد الاشتراكات**: دعم المستخدمين الذين لديهم أكثر من اشتراك
- **حالة "لا يوجد اشتراك"**: للمستخدمين بدون اشتراكات

## قاعدة البيانات

### الجداول الجديدة

#### جدول `subscriptions`
```sql
- id: UUID (Primary Key)
- user_id: UUID (Foreign Key → users.id)
- plan_name: VARCHAR(100) (basic, standard, premium)
- monthly_fee: DECIMAL(10,2)
- currency: VARCHAR(3) (USD)
- start_date: DATE
- end_date: DATE (اختياري)
- status: VARCHAR(20) (active, inactive, cancelled, expired)
- auto_renew: BOOLEAN
- payment_method: VARCHAR(50)
- notes: TEXT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### جدول `subscription_payments`
```sql
- id: UUID (Primary Key)
- subscription_id: UUID (Foreign Key → subscriptions.id)
- amount: DECIMAL(10,2)
- currency: VARCHAR(3)
- payment_date: DATE
- payment_method: VARCHAR(50)
- transaction_id: VARCHAR(255)
- status: VARCHAR(20) (pending, completed, failed, refunded)
- notes: TEXT
- created_at: TIMESTAMP
```

### الدوال المساعدة

#### `get_subscription_stats()`
```sql
إرجاع:
- total_subscriptions: INTEGER
- active_subscriptions: INTEGER
- inactive_subscriptions: INTEGER
- monthly_revenue: DECIMAL(10,2)
- average_monthly_fee: DECIMAL(10,2)
- most_popular_plan: TEXT
```

#### `calculate_monthly_revenue(target_month)`
```sql
إرجاع:
- month_year: TEXT
- total_revenue: DECIMAL(10,2)
- payment_count: INTEGER
- active_subscriptions: INTEGER
- average_payment: DECIMAL(10,2)
```

## البيانات التجريبية
- **4 اشتراكات نشطة** موزعة على المستخدمين الحاليين
- **23 دفعة مكتملة** بإجمالي 319.77$
- **دخل شهري حالي**: 59.96$
- **الخطة الأكثر شعبية**: قياسي (Standard)

## الملفات المضافة/المحدثة

### ملفات جديدة:
- `app/admin/subscriptions/page.js` - صفحة إدارة الاشتراكات
- `app/admin/subscriptions/add/page.js` - صفحة إضافة اشتراك جديد
- `app/admin/analytics/page.js` - صفحة التحليلات المتقدمة

### ملفات محدثة:
- `app/admin/subscribers/page.js` - إضافة عمود الاشتراك
- `app/admin/dashboard/page.js` - تحديث الروابط للصفحات الجديدة

### قاعدة البيانات:
- إنشاء جداول الاشتراكات والمدفوعات
- إضافة الدوال المساعدة
- إدراج بيانات تجريبية
- إنشاء الفهارس والمحفزات

## كيفية الاستخدام

### للمدير:
1. **عرض الاشتراكات**: انتقل إلى `/admin/subscriptions`
2. **إضافة اشتراك جديد**: اضغط "إضافة اشتراك" أو انتقل إلى `/admin/subscriptions/add`
3. **عرض التحليلات**: انتقل إلى `/admin/analytics`
4. **إدارة المشتركين**: انتقل إلى `/admin/subscribers` لرؤية معلومات الاشتراك

### العمليات الأساسية:
- **إنشاء اشتراك**: اختر المستخدم، نوع الخطة، املأ التفاصيل
- **حذف اشتراك**: اضغط زر الحذف في جدول الاشتراكات
- **عرض الإحصائيات**: تحديث تلقائي للبيانات
- **تصدير التحليلات**: اضغط زر "تصدير" في صفحة التحليلات

## الأمان والصلاحيات
- **صلاحيات المدير فقط**: جميع صفحات الاشتراكات محمية
- **التحقق من المستخدم**: فحص الدور والحالة النشطة
- **منع التكرار**: فحص الاشتراكات الموجودة قبل الإنشاء
- **حذف آمن**: تأكيد قبل حذف الاشتراكات

## التحسينات المستقبلية المقترحة
1. **تعديل الاشتراكات**: إضافة وظيفة تعديل الاشتراكات الموجودة
2. **تجديد تلقائي**: نظام تجديد الاشتراكات تلقائياً
3. **تنبيهات انتهاء الصلاحية**: إشعارات قبل انتهاء الاشتراكات
4. **تقارير PDF**: تصدير التقارير بصيغة PDF
5. **دفع إلكتروني**: ربط مع بوابات الدفع الإلكتروني
6. **خصومات وعروض**: نظام إدارة الخصومات والعروض الترويجية

## الدعم الفني
- جميع الأخطاء مسجلة في وحدة التحكم
- رسائل تأكيد للعمليات الناجحة
- رسائل خطأ واضحة للمستخدم
- تحديث تلقائي للبيانات بعد العمليات

---

**تم التطوير**: نظام شامل لإدارة الاشتراكات مع واجهة عربية حديثة وتحليلات متقدمة.
