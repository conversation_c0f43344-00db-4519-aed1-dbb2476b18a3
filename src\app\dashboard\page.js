import { supabase } from '@/lib/supabase';
import Link from 'next/link';

export default async function SubscriberDashboard({ params }) {
  // في الواقع، ستحتاج إلى الحصول على معرف المستخدم من الجلسة
  // هذا مجرد مثال
  const userId = '123'; // سيتم استبداله بمعرف المستخدم الفعلي من الجلسة
  
  const { data: invitations } = await supabase
    .from('invitations')
    .select('*')
    .eq('user_id', userId);
  
  const { data: guests } = await supabase
    .from('guests')
    .select('*')
    .in('invitation_id', invitations?.map(inv => inv.id) || []);
  
  const totalGuests = guests?.length || 0;
  const acceptedGuests = guests?.filter(guest => guest.status === 'accepted').length || 0;
  const attendedGuests = guests?.filter(guest => guest.attended).length || 0;

  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold mb-8">لوحة التحكم</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-2">الدعوات</h2>
          <p className="text-3xl font-bold">{invitations?.length || 0}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-2">المدعوين</h2>
          <p className="text-3xl font-bold">{totalGuests}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-2">الحضور المؤكد</h2>
          <p className="text-3xl font-bold">{attendedGuests} / {acceptedGuests}</p>
        </div>
      </div>
      
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">الدعوات</h2>
          <Link 
            href="/dashboard/invitations/create" 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            إنشاء دعوة جديدة
          </Link>
        </div>
        
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العنوان</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المكان</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المدعوين</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {invitations?.map((invitation) => {
                const invitationGuests = guests?.filter(g => g.invitation_id === invitation.id) || [];
                const accepted = invitationGuests.filter(g => g.status === 'accepted').length;
                
                return (
                  <tr key={invitation.id}>
                    <td className="px-6 py-4 whitespace-nowrap">{invitation.title}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {new Date(invitation.date).toLocaleDateString('ar-EG')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">{invitation.location}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {accepted} / {invitationGuests.length}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link 
                        href={`/dashboard/invitations/${invitation.id}`}
                        className="text-blue-600 hover:text-blue-900 ml-4"
                      >
                        عرض
                      </Link>
                      <Link 
                        href={`/dashboard/invitations/${invitation.id}/guests`}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        المدعوين
                      </Link>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}